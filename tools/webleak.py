# Encoding: utf-8
from typing import Optional
from utils.exec import run_executable as exec

def run_webleak(
    target: Optional[str] = None,
) -> str:
    """Run webleak to detect web application leak vulnerabilities.

    Args:
        target: Single target URL to scan

    Returns:
        str: webleak scan results
    """
    # 构建基本命令
    cmd = ["webleak", "-u", target, "-t", "all", "-p", "./dicts/pocs/", "-q"]
    
    return exec(cmd)
