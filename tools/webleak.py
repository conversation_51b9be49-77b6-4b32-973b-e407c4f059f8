# Encoding: utf-8
from typing import Optional
from utils.exec import run_executable as exec

def run_webleak(
    target: Optional[str] = None,
) -> str:
    """运行 WebLeak 工具检测 Web 应用泄露漏洞

    使用 WebLeak 工具对目标网站进行信息泄露和敏感数据暴露检测。

    参数:
        target: 目标网站 URL

    返回:
        str: WebLeak 扫描结果
    """
    # 构建基本命令
    cmd = ["webleak", "-u", target, "-t", "all", "-p", "./dicts/pocs/", "-q"]
    
    return exec(cmd)
