# Encdoding: utf-8
from utils.exec import run_executable as exec  # 使用绝对导入

def run_fingerprintx(
    ip: str,
    port: str,
) -> str:
    """运行 FingerprintX 工具进行服务指纹识别

    使用 FingerprintX 工具对指定 IP 地址和端口进行服务指纹识别，检测运行的服务类型和版本。

    参数:
        ip: 目标 IP 地址
        port: 目标端口号

    返回:
        str: JSON 格式的服务指纹识别结果
    """
    # 构建目标地址
    target = f"{ip}:{port}"

    # 构建命令列表
    cmd = [
        'fingerprintx',
        '-t', target,
        '--json'
    ]

    return exec(cmd)
