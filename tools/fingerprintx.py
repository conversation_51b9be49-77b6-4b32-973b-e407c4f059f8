# Encdoding: utf-8
from utils.exec import run_executable as exec  # 使用绝对导入

def run_fingerprintx(
    ip: str,
    port: str,
) -> str:
    """Identify the service fingerprint for a given IP and port

    Args:
        ip: Target IP address
        port: Target port number

    Returns:
        str: JSON formatted fingerprint output
    """
    # 构建目标地址
    target = f"{ip}:{port}"

    # 构建命令列表
    cmd = [
        'fingerprintx',
        '-t', target,
        '--json'
    ]

    return exec(cmd)
