from typing import Optional
from utils.exec import run_executable as exec

def run_tlsx(host: str, port: Optional[int] = 443) -> str:
    """运行 TLSX 工具进行 TLS 配置分析

    使用 TLSX 工具对目标主机的 TLS/SSL 配置进行深度分析和安全评估。

    参数:
        host: 目标主机名或 IP 地址
        port: 目标端口，默认为 443

    返回:
        str: JSON 格式的 TLS 分析结果
    """

    cmd = ["tlsx", "-host", host, "-port", str(port), "-json"]
    return exec(cmd)
