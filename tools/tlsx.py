from typing import Optional
from utils.exec import run_executable as exec

def run_tlsx(host: str, port: Optional[int] = 443) -> str:
    """
    Run tlsx to analyze TLS configurations.

    Args:
        host: Target hostname or IP address
        port: Target port (default: 443)

    Returns:
        str: JSON string containing TLS analysis results
    """

    cmd = ["tlsx", "-host", host, "-port", str(port), "-json"]
    return exec(cmd)
