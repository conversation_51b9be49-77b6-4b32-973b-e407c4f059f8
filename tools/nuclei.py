# Encdoding: utf-8
import logging
from pathlib import Path
from typing import Optional
from utils.exec import run_executable as exec  # 使用绝对导入

logger = logging.getLogger(__name__)

def _select_nuclei_workflows(service: Optional[str]) -> Optional[str]:
    """
    智能选择 Nuclei 工作流

    Args:
        service: 服务类型

    Returns:
        工作流文件名或 None（使用默认扫描）
    """
    if not service:
        return None

    # 服务到工作流的映射 - 基于 /dicts/nuclei-templates/workflows 下的所有工作流
    service_workflow_map = {
        # CMS 系统
        '74cms': '74cms-workflow.yaml',
        'b2evolution': 'b2evolution-workflow.yaml',
        'bitrix': 'bitrix-workflow.yaml',
        'concrete': 'concrete-workflow.yaml',
        'dedecms': 'dedecms-workflow.yaml',
        'dotnetnuke': 'dotnetnuke-workflow.yaml',
        'drupal': 'drupal-workflow.yaml',
        'duomicms': 'duomicms-workflow.yaml',
        'dynamicweb': 'dynamicweb-workflow.yaml',
        'episerver': 'episerver-workflow.yaml',
        'feifeicms': 'feifeicms-workflow.yaml',
        'grav': 'grav-workflow.yaml',
        'joomla': 'joomla-workflow.yaml',
        'kentico': 'kentico-workflow.yaml',
        'kindeditor': 'kindeditor-workflow.yaml',
        'liferay': 'liferay-workflow.yaml',
        'magento': 'magento-workflow.yaml',
        'metinfo': 'metinfo-workflow.yaml',
        'moodle': 'moodle-workflow.yaml',
        'movable': 'movable-workflow.yaml',
        'nette': 'nette-workflow.yaml',
        'opensns': 'opensns-workflow.yaml',
        'oscommerce': 'oscommerce-workflow.yaml',
        'phpwiki': 'phpwiki-workflow.yaml',
        'rosariosis': 'rosariosis-workflow.yaml',
        'sharepoint': 'sharepoint-workflow.yaml',
        'shopxo': 'shopxo-workflow.yaml',
        'sitecore': 'sitecore-workflow.yaml',
        'subrion': 'subrion-workflow.yaml',
        'symfony': 'symfony-workflow.yaml',
        'tapestry': 'tapestry-workflow.yaml',
        'thinkadmin': 'thinkadmin-workflow.yaml',
        'thinkcmf': 'thinkcmf-workflow.yaml',
        'thinkphp': 'thinkphp-workflow.yaml',
        'tikiwiki': 'tikiwiki-workflow.yaml',
        'tpshop': 'tpshop-workflow.yaml',
        'umbraco': 'umbraco-workflow.yaml',
        'vbulletin': 'vbulletin-workflow.yaml',
        'wordpress': 'wordpress-workflow.yaml',
        'wuzhicms': 'wuzhicms-workflow.yaml',
        'xdcms': 'xdcms-workflow.yaml',
        'xiuno': 'xiuno-workflow.yaml',
        'yii': 'yii-workflow.yaml',
        'zcms': 'zcms-workflow.yaml',
        'zzzcms': 'zzzcms-workflow.yaml',

        # Web 服务器和应用服务器
        'apache': 'apache-workflow.yaml',
        'nginx': 'apache-workflow.yaml',  # 使用 apache workflow 作为通用 web 服务器
        'iis': 'apache-workflow.yaml',
        'tomcat': 'tomcat-workflow.yaml',
        'jetty': 'jetty-workflow.yaml',
        'weblogic': 'weblogic-workflow.yaml',
        'jboss': 'jboss-workflow.yaml',
        'cherokee': 'cherokee-workflow.yaml',
        'coldfusion': 'coldfusion-workflow.yaml',
        'lucee': 'lucee-workflow.yaml',
        'ofbiz': 'ofbiz-workflow.yaml',
        'springboot': 'springboot-workflow.yaml',
        'traefik': 'traefik-workflow.yaml',

        # 数据库
        'mysql': 'mysql-workflow.yaml',
        'postgresql': 'pgsql-workflow.yaml',
        'pgsql': 'pgsql-workflow.yaml',
        'mongodb': 'mongodb-workflow.yaml',
        'redis': 'redis-workflow.yaml',
        'sequoiadb': 'sequoiadb-workflow.yaml',

        # 网络服务
        'ssh': 'ssh-workflow.yaml',
        'rdp': 'rdp-workflow.yaml',
        'smb': 'smb-workflow.yaml',
        'smtp': 'smtp-workflow.yaml',
        'pop3': 'pop3-workflow.yaml',
        'ldap': 'ldap-workflow.yaml',

        # 开发和管理工具
        'jenkins': 'jenkins-workflow.yaml',
        'gitlab': 'gitlab-workflow.yaml',
        'gitlist': 'gitlist-workflow.yaml',
        'gogs': 'gogs-workflow.yaml',
        'jira': 'jira-workflow.yaml',
        'confluence': 'confluence-workflow.yaml',
        'artifactory': 'artifactory-workflow.yaml',
        'gocd': 'gocd-workflow.yaml',
        'rancher': 'rancher-workflow.yaml',
        'harbor': 'harbor-workflow.yaml',
        'sonarqube': 'sonarqube-workflow.yaml',
        'xxljob': 'xxljob-workflow.yaml',
        'yapi': 'yapi-workflow.yaml',

        # 监控和分析工具
        'grafana': 'grafana-workflow.yaml',
        'kibana': 'kibana-workflow.yaml',
        'prometheus': 'prometheus-workflow.yaml',
        'splunk': 'splunk-workflow.yaml',
        'zabbix': 'zabbix-workflow.yaml',
        'cacti': 'cacti-workflow.yaml',
        'pandora': 'pandora-workflow.yaml',
        'prtg': 'prtg-workflow.yaml',
        'graphite': 'graphite-workflow.yaml',
        'skywalking': 'skywalking-workflow.yaml',
        'thruk': 'thruk-workflow.yaml',
        'voipmonitor': 'voipmonitor-workflow.yaml',

        # 数据库管理工具
        'phpmyadmin': 'phpmyadmin-workflow.yaml',
        'phppgadmin': 'phppgadmin-workflow.yaml',
        'adminer': 'adminer-workflow.yaml',

        # 系统管理工具
        'webmin': 'webmin-workflow.yaml',
        'cockpit': 'cockpit-workflow.yaml',
        'cpanel': 'apache-workflow.yaml',
        'plesk': 'apache-workflow.yaml',

        # 虚拟化和云平台
        'vmware': 'vmware-workflow.yaml',
        'citrix': 'citrix-workflow.yaml',
        'azure': 'azure-workflow.yaml',

        # 网络设备和安全设备
        'cisco': 'cisco-asa-workflow.yaml',
        'cisco-asa': 'cisco-asa-workflow.yaml',
        'cisco-meraki': 'cisco-meraki-workflow.yaml',
        'fortinet': 'fortinet-workflow.yaml',
        'pfsense': 'fortinet-workflow.yaml',
        'checkpoint': 'checkpoint-workflow.yaml',
        'pulsesecure': 'pulsesecure-workflow.yaml',
        'bigip': 'bigip-workflow.yaml',
        'netgear': 'netgear-workflow.yaml',
        'samsung-wlan-ap': 'samsung-wlan-ap-workflow.yaml',
        'zeroshell': 'zeroshell-workflow.yaml',

        # 企业应用
        'sap-netweaver': 'sap-netweaver-workflow.yaml',
        'oracle-peoplesoft': 'oracle-peoplesoft-workflow.yaml',
        'microsoft-exchange': 'microsoft-exchange-workflow.yaml',
        'lotus-domino': 'lotus-domino-workflow.yaml',
        'zimbra': 'zimbra-workflow.yaml',
        'keycloak': 'keycloak-workflow.yaml',
        'openam': 'openam-workflow.yaml',
        'apereo-cas': 'apereo-cas-workflow.yaml',

        # 消息队列和中间件
        'activemq': 'activemq-workflow.yaml',
        'rabbitmq': 'rabbitmq-workflow.yaml',
        'kong': 'kong-workflow.yaml',
        'apisix': 'apisix-workflow.yaml',
        'nacos': 'nacos-workflow.yaml',
        'saltstack': 'saltstack-workflow.yaml',

        # 流媒体和多媒体
        'emby': 'emby-workflow.yaml',
        'jellyfin': 'jellyfin-workflow.yaml',

        # 工作流和自动化
        'airflow': 'airflow-workflow.yaml',
        'azkaban': 'azkaban-workflow.yaml',
        'node-red': 'node-red-workflow.yaml',

        # 数据分析和BI
        'metabase': 'metabase-workflow.yaml',
        'microstrategy': 'microstrategy-workflow.yaml',
        'pentaho': 'pentaho-workflow.yaml',
        'finereport': 'finereport-workflow.yaml',
        'solr': 'solr-workflow.yaml',

        # 远程访问和VPN
        'guacamole': 'guacamole-workflow.yaml',
        'novnc': 'novnc-workflow.yaml',
        'gateone': 'gateone-workflow.yaml',
        'bomgar': 'bomgar-workflow.yaml',
        'thinfinity': 'thinfinity-workflow.yaml',
        'aviatrix': 'aviatrix-workflow.yaml',

        # 网络管理
        'lansweeper': 'lansweeper-workflow.yaml',
        'netsweeper': 'netsweeper-workflow.yaml',
        'rconfig': 'rconfig-workflow.yaml',
        'lanproxy': 'lanproxy-workflow.yaml',
        'artica-web-proxy': 'artica-web-proxy-workflow.yaml',

        # 硬件管理
        'dell-idrac': 'dell-idrac-workflow.yaml',
        'hikvision': 'hikvision-workflow.yaml',
        'dahua': 'dahua-workflow.yaml',
        'ricoh': 'ricoh-workflow.yaml',
        'terramaster': 'terramaster-workflow.yaml',

        # 邮件系统
        'axigen': 'axigen-workflow.yaml',
        'squirrelmail': 'squirrelmail-workflow.yaml',
        'avantfax': 'avantfax-workflow.yaml',

        # 学习管理系统
        'chamilo': 'chamilo-workflow.yaml',
        'mautic': 'mautic-workflow.yaml',
        'opensis': 'opensis-workflow.yaml',

        # 项目管理和协作
        'mantisbt': 'mantisbt-workflow.yaml',
        'phpcollab': 'phpcollab-workflow.yaml',
        'itop': 'itop-workflow.yaml',
        'glpi': 'glpi-workflow.yaml',
        'processmaker': 'processmaker-workflow.yaml',
        'sourcebans': 'sourcebans-workflow.yaml',

        # 其他专业工具
        'acrolinx': 'acrolinx-workflow.yaml',
        'ambari': 'ambari-workflow.yaml',
        'bullwark': 'bullwark-workflow.yaml',
        'centos': 'centos-workflow.yaml',
        'circarlife': 'circarlife-workflow.yaml',
        'cocoon': 'cocoon-workflow.yaml',
        'dolibarr': 'dolibarr-workflow.yaml',
        'emerge': 'emerge-workflow.yaml',
        'geowebserver': 'geowebserver-workflow.yaml',
        'gespage': 'gespage-workflow.yaml',
        'gophish': 'gophish-workflow.yaml',
        'gsoap': 'gsoap-workflow.yaml',
        'h3c-imc': 'h3c-imc-workflow.yaml',
        'igs': 'igs-workflow.yaml',
        'jeedom': 'jeedom-workflow.yaml',
        'kev': 'kev-workflow.yaml',
        'laravel': 'laravel-workflow.yaml',
        'magmi': 'magmi-workflow.yaml',
        'maian': 'maian-workflow.yaml',
        'micro-focus': 'micro-focus-workflow.yaml',
        'mida-eframework': 'mida-eframework-workflow.yaml',
        'mobileiron': 'mobileiron-workflow.yaml',
        'openemr': 'openemr-workflow.yaml',
        'pega': 'pega-workflow.yaml',
        'powercreator': 'powercreator-workflow.yaml',
        'qcubed': 'qcubed-workflow.yaml',
        'r-seenet': 'r-seenet-workflow.yaml',
        'rstudio': 'rstudio-workflow.yaml',
        'ruijie': 'ruijie-workflow.yaml',
        'sarg': 'sarg-workflow.yaml',
        'sco': 'sco-workflow.yaml',
        'sidekiq': 'sidekiq-workflow.yaml',
        'solarwinds-orion': 'solarwinds-orion-workflow.yaml',
        'sugarcrm': 'sugarcrm-workflow.yaml',
        'sysaid': 'sysaid-workflow.yaml',
        'tongda': 'tongda-workflow.yaml',
        'websvn': 'websvn-workflow.yaml',
        'worksite-takeover': 'worksite-takeover-workflow.yaml',
        'yonyou-nc': 'yonyou-nc-workflow.yaml',

        # 特殊工作流
        'default-application': 'default-application-workflow.yaml',
        'google-api-enumeration': 'google-api-enumeration-workflow.yaml',
    }

    # 标准化服务名称（转小写，去除常见后缀）
    normalized_service = service.lower().strip()
    normalized_service = normalized_service.replace('-server', '').replace('_server', '')
    normalized_service = normalized_service.replace('-service', '').replace('_service', '')

    # 查找匹配的工作流
    workflow = service_workflow_map.get(normalized_service)

    if workflow:
        return workflow
    else:
        return None


def run_nuclei(
    target: str,
    service: Optional[str] = None,
) -> str:
    """运行 Nuclei 安全扫描工具

    使用 Nuclei 工具对指定目标进行全面的安全漏洞扫描，支持智能工作流选择。

    参数:
        target: 目标 URL 或 IP 地址
        service: 服务类型，用于智能工作流选择

    返回:
        str: JSON 格式的扫描结果
    """


    # 获取模板路径
    template_path = Path(__file__).parent.parent / "dicts" / "nuclei-templates"
    if not template_path.exists():
        return f"Error: Templates not found at {template_path}"

    # 优化的 nuclei 参数构建
    cmd = [
        "nuclei", "-u", target,

        # 强制使用本地模板目录
        "-t", str(template_path),       # 指定模板目录，确保使用本地模板

        # 性能优化参数 - 平衡性能和检测准确性
        "-c", "50",                    # 并发数：适中的并发数，避免过快导致漏检
        "-rate-limit", "100",           # 速率限制：适中的速率，确保检测稳定性
        "-timeout", "5",                # 超时时间：增加到5秒，减少误报
        "-retries", "2",                # 重试次数：增加到2次，提高可靠性
        "-max-host-error", "10",        # 最大主机错误数：避免过早停止扫描

        # 输出和格式化参数
        "-no-color",                    # 禁用颜色输出
        "-silent",                      # 静默模式，减少噪音
        "-duc",                         # 禁用更新检查

        # 网络和连接优化
        "-follow-host-redirects",       # 跟随主机重定向，更安全的重定向策略
        "-max-redirects", "3",          # 最大重定向次数

        # 扫描范围控制 - 完全移除排除标签，确保最大化检测覆盖
        # 不使用 -exclude-tags，让所有模板都能执行，包括 configuration-listing 等

        # 模板和更新控制
        "-no-interactsh",               # 禁用interactsh以避免外部依赖
        # 移除 -disable-clustering 以保持默认聚类行为，确保模板正常执行

        # 输出详细信息
        "-stats",                       # 显示统计信息
    ]

    # 根据 service 智能选择工作流
    workflow = _select_nuclei_workflows(service)

    # 添加工作流或严重性参数
    if workflow:
        # 使用特定工作流，需要移除之前的 -t 参数，改用 -w 参数
        workflow_path = template_path / "workflows" / workflow
        if not workflow_path.exists():
            logger.warning(f"Workflow not found at {workflow_path}, using default scan with severity filter")
            # 工作流不存在时，保持使用模板目录但添加严重性过滤
            cmd.extend(["-s", "critical,high,medium"])
        else:
            # 工作流存在时，移除 -t 参数，使用 -w 参数
            # 找到并移除 -t 参数及其值
            if "-t" in cmd:
                t_index = cmd.index("-t")
                cmd.pop(t_index)  # 移除 -t
                cmd.pop(t_index)  # 移除模板路径值
            cmd.extend(["-w", str(workflow_path)])
    else:
        # 使用默认扫描策略，保持使用模板目录
        cmd.extend(["-s", "critical,high,medium"])

    return exec(cmd)
