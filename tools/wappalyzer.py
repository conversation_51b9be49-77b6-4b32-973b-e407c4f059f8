# Encdoding: utf-8
from utils.exec import run_executable as exec  # 使用绝对导入

def run_wappalyzer(
    url: str,
) -> str:
    """运行 Wappalyzer 工具进行 Web 技术栈识别

    使用 Wappalyzer 工具分析目标网站，识别其使用的技术栈、框架、库和服务。

    参数:
        url: 目标 URL 地址

    返回:
        str: JSON 格式的技术栈识别结果
    """
    # 构建命令列表
    cmd = [
        'wappalyzer',
        '-u', url,
        '-s', 
        '-f','json'
    ]

    return exec(cmd)
