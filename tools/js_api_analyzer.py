#!/usr/bin/env python3
"""
JS API Analyzer - MCP Tool
分析网站JS代码中的API路径并进行安全测试

功能：
1. 从网站JS代码中提取API路径
2. 自动测试API可达性
3. 检测未授权访问漏洞
4. 生成详细的安全报告
"""

import re
import json
import time
import asyncio
import aiohttp
import logging
from urllib.parse import urljoin, urlparse, parse_qs
from bs4 import BeautifulSoup
from concurrent.futures import ThreadPoolExecutor, as_completed
from dataclasses import dataclass, asdict
from typing import List, Dict, Set, Optional, Tuple
import argparse

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

@dataclass
class APIEndpoint:
    """API端点数据结构"""
    url: str
    method: str = "GET"
    source_file: str = ""
    line_number: int = 0
    context: str = ""
    original_url: str = ""  # 原始提取的URL
    path_type: str = ""     # 路径类型标识
    
@dataclass
class APITestResult:
    """API测试结果数据结构"""
    endpoint: APIEndpoint
    status_code: int = 0
    response_time: float = 0.0
    response_size: int = 0
    headers: Dict[str, str] = None
    response_body: str = ""
    is_accessible: bool = False
    is_unauthorized: bool = False
    has_sensitive_data: bool = False
    security_issues: List[str] = None
    error_message: str = ""

class JSAPIAnalyzer:
    """JS API分析器主类"""
    
    def __init__(self, target_url: str, timeout: int = 10, max_depth: int = 2,
                 auth_headers: Dict[str, str] = None, max_workers: int = 10, verbose: bool = False):
        self.target_url = target_url.rstrip('/')
        self.timeout = timeout
        self.max_depth = max_depth
        self.auth_headers = auth_headers or {}
        self.max_workers = max_workers
        self.verbose = verbose
        self.session = None

        # 解析目标URL
        self.parsed_url = urlparse(target_url)
        self.base_url = f"{self.parsed_url.scheme}://{self.parsed_url.netloc}"

        # 提取应用路径（用于正确拼接相对URL）
        self.app_path = self._extract_app_path(self.parsed_url.path)

        # 构建应用基础URL（包含应用路径）
        self.app_base_url = self.base_url + self.app_path

        # 存储结果
        self.js_files: Set[str] = set()
        self.api_endpoints: List[APIEndpoint] = []
        self.test_results: List[APITestResult] = []
        
        # API提取正则表达式模式（更精确的匹配）
        self.api_patterns = [
            # REST API路径（更严格）
            r'["\']([/]api[/][^"\']*)["\']',  # 必须有/api/
            r'["\']([/]v\d+[/][^"\']*)["\']',  # 必须有/v1/等

            # fetch调用
            r'fetch\s*\(\s*["\']([^"\']+)["\']',
            r'fetch\s*\(\s*`([^`]+)`',

            # axios调用
            r'axios\.(get|post|put|delete|patch)\s*\(\s*["\']([^"\']+)["\']',
            r'axios\s*\(\s*{\s*url\s*:\s*["\']([^"\']+)["\']',

            # jQuery AJAX
            r'\$\.ajax\s*\(\s*{\s*url\s*:\s*["\']([^"\']+)["\']',
            r'\$\.(get|post)\s*\(\s*["\']([^"\']+)["\']',

            # XMLHttpRequest
            r'\.open\s*\(\s*["\']([^"\']+)["\'],\s*["\']([^"\']+)["\']',

            # Java Web应用常见模式（更严格）
            r'["\']([^"\']{1,100}\.do(?:\?[^"\']{0,200})?)["\']',  # 限制长度，.do结尾
            r'["\']([^"\']{1,100}\.action(?:\?[^"\']{0,200})?)["\']',  # .action结尾
            r'["\']([^"\']{1,100}\.jsp(?:\?[^"\']{0,200})?)["\']',  # .jsp页面
            r'["\']([^"\']{1,100}\.php(?:\?[^"\']{0,200})?)["\']',  # .php页面
            r'["\']([^"\']{1,100}\.aspx?(?:\?[^"\']{0,200})?)["\']',  # .asp/.aspx页面

            # 特定的API路径模式（更精确）
            r'["\']([/](?:api|admin|manage|dashboard|service|rest)[/][^"\']{1,100})["\']',

            # URL参数模式（更严格）
            r'["\']([^"\']{1,100}\?[^"\']*(?:action|method|cmd|op|func)=[^"\']{1,50})["\']',

            # 特定的认证和用户相关API
            r'["\']([/](?:auth|login|logout|user)[^"\']{0,50}\.(?:do|action|php|jsp)(?:\?[^"\']{0,100})?)["\']',

            # 多级路径API（如 /auth/spnego/pcOwner）
            r'["\']([/](?:auth|admin|api|service)[/][^"\']{1,80})["\']',

            # 通用的多级路径模式
            r'["\']([/][a-zA-Z][^"\']{0,20}[/][a-zA-Z][^"\']{0,60})["\']',
        ]
        
        # 敏感数据检测模式
        self.sensitive_patterns = [
            r'password',
            r'token',
            r'secret',
            r'key',
            r'email',
            r'phone',
            r'ssn',
            r'credit',
            r'admin',
            r'user_id',
            r'session',
        ]

    def _extract_app_path(self, url_path: str) -> str:
        """
        提取应用路径，用于正确拼接相对URL

        例如：
        - /cas/PA011/UM_SELFSERVICE/login -> /cas
        - /app/admin/dashboard -> /app
        - /api/v1/users -> /api
        - /login -> ''
        """
        if not url_path or url_path == '/':
            return ''

        # 移除末尾的斜杠
        path = url_path.rstrip('/')

        # 特殊的应用路径识别（基于常见的应用部署模式）
        known_app_paths = [
            'cas', 'app', 'admin', 'api', 'web', 'portal', 'console',
            'dashboard', 'manage', 'system', 'service', 'auth', 'oauth',
            'sso', 'login', 'user', 'client', 'server', 'gateway'
        ]

        # 分割路径
        path_parts = [p for p in path.split('/') if p]

        if not path_parts:
            return ''

        # 检查第一级路径是否是已知的应用路径
        first_part = path_parts[0].lower()

        # 如果第一级路径是已知的应用路径，则使用它
        if first_part in known_app_paths:
            return f'/{path_parts[0]}'

        # 如果路径很深（超过2级），可能第一级是应用路径
        if len(path_parts) >= 2:
            return f'/{path_parts[0]}'

        # 否则不使用应用路径
        return ''

    async def analyze(self) -> Dict:
        """主分析函数"""
        if self.verbose:
            logger.debug(f"开始分析目标: {self.target_url}")
        
        # 创建HTTP会话
        connector = aiohttp.TCPConnector(limit=100, limit_per_host=30)
        timeout = aiohttp.ClientTimeout(total=self.timeout)
        self.session = aiohttp.ClientSession(
            connector=connector, 
            timeout=timeout,
            headers=self.auth_headers
        )
        
        try:
            # 步骤1: 收集JS文件
            await self._collect_js_files()
            if self.verbose:
                logger.debug(f"发现 {len(self.js_files)} 个JS文件")
            
            # 步骤2: 提取API端点
            await self._extract_api_endpoints()
            if self.verbose:
                logger.debug(f"提取到 {len(self.api_endpoints)} 个API端点")
            
            # 步骤3: 测试API端点
            await self._test_api_endpoints()
            if self.verbose:
                logger.debug(f"完成 {len(self.test_results)} 个API测试")

            # 步骤4: 智能选择最佳API版本
            self._select_best_api_versions()

            # 步骤5: 生成报告
            return self._generate_report()
            
        finally:
            if self.session:
                await self.session.close()

    async def _collect_js_files(self):
        """收集所有JS文件"""
        visited_urls = set()
        urls_to_visit = [self.target_url]
        current_depth = 0
        
        while urls_to_visit and current_depth < self.max_depth:
            current_urls = urls_to_visit.copy()
            urls_to_visit.clear()
            
            for url in current_urls:
                if url in visited_urls:
                    continue
                    
                visited_urls.add(url)
                
                try:
                    async with self.session.get(url) as response:
                        if response.status == 200:
                            content = await response.text()
                            
                            # 解析HTML获取JS文件链接
                            soup = BeautifulSoup(content, 'html.parser')
                            
                            # 外部JS文件
                            for script in soup.find_all('script', src=True):
                                js_url = urljoin(url, script['src'])
                                self.js_files.add(js_url)
                            
                            # 内联JS代码
                            for script in soup.find_all('script', src=False):
                                if script.string:
                                    # 将内联JS保存为虚拟文件
                                    inline_url = f"{url}#inline-{len(self.js_files)}"
                                    self.js_files.add(inline_url)
                            
                            # 收集页面链接用于深度爬取
                            if current_depth < self.max_depth - 1:
                                for link in soup.find_all('a', href=True):
                                    link_url = urljoin(url, link['href'])
                                    if self._is_same_domain(link_url):
                                        urls_to_visit.append(link_url)
                                        
                except Exception as e:
                    logger.warning(f"访问 {url} 失败: {e}")
            
            current_depth += 1

    def _is_same_domain(self, url: str) -> bool:
        """检查URL是否属于同一域名"""
        try:
            parsed = urlparse(url)
            return parsed.netloc == self.parsed_url.netloc
        except:
            return False

    async def _extract_api_endpoints(self):
        """从JS文件中提取API端点"""
        tasks = []
        
        for js_url in self.js_files:
            task = self._extract_from_js_file(js_url)
            tasks.append(task)
        
        # 并发处理所有JS文件
        await asyncio.gather(*tasks, return_exceptions=True)
        
        # 去重和标准化
        self._deduplicate_endpoints()

    async def _extract_from_js_file(self, js_url: str):
        """从单个JS文件提取API端点"""
        try:
            if '#inline-' in js_url:
                # 处理内联JS
                base_url = js_url.split('#')[0]
                async with self.session.get(base_url) as response:
                    content = await response.text()
                    soup = BeautifulSoup(content, 'html.parser')
                    scripts = soup.find_all('script', src=False)
                    if scripts:
                        js_content = scripts[0].string or ""
            else:
                # 处理外部JS文件
                async with self.session.get(js_url) as response:
                    js_content = await response.text()
            
            # 使用正则表达式提取API路径
            for pattern in self.api_patterns:
                matches = re.finditer(pattern, js_content, re.IGNORECASE)
                for match in matches:
                    self._process_regex_match(match, js_url, js_content)
                    
        except Exception as e:
            logger.warning(f"处理JS文件 {js_url} 失败: {e}")

    def _is_static_resource(self, url: str) -> bool:
        """检查URL是否是静态资源"""
        url_lower = url.lower()

        # 首先检查是否是明显的API路径，即使有静态扩展名也保留
        api_path_indicators = [
            '/api/', '/rest/', '/service/', '/webapi/', '/services/',
            '/v1/', '/v2/', '/v3/', '/v4/', '/v5/',
            '/auth/', '/oauth/', '/sso/',
            '/admin/api/', '/management/api/'
        ]

        for indicator in api_path_indicators:
            if indicator in url_lower:
                # 对于明显的API路径，只过滤真正的静态文件
                static_in_api = ['.css', '.js', '.png', '.jpg', '.gif', '.ico', '.woff', '.ttf']
                for ext in static_in_api:
                    if url_lower.endswith(ext):
                        return True
                return False  # API路径下的其他文件（如.json）保留

        # 静态资源文件扩展名
        static_extensions = [
            # 图片文件
            '.jpg', '.jpeg', '.png', '.gif', '.bmp', '.svg', '.ico', '.webp',
            # 样式文件
            '.css', '.scss', '.sass', '.less',
            # 脚本文件
            '.js', '.ts', '.jsx', '.tsx',
            # 字体文件
            '.woff', '.woff2', '.ttf', '.eot', '.otf',
            # 文档文件
            '.pdf', '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx',
            # 媒体文件
            '.mp3', '.mp4', '.avi', '.mov', '.wmv', '.flv',
            # 压缩文件
            '.zip', '.rar', '.7z', '.tar', '.gz',
            # 其他静态资源
            '.xml', '.txt', '.log', '.map', '.json'
        ]

        # 检查文件扩展名
        for ext in static_extensions:
            if url_lower.endswith(ext):
                return True

        # 检查是否包含静态资源路径特征
        static_path_patterns = [
            r'/static/',
            r'/assets/',
            r'/public/',
            r'/resources/',
            r'/images/',
            r'/img/',
            r'/css/',
            r'/js/',
            r'/fonts/',
            r'/media/',
            r'/uploads/',
            r'/files/',
        ]

        for pattern in static_path_patterns:
            if re.search(pattern, url_lower):
                return True

        return False

    def _is_valid_api_url(self, url: str) -> bool:
        """验证URL是否是有效的API路径"""
        if not url or len(url) < 2:
            return False

        # 首先检查是否是静态资源
        if self._is_static_resource(url):
            return False

        # 过滤明显不是API的内容
        invalid_patterns = [
            r'[{}()\[\]<>]',  # 包含代码符号
            r'function\s*\(',  # 包含function关键字
            r'var\s+\w+',  # 包含变量声明
            r'return\s+',  # 包含return语句
            r'if\s*\(',  # 包含if语句
            r'for\s*\(',  # 包含for循环
            r'while\s*\(',  # 包含while循环
            r'[=]{2,}',  # 包含比较运算符
            r'[!]{2,}',  # 包含逻辑运算符
            r'prototype\.',  # 包含prototype
            r'\.length\b',  # 包含.length
            r'\.push\b',  # 包含.push
            r'\.slice\b',  # 包含.slice
            r'console\.',  # 包含console
            r'window\.',  # 包含window
            r'document\.',  # 包含document
            r'[a-zA-Z]+\s*:\s*[a-zA-Z]+',  # 包含对象属性定义
        ]

        for pattern in invalid_patterns:
            if re.search(pattern, url, re.IGNORECASE):
                return False

        # 检查长度是否合理（API路径通常不会太长）
        if len(url) > 200:
            return False

        # 检查是否包含过多的特殊字符
        special_char_count = len(re.findall(r'[^a-zA-Z0-9/_\-\.?&=]', url))
        if special_char_count > len(url) * 0.3:  # 特殊字符超过30%
            return False

        return True

    def _should_prioritize_global_path(self, url: str) -> bool:
        """判断API是否应该优先使用全局路径（不添加应用路径）"""
        global_patterns = [
            '/api/', '/rest/', '/service/', '/webapi/', '/services/',
            '/v1/', '/v2/', '/v3/', '/public/', '/common/',
            '/system/', '/admin/api/', '/management/'
        ]
        url_lower = url.lower()
        return any(pattern in url_lower for pattern in global_patterns)

    def _process_regex_match(self, match, js_url: str, js_content: str):
        """处理正则匹配结果"""
        groups = match.groups()

        # 提取URL和HTTP方法
        if len(groups) == 1:
            url = groups[0]
            method = "GET"
        elif len(groups) == 2:
            if groups[0].upper() in ['GET', 'POST', 'PUT', 'DELETE', 'PATCH']:
                method = groups[0].upper()
                url = groups[1]
            else:
                method = groups[0].upper() if groups[0] else "GET"
                url = groups[1]
        else:
            return

        # 验证URL是否有效
        if not self._is_valid_api_url(url):
            return

        # 获取上下文信息
        lines = js_content.split('\n')
        line_number = js_content[:match.start()].count('\n') + 1
        context = lines[line_number - 1] if line_number <= len(lines) else ""

        # 标准化URL - 智能双重策略
        if url.startswith('/'):
            # 绝对路径：需要智能判断是否添加应用路径

            # 检查是否已经包含应用路径
            if self.app_path and url.startswith(self.app_path):
                # URL已经包含应用路径，直接使用
                full_url = self.base_url + url
                self._create_endpoint(full_url, method, js_url, line_number, context, url, "with_app_path")
            else:
                # URL不包含应用路径，生成两个版本进行测试

                # 版本1：不添加应用路径（全局路径）
                global_url = self.base_url + url

                # 版本2：添加应用路径（应用内路径）
                app_url = self.app_base_url + url if self.app_path else None

                # 总是生成两个版本，让后续的智能选择来决定最佳版本
                if self._should_prioritize_global_path(url):
                    # 优先全局路径
                    self._create_endpoint(global_url, method, js_url, line_number, context, url, "global_priority")
                    if app_url and app_url != global_url:
                        self._create_endpoint(app_url, method, js_url, line_number, context, url, "app_fallback")
                else:
                    # 优先应用路径，但仍然生成两个版本
                    if app_url and app_url != global_url:
                        self._create_endpoint(app_url, method, js_url, line_number, context, url, "app_priority")
                    self._create_endpoint(global_url, method, js_url, line_number, context, url, "global_fallback")

        elif url.startswith('http'):
            # 完整URL，直接使用
            self._create_endpoint(url, method, js_url, line_number, context, url, "full_url")
        else:
            # 相对路径：基于当前页面的应用路径拼接
            if self.app_path:
                full_url = urljoin(self.app_base_url + '/', url)
            else:
                full_url = urljoin(self.base_url + '/', url)
            self._create_endpoint(full_url, method, js_url, line_number, context, url, "relative")

    def _create_endpoint(self, full_url: str, method: str, js_url: str, line_number: int, context: str, original_url: str, path_type: str):
        """创建API端点对象"""
        endpoint = APIEndpoint(
            url=full_url,
            method=method,
            source_file=js_url,
            line_number=line_number,
            context=context.strip()
        )

        # 添加额外信息用于后续处理
        endpoint.original_url = original_url
        endpoint.path_type = path_type

        self.api_endpoints.append(endpoint)

    def _deduplicate_endpoints(self):
        """去重API端点，但保留不同路径版本用于后续测试"""
        # 按完整URL去重（保留不同的路径版本）
        seen = set()
        unique_endpoints = []

        for endpoint in self.api_endpoints:
            key = (endpoint.url, endpoint.method)
            if key not in seen:
                seen.add(key)
                unique_endpoints.append(endpoint)

        self.api_endpoints = unique_endpoints
        if self.verbose:
            logger.debug(f"去重后剩余 {len(self.api_endpoints)} 个唯一API端点")

    def _select_best_endpoint(self, endpoints):
        """从多个端点版本中选择最佳的"""
        # 优先级规则：
        # 1. 有明确优先级标识的（priority > fallback）
        # 2. 全局路径优先（对于明显的全局API）
        # 3. 应用路径优先（对于业务API）

        priority_endpoints = []
        fallback_endpoints = []

        for endpoint in endpoints:
            path_type = getattr(endpoint, 'path_type', '')
            if 'priority' in path_type:
                priority_endpoints.append(endpoint)
            elif 'fallback' in path_type:
                fallback_endpoints.append(endpoint)
            else:
                priority_endpoints.append(endpoint)  # 默认为优先级

        # 如果有优先级端点，选择第一个；否则选择第一个fallback
        if priority_endpoints:
            return priority_endpoints[0]
        else:
            return fallback_endpoints[0] if fallback_endpoints else endpoints[0]

    def _select_best_api_versions(self):
        """根据测试结果选择最佳的API版本"""
        # 按原始URL分组测试结果
        result_groups = {}
        for result in self.test_results:
            original = getattr(result.endpoint, 'original_url', result.endpoint.url)
            key = (original, result.endpoint.method)

            if key not in result_groups:
                result_groups[key] = []
            result_groups[key].append(result)

        # 选择每组中最佳的结果
        best_results = []
        for (original_url, method), results in result_groups.items():
            if len(results) == 1:
                # 只有一个版本
                best_results.append(results[0])
            else:
                # 多个版本，选择最佳的
                best_result = self._choose_best_result(results)
                best_results.append(best_result)

                # 记录选择信息
                if self.verbose:
                    logger.debug(f"为 {original_url} 选择了最佳版本: {best_result.endpoint.url} (状态码: {best_result.status_code})")

        # 更新测试结果
        self.test_results = best_results

    def _choose_best_result(self, results):
        """从多个测试结果中选择最佳的"""
        # 状态码优先级：200 > 401 > 403 > 其他 > 404 > 0
        def status_priority(status_code):
            if status_code == 200:
                return 1
            elif status_code == 401:
                return 2
            elif status_code == 403:
                return 3
            elif status_code == 404:
                return 5
            elif status_code == 0:
                return 6
            else:
                return 4

        # 按状态码优先级排序
        sorted_results = sorted(results, key=lambda r: status_priority(r.status_code))

        # 如果最佳结果有多个相同状态码，选择响应时间最短的
        best_status = sorted_results[0].status_code
        best_candidates = [r for r in sorted_results if r.status_code == best_status]

        if len(best_candidates) > 1:
            # 选择响应时间最短的
            return min(best_candidates, key=lambda r: r.response_time)
        else:
            return best_candidates[0]

    async def _test_api_endpoints(self):
        """测试所有API端点"""
        semaphore = asyncio.Semaphore(self.max_workers)
        tasks = []

        for endpoint in self.api_endpoints:
            task = self._test_single_endpoint(endpoint, semaphore)
            tasks.append(task)

        # 并发测试所有端点
        results = await asyncio.gather(*tasks, return_exceptions=True)

        # 过滤异常结果
        self.test_results = [r for r in results if isinstance(r, APITestResult)]

    async def _test_single_endpoint(self, endpoint: APIEndpoint, semaphore) -> APITestResult:
        """测试单个API端点"""
        async with semaphore:
            result = APITestResult(
                endpoint=endpoint,
                headers={},
                security_issues=[],
                response_body=""
            )

            try:
                start_time = time.time()

                # 发送HTTP请求
                async with self.session.request(
                    method=endpoint.method,
                    url=endpoint.url,
                    allow_redirects=False
                ) as response:
                    result.status_code = response.status
                    result.response_time = time.time() - start_time
                    result.headers = dict(response.headers)

                    # 读取响应体（限制大小）
                    content = await response.read()
                    result.response_size = len(content)

                    if result.response_size < 1024 * 1024:  # 限制1MB
                        try:
                            result.response_body = content.decode('utf-8', errors='ignore')
                        except:
                            result.response_body = str(content)

                    # 分析响应
                    self._analyze_response(result)

            except asyncio.TimeoutError:
                result.error_message = "请求超时"
            except Exception as e:
                result.error_message = str(e)

            return result

    def _analyze_response(self, result: APITestResult):
        """分析API响应，检测安全问题"""
        # 检查是否可访问
        if result.status_code in [200, 201, 202, 204]:
            result.is_accessible = True

        # 检查未授权访问
        if result.status_code == 200 and not self.auth_headers:
            # 如果没有提供认证信息但返回200，可能存在未授权访问
            if self._contains_sensitive_content(result.response_body):
                result.is_unauthorized = True
                result.security_issues.append("可能存在未授权访问")

        # 检查敏感数据泄露
        if self._contains_sensitive_data(result.response_body):
            result.has_sensitive_data = True
            result.security_issues.append("响应包含敏感数据")

        # 检查错误信息泄露
        if result.status_code >= 400:
            if self._contains_error_disclosure(result.response_body):
                result.security_issues.append("错误信息泄露")

        # 检查安全头
        self._check_security_headers(result)

        # 检查CORS配置
        self._check_cors_config(result)

    def _contains_sensitive_content(self, content: str) -> bool:
        """检查内容是否包含敏感信息"""
        if not content:
            return False

        content_lower = content.lower()

        # 检查是否包含用户数据、管理信息等
        sensitive_indicators = [
            'user', 'admin', 'password', 'token', 'secret',
            'email', 'phone', 'address', 'credit', 'payment',
            'database', 'config', 'settings', 'private'
        ]

        return any(indicator in content_lower for indicator in sensitive_indicators)

    def _contains_sensitive_data(self, content: str) -> bool:
        """检查是否包含敏感数据"""
        if not content:
            return False

        for pattern in self.sensitive_patterns:
            if re.search(pattern, content, re.IGNORECASE):
                return True

        # 检查JSON结构中的敏感字段
        try:
            if content.strip().startswith('{') or content.strip().startswith('['):
                data = json.loads(content)
                return self._check_json_sensitive_data(data)
        except:
            pass

        return False

    def _check_json_sensitive_data(self, data) -> bool:
        """递归检查JSON数据中的敏感字段"""
        if isinstance(data, dict):
            for key, value in data.items():
                if any(pattern in key.lower() for pattern in self.sensitive_patterns):
                    return True
                if self._check_json_sensitive_data(value):
                    return True
        elif isinstance(data, list):
            for item in data:
                if self._check_json_sensitive_data(item):
                    return True

        return False

    def _contains_error_disclosure(self, content: str) -> bool:
        """检查错误信息泄露"""
        error_patterns = [
            r'stack trace',
            r'exception',
            r'error.*line \d+',
            r'mysql.*error',
            r'postgresql.*error',
            r'oracle.*error',
            r'sql.*syntax',
            r'file not found',
            r'permission denied',
            r'access denied',
        ]

        for pattern in error_patterns:
            if re.search(pattern, content, re.IGNORECASE):
                return True

        return False

    def _check_security_headers(self, result: APITestResult):
        """检查安全响应头"""
        headers = {k.lower(): v for k, v in result.headers.items()}

        # 检查缺失的安全头
        security_headers = [
            'x-content-type-options',
            'x-frame-options',
            'x-xss-protection',
            'strict-transport-security',
            'content-security-policy'
        ]

        missing_headers = [h for h in security_headers if h not in headers]
        if missing_headers:
            result.security_issues.append(f"缺失安全头: {', '.join(missing_headers)}")

    def _check_cors_config(self, result: APITestResult):
        """检查CORS配置"""
        headers = {k.lower(): v for k, v in result.headers.items()}

        # 检查过于宽松的CORS配置
        if 'access-control-allow-origin' in headers:
            origin = headers['access-control-allow-origin']
            if origin == '*':
                result.security_issues.append("CORS配置过于宽松 (允许所有域名)")

    def _generate_report(self) -> Dict:
        """生成分析报告"""
        # 过滤掉404和网络错误的结果，保留所有有响应的API（包括401认证错误）
        valid_results = [r for r in self.test_results if r.status_code != 404 and r.status_code != 0]

        # 统计信息（基于有效结果）
        total_apis = len(self.test_results)  # 总发现数
        valid_api_count = len(valid_results)  # 有效API数（非404）
        accessible_apis = len([r for r in valid_results if r.is_accessible])
        unauthorized_apis = len([r for r in valid_results if r.is_unauthorized])
        sensitive_data_apis = len([r for r in valid_results if r.has_sensitive_data])

        # 按状态分类（只统计非404）
        status_distribution = {}
        for result in valid_results:
            status = result.status_code
            status_distribution[status] = status_distribution.get(status, 0) + 1

        # 安全问题汇总（只统计非404）
        all_security_issues = []
        for result in valid_results:
            all_security_issues.extend(result.security_issues)

        security_issue_counts = {}
        for issue in all_security_issues:
            security_issue_counts[issue] = security_issue_counts.get(issue, 0) + 1

        # 生成详细结果（只包含非404的API）
        detailed_results = []
        for result in valid_results:
            detailed_results.append({
                'url': result.endpoint.url,
                'method': result.endpoint.method,
                'source_file': result.endpoint.source_file,
                'status_code': result.status_code,
                'response_time': round(result.response_time, 3),
                'response_size': result.response_size,
                'is_accessible': result.is_accessible,
                'is_unauthorized': result.is_unauthorized,
                'has_sensitive_data': result.has_sensitive_data,
                'security_issues': result.security_issues,
                'error_message': result.error_message
            })

        return {
            'summary': {
                'target_url': self.target_url,
                'total_js_files': len(self.js_files),
                'total_apis_discovered': total_apis,  # 总发现的API数
                'valid_apis': valid_api_count,  # 有效API数（非404且非网络错误）
                'accessible_apis': accessible_apis,
                'unauthorized_apis': unauthorized_apis,
                'sensitive_data_apis': sensitive_data_apis,
                'filtered_count': total_apis - len(valid_results),  # 被过滤的无效API数量（404+网络错误）
                'scan_time': time.strftime('%Y-%m-%d %H:%M:%S')
            },
            'statistics': {
                'status_distribution': status_distribution,
                'security_issue_counts': security_issue_counts
            },
            'detailed_results': detailed_results,
            'js_files': list(self.js_files)
        }


async def js_api_analyzer_wrapper(target: str, timeout: int = 10, max_depth: int = 2,
                                auth_headers: Optional[Dict[str, str]] = None,
                                max_workers: int = 10, only_valid_apis: bool = False,
                                verbose: bool = False) -> str:
    """
    JS API分析器包装函数 - MCP工具接口

    Args:
        target: 目标URL - 必须是完整的URL (http/https)
        timeout: 请求超时时间（秒）
        max_depth: 爬取深度
        auth_headers: 认证头信息（可选）
        max_workers: 最大并发数
        only_valid_apis: 是否只输出有效API（非404）的简洁列表

    Returns:
        JSON格式的分析报告或简洁的API列表
    """
    if not target.startswith(('http://', 'https://')):
        return json.dumps({
            'error': '目标URL必须包含协议 (http:// 或 https://)',
            'example': 'https://example.com'
        }, ensure_ascii=False, indent=2)

    try:
        analyzer = JSAPIAnalyzer(
            target_url=target,
            timeout=timeout,
            max_depth=max_depth,
            auth_headers=auth_headers,
            max_workers=max_workers,
            verbose=verbose
        )

        result = await analyzer.analyze()

        # 如果只需要有效API列表，返回简洁格式
        if only_valid_apis:
            valid_apis = []
            for api in result.get('detailed_results', []):
                valid_apis.append({
                    'method': api['method'],
                    'url': api['url'],
                    'status_code': api['status_code'],
                    'response_time': api['response_time']
                })

            simple_result = {
                'target': target,
                'valid_apis_count': len(valid_apis),
                'valid_apis': valid_apis
            }
            return json.dumps(simple_result, ensure_ascii=False, indent=2)

        return json.dumps(result, ensure_ascii=False, indent=2)

    except Exception as e:
        logger.error(f"分析失败: {e}")
        return json.dumps({
            'error': f'分析失败: {str(e)}',
            'target': target
        }, ensure_ascii=False, indent=2)


async def get_valid_apis_only(target: str, timeout: int = 10, max_depth: int = 2,
                             auth_headers: Optional[Dict[str, str]] = None,
                             max_workers: int = 10, include_404: bool = False,
                             verbose: bool = False) -> str:
    """
    只获取有效API列表的简洁函数

    Args:
        target: 目标URL
        timeout: 请求超时时间
        max_depth: 爬取深度
        auth_headers: 认证头信息
        max_workers: 最大并发数
        include_404: 是否包含404状态码的API（默认False）

    Returns:
        只包含有效API的简洁JSON列表
    """
    try:
        analyzer = JSAPIAnalyzer(
            target_url=target,
            timeout=timeout,
            max_depth=max_depth,
            auth_headers=auth_headers,
            max_workers=max_workers
        )

        result = await analyzer.analyze()

        # 提取有效API
        valid_apis = []

        # 如果包含404，需要重新分析获取完整结果
        if include_404:
            # 重新分析获取包含404的完整结果
            analyzer = JSAPIAnalyzer(
                target_url=target,
                timeout=timeout,
                max_depth=max_depth,
                auth_headers=auth_headers,
                max_workers=max_workers,
                verbose=verbose
            )

            # 创建HTTP会话
            import aiohttp
            connector = aiohttp.TCPConnector(limit=100, limit_per_host=30)
            timeout_obj = aiohttp.ClientTimeout(total=timeout)
            analyzer.session = aiohttp.ClientSession(
                connector=connector,
                timeout=timeout_obj,
                headers=auth_headers or {}
            )

            try:
                # 执行完整分析
                await analyzer._collect_js_files()
                await analyzer._extract_api_endpoints()
                await analyzer._test_api_endpoints()

                # 根据include_404参数决定过滤策略
                for test_result in analyzer.test_results:
                    if include_404:
                        # 包含404，只过滤网络错误
                        if test_result.status_code != 0:
                            valid_apis.append(f"{test_result.endpoint.method} {test_result.endpoint.url} ({test_result.status_code})")
                    else:
                        # 不包含404，过滤404和网络错误
                        if test_result.status_code != 0 and test_result.status_code != 404:
                            valid_apis.append(f"{test_result.endpoint.method} {test_result.endpoint.url} ({test_result.status_code})")

                total_discovered = len(analyzer.test_results)
                if include_404:
                    # 只过滤网络错误
                    filtered_count = len([r for r in analyzer.test_results if r.status_code == 0])
                else:
                    # 过滤404和网络错误
                    filtered_count = len([r for r in analyzer.test_results if r.status_code == 0 or r.status_code == 404])

            finally:
                await analyzer.session.close()
        else:
            # 原有逻辑：使用已过滤的结果（已经过滤了404和网络错误）
            result_str = await js_api_analyzer_wrapper(
                target=target,
                timeout=timeout,
                max_depth=max_depth,
                auth_headers=auth_headers,
                max_workers=max_workers,
                verbose=verbose
            )

            result_data = json.loads(result_str)
            for api in result_data.get('detailed_results', []):
                valid_apis.append(f"{api['method']} {api['url']} ({api['status_code']})")

            total_discovered = result_data['summary']['total_apis_discovered']
            filtered_count = result_data['summary']['filtered_count']

        # 简洁输出格式
        output = {
            'target': target,
            'scan_time': result['summary']['scan_time'] if not include_404 else time.strftime('%Y-%m-%d %H:%M:%S'),
            'total_discovered': total_discovered,
            'valid_count': len(valid_apis),
            'filtered_count': filtered_count,
            'valid_apis': valid_apis
        }

        return json.dumps(output, ensure_ascii=False, indent=2)

    except Exception as e:
        return json.dumps({
            'error': f'分析失败: {str(e)}',
            'target': target
        }, ensure_ascii=False, indent=2)


def main():
    """命令行主函数"""
    parser = argparse.ArgumentParser(description='JS API分析器 - 从网站JS代码中提取并测试API端点')
    parser.add_argument('target', help='目标URL (必须包含http://或https://)')
    parser.add_argument('--timeout', type=int, default=10, help='请求超时时间（秒）')
    parser.add_argument('--max-depth', type=int, default=2, help='爬取深度')
    parser.add_argument('--max-workers', type=int, default=10, help='最大并发数')
    parser.add_argument('--auth-header', action='append', help='认证头 (格式: "Header: Value")')
    parser.add_argument('--output', '-o', help='输出文件路径')
    parser.add_argument('--verbose', '-v', action='store_true', help='详细输出')
    parser.add_argument('--valid-only', action='store_true', help='只输出有效API（非404）')
    parser.add_argument('--include-404', action='store_true', help='在有效API输出中包含404状态码的API')

    args = parser.parse_args()

    # 设置日志级别 - 默认只显示ERROR，verbose模式显示所有
    if args.verbose:
        logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(levelname)s - %(message)s')
    else:
        logging.basicConfig(level=logging.ERROR, format='%(message)s')

    # 解析认证头
    auth_headers = {}
    if args.auth_header:
        for header in args.auth_header:
            if ':' in header:
                key, value = header.split(':', 1)
                auth_headers[key.strip()] = value.strip()

    # 运行分析
    async def run_analysis():
        if args.valid_only:
            # 只输出有效API
            return await get_valid_apis_only(
                target=args.target,
                timeout=args.timeout,
                max_depth=args.max_depth,
                auth_headers=auth_headers if auth_headers else None,
                max_workers=args.max_workers,
                include_404=args.include_404,
                verbose=args.verbose
            )
        else:
            # 完整分析
            return await js_api_analyzer_wrapper(
                target=args.target,
                timeout=args.timeout,
                max_depth=args.max_depth,
                auth_headers=auth_headers if auth_headers else None,
                max_workers=args.max_workers,
                verbose=args.verbose
            )

    # 执行异步分析
    result = asyncio.run(run_analysis())

    # 输出结果
    if args.output:
        with open(args.output, 'w', encoding='utf-8') as f:
            f.write(result)
        print(f"结果已保存到: {args.output}")
    else:
        print(result)


if __name__ == "__main__":
    main()
