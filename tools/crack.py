# Encoding: utf-8
from utils.exec import run_executable as exec
from typing import Optional
import os

def run_crack(
    protocol: str,
    targets: str,
    usernames: Optional[str] = None,
    passwords: Optional[str] = None,
) -> str:
    """运行 Crack 工具进行网络服务暴力破解

    使用 Crack 工具对指定协议的网络服务进行用户名和密码暴力破解攻击。

    参数:
        protocol: 目标协议类型（如 ssh、ftp、smb 等）
        targets: 目标服务地址，格式为 "IP:PORT"
        usernames: 用户名列表，可以是文件路径或逗号分隔的字符串
        passwords: 密码列表，可以是文件路径或逗号分隔的字符串

    返回:
        str: 暴力破解的结果，包含发现的有效凭据
    """

    cmd = ["crack", "-target-concurrent", "30", "-task-concurrent", "15", "-retries", "2", "-timeout", "5", "-ok-to-stop",
        "-protocol", protocol, "-targets", targets]

    # 处理用户名参数
    if usernames:
        # 判断是文件路径还是逗号分隔的列表
        if os.path.isfile(usernames):
            cmd.extend(["-uf", usernames])
        else:
            cmd.extend(["-u", usernames])
    else:
        # 使用默认用户名字典
        default_user_file = f"./dicts/user-passwords/dic_username_{protocol}.txt"
        if os.path.isfile(default_user_file):
            cmd.extend(["-uf", default_user_file])
        else:
            # 如果没有协议特定的字典，使用通用字典
            generic_user_file = "./dicts/user-passwords/users.txt"
            if os.path.isfile(generic_user_file):
                cmd.extend(["-uf", generic_user_file])

    # 处理密码参数
    if passwords:
        # 判断是文件路径还是逗号分隔的列表
        if os.path.isfile(passwords):
            cmd.extend(["-pf", passwords])
        else:
            cmd.extend(["-p", passwords])
    else:
        # 使用默认密码字典
        default_pass_file = f"./dicts/user-passwords/dic_password_{protocol}.txt"
        if os.path.isfile(default_pass_file):
            cmd.extend(["-pf", default_pass_file])
        else:
            # 如果没有协议特定的字典，使用通用字典
            generic_pass_file = "./dicts/user-passwords/passwords.txt"
            if os.path.isfile(generic_pass_file):
                cmd.extend(["-pf", generic_pass_file])

    return exec(cmd)
