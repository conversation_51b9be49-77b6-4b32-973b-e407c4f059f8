# Encdoding: utf-8
from utils.exec import run_executable as exec  # 使用绝对导入

def run_katana(
    target: str,
) -> str:
    """Use katana to crawl website interfaces
    
    Args:
        target: Target url
    
    Returns:
        str: website interfaces list
    """
    # 公共参数
    # cmd = [
    #     'katana',
    #     '-u', target,
    #     '-jc', '-jsl',
    #     '-xhr', '-kf',
    #     '-c', '50',
    #     '-d', '3',
    #     '-rd', '1',
    #     '-rl', '500',
    #     '-fs', "rdn",
    #     '-ef', 'png,css,jpg,gif,svg,ico,woff,ttf'
    # ]

    # 高级版 - 全量参数
    cmd = [
        'katana',
        '-u', target,
        '-d', '6',
        '-jc', '-jsl',
        '-hl',
        '-xhr',
        '-fx',
        '-td',
        '-aff',
        '-kf', 'all',
        '-c', '20',
        '-p', '10',
        '-rl', '80',
        '-timeout', '20',
        '-retry', '3',
        '-cs', '^https?://[^/]*target\\.com/(api|v[0-9]+|rest|graphql|webhook|endpoint|service|admin|manage|dashboard)',
        '-f', 'url,path,qurl,qpath,key,value',
        '-em', 'js,json,xml,php,asp,aspx,jsp,py,rb,go,yaml,yml',
        '-ef', 'png,jpg,jpeg,gif,svg,css,ico,woff,woff2,ttf,eot,pdf,doc,docx,zip,rar,mp4,mp3,avi',
        '-mr', '/(api|v[0-9]+|rest|graphql|webhook|endpoint|service|admin|manage|dashboard)/',
        '-mr', '\\?.*[=&](id|key|token|auth|api|secret|access)',
        '-mr', '/[a-zA-Z0-9_-]+\\.(json|xml|yaml|yml)(\\?|$)',
        '-mr', '/(get|post|put|delete|patch|options|head)[A-Z][a-zA-Z]*',
        '-fr', '/(static|assets|public|img|image|css|js|font|media|upload|download)/',
        '-fr', '\\.(png|jpg|jpeg|gif|svg|css|ico|woff|woff2|ttf|eot|pdf|doc|docx|zip|rar|mp4|mp3|avi)$',
        '-fr', '/\\.well-known/',
        '-mdc', 'status_code >= 200 && status_code < 400',
        '-mdc', 'contains(tolower(content_type), \'json\') || contains(tolower(content_type), \'xml\') || contains(tolower(content_type), \'text\')',
        '-fdc', 'status_code == 404 || status_code == 403',
        '-fdc', 'contains(tolower(response), \'error 404\') || contains(tolower(response), \'not found\') || contains(tolower(response), \'access denied\')',
        '-H', 'User-Agent: Mozilla/5.0 (compatible; API-Scanner/1.0)',
        '-H', 'Accept: application/json, application/xml, text/plain, */*',
        '-j', '-silent', '-nc'
    ]
    
    # 基础版 - 简化参数
    cmd_basic = [
        'katana',
        '-u', target,
        '-d', '5',
        '-jc', '-jsl',
        '-hl',
        '-xhr',
        '-fx',
        '-td',
        '-kf', 'all',
        '-c', '15',
        '-p', '8',
        '-rl', '100',
        '-timeout', '15',
        '-retry', '2',
        '-f', 'url,path,qurl,qpath',
        '-em', 'js,json,xml,php,asp,aspx,jsp,py,rb,go',
        '-ef', 'png,jpg,jpeg,gif,svg,css,ico,woff,woff2,ttf,eot,pdf,doc,docx,zip,rar',
        '-mr', '/(api|v[0-9]+|rest|graphql|webhook|endpoint|service)/',
        '-mr', '\\.(json|xml)(\\?|$)',
        '-fr', '/(static|assets|public|img|css|js|font|media)/',
        '-fr', '\\.(png|jpg|jpeg|gif|svg|css|ico|woff|woff2|ttf|eot|pdf|doc|docx|zip|rar)$',
        '-mdc', 'contains(tolower(response), \'api\')',
        '-mdc', 'contains(tolower(response), \'json\')',
        '-fdc', 'contains(tolower(response), \'error 404\')',
        '-fdc', 'contains(tolower(response), \'not found\')',
        '-j','-silent', '-nc'
    ]

    # 快速扫描版 - 轻量级
    cmd_quick = [
        'katana',
        '-u', target,
        '-d', '3',
        '-jc',
        '-c', '25',
        '-rl', '150',
        '-timeout', '10',
        '-f', 'url,path',
        '-em', 'json,xml,php,asp,aspx,jsp',
        '-ef', 'png,jpg,jpeg,gif,svg,css,ico,woff,ttf,pdf,zip',
        '-mr', '/(api|v[0-9]+|rest|graphql)/',
        '-mr', '\\.(json|xml)(\\?|$)',
        '-fr', '/(static|assets|css|js|img)/',
        '-silent', '-nc'
    ]

    return exec(cmd)