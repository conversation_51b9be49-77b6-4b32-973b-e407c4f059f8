# Encdoding: utf-8
from typing import Optional
from utils.exec import run_executable as exec  # 使用绝对导入

def run_httpx(
    target: str,
    method: Optional[str] = "GET",
    headers: Optional[dict] = None,
    data: Optional[str] = None,
) -> str:
    """运行 HTTPX 工具进行 HTTP 服务探测

    使用 HTTPX 工具对目标主机进行 HTTP 服务探测和分析，支持自定义请求方法、头部和数据。

    参数:
        target: 目标主机地址
        method: HTTP 请求方法，默认为 "GET"
        headers: HTTP 请求头部字典，可选
        data: HTTP 请求体数据，可选

    返回:
        str: JSON 格式的 HTTPX 探测结果
    """
    # 构建基本命令
    cmd = [
        "httpx", "-u", target,
        "-x", method,
        "-irr", "-json"
    ]

    # 添加请求体参数（如果存在）
    if data:
        cmd.extend(["-body", data])
    if headers:
        for key, value in headers.items():
            cmd.extend(["-header", f"{key}: {value}"])


    return exec(cmd)
