# Encdoding: utf-8
from typing import Optional
from utils.exec import run_executable as exec  # 使用绝对导入

def run_httpx(
    target: str,
    method: Optional[str] = "GET",
    headers: Optional[dict] = None,
    data: Optional[str] = None,
) -> str:
    """Run httpx to probe HTTP servers.

    Args:
        target: Input target host(s) to probe
        method: HTTP method to use (default: GET)
        headers: HTTP request headers (default: None)
        data: HTTP request body (default: None)

    Returns:
        str: httpx output in JSON format
    """
    # 构建基本命令
    cmd = [
        "httpx", "-u", target,
        "-x", method,
        "-irr", "-json"
    ]

    # 添加请求体参数（如果存在）
    if data:
        cmd.extend(["-body", data])
    if headers:
        for key, value in headers.items():
            cmd.extend(["-header", f"{key}: {value}"])


    return exec(cmd)
