# Encdoding: utf-8
import re
import logging
from typing import Set
from utils.exec import run_executable as exec  # 使用绝对导入

logger = logging.getLogger(__name__)

def _parse_rustscan_output(output: str, target: str) -> Set[int]:
    """解析 RustScan 输出，提取端口号

    Args:
        output: RustScan 的原始输出
        target: 目标IP地址

    Returns:
        Set[int]: 发现的端口号集合
    """
    ports = set()

    # RustScan 输出格式通常是: IP -> [port1,port2,port3]
    # 或者每行一个端口的格式

    # 匹配格式: IP -> [port1,port2,port3]
    pattern1 = rf"{re.escape(target)}\s*->\s*\[([^\]]+)\]"
    match = re.search(pattern1, output)
    if match:
        port_str = match.group(1)
        for port in port_str.split(','):
            try:
                ports.add(int(port.strip()))
            except ValueError:
                continue

    return ports

def _format_rustscan_output(target: str, ports: Set[int]) -> str:
    """格式化输出为标准格式

    Args:
        target: 目标IP地址
        ports: 端口号集合

    Returns:
        str: 格式化的输出字符串
    """
    if not ports:
        return f"{target} -> []"

    # 按端口号排序
    sorted_ports = sorted(ports)
    port_list = ','.join(map(str, sorted_ports))
    return f"{target} -> [{port_list}]"

def run_rustscan(
    target: str,
    ports: str,
) -> str:
    """运行 RustScan 网络端口扫描工具

    使用 RustScan 工具对目标进行高速端口扫描，采用双重扫描策略减少漏报。

    参数:
        target: 目标 IP 地址或主机名
        ports: 端口范围（如 "1-1000"）或逗号分隔的端口（如 "80,443"）

    返回:
        str: 可解析格式的扫描结果（如 "************* -> [22,8081,53]"）
    """
    logger.debug(f"Starting dual RustScan for {target} with ports {ports}")

    # 第一次扫描参数 - 较快的扫描
    cmd1 = [
        'rustscan',
        '-a', target,
        '-t', '4000',      # 较低的超时时间
        '-b', '1000',      # 线程数量
        '--scan-order', 'random',
        '--tries', '3',
        '-g'
    ]

    # 根据端口格式添加不同的参数
    port_args = []
    if ports == 'top':
        port_args = ['--top']
    elif '-' in ports:
        port_args = ['-r', ports]
    else:
        port_args = ['-p', ports]

    # 为两个命令添加端口参数
    cmd1.extend(port_args)

    # 执行第一次扫描
    logger.debug(f"Executing first scan: {' '.join(cmd1)}")
    try:
        result1 = exec(cmd1)
        ports1 = _parse_rustscan_output(result1, target)
        logger.debug(f"First scan found ports: {sorted(ports1)}")
    except Exception as e:
        logger.warning(f"First scan failed: {e}")
        result1 = e

    return result1