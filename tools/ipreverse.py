# Encoding: utf-8
import json
import requests
from datetime import datetime

def extract_domains_from_json(json_str) -> str:
    """
    从VirusTotal API的JSON响应中提取域名并去重

    Returns:
        JSON字符串格式的结果
    """
    try:
        # 解析JSON数据
        data = json.loads(json_str)
        
        # 提取所有域名
        domains = []
        ip_address = None
        
        for item in data.get('data', []):
            attributes = item.get('attributes', {})
            host_name = attributes.get('host_name')
            ip_addr = attributes.get('ip_address')
            date = attributes.get('date')
            
            if host_name:
                domains.append({
                    'domain': host_name,
                    'ip': ip_addr,
                    'date': date,
                    'date_readable': datetime.fromtimestamp(date).strftime('%Y-%m-%d %H:%M:%S') if date else None
                })
            
            if ip_addr and not ip_address:
                ip_address = ip_addr
        
        # 去重（基于域名）
        unique_domains = []
        seen_domains = set()
        
        for domain_info in domains:
            domain = domain_info['domain']
            if domain not in seen_domains:
                seen_domains.add(domain)
                unique_domains.append(domain_info)
        
        result = {
            'ip_address': ip_address,
            'total_records': len(domains),
            'unique_domains_count': len(unique_domains),
            'unique_domains': unique_domains,
            'domain_list': [d['domain'] for d in unique_domains]
        }
        return json.dumps(result, ensure_ascii=False)

    except json.JSONDecodeError as e:
        return json.dumps({"error": f"JSON解析错误: {e}"}, ensure_ascii=False)
    except Exception as e:
        return json.dumps({"error": f"处理错误: {e}"}, ensure_ascii=False)
def ipreverse(ip: str, VT_API_KEY: str) -> str:
    """
    查询IP地址的历史域名解析记录

    Args:
        ip: IP地址字符串
        VT_API_KEY: VirusTotal API密钥

    Returns:
        包含历史域名解析记录的JSON字符串
    """
    try:
        vt_api = f'https://www.virustotal.com/api/v3/ip_addresses/{ip}/resolutions'
        headers = {
            "Accept": "application/json",
            "x-apikey": VT_API_KEY,
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
        }

        response = requests.get(vt_api, headers=headers, timeout=30)
        if response.status_code != 200:
            return json.dumps({
                "error": f"请求VirusTotal接口出错，状态码: {response.status_code}",
                "status_code": response.status_code
            }, ensure_ascii=False)

        # 将响应转换为JSON字符串再传递给extract_domains_from_json
        json_str = json.dumps(response.json())
        result = extract_domains_from_json(json_str)

        # extract_domains_from_json现在始终返回JSON字符串
        return result

    except requests.exceptions.RequestException as e:
        return json.dumps({"error": f"网络请求异常: {str(e)}"}, ensure_ascii=False)
    except Exception as e:
        return json.dumps({"error": f"未知异常: {str(e)}"}, ensure_ascii=False)