# Encoding: utf-8
import asyncio
import socket
import random
import string
import json
import logging
from typing import List, Set, Dict, Optional, Tuple
from concurrent.futures import ThreadPoolExecutor, as_completed
import time

# 导入现有的subdomain工具
from .subfinder import run_subfinder
from .crt import crt_get_subdomains
from .virustotal import virustotal_get_subdomains
from .securitytrails import securitytrails_get_subdomains

logger = logging.getLogger(__name__)

class SubdomainValidator:
    """子域名验证器，负责DNS解析验证和泛解析检测"""
    
    def __init__(self, timeout: float = 3.0):
        self.timeout = timeout
        self.wildcard_cache = {}  # 缓存泛解析检测结果
    
    def _generate_random_subdomain(self, domain: str) -> str:
        """生成随机子域名用于泛解析检测"""
        random_str = ''.join(random.choices(string.ascii_lowercase + string.digits, k=12))
        return f"{random_str}.{domain}"
    
    def _resolve_domain(self, domain: str) -> Optional[str]:
        """解析域名获取IP地址"""
        try:
            socket.setdefaulttimeout(self.timeout)
            ip = socket.gethostbyname(domain)
            return ip
        except (socket.gaierror, socket.timeout, Exception):
            return None
    
    def detect_wildcard(self, domain: str) -> bool:
        """检测域名是否存在泛解析
        
        Args:
            domain: 主域名
            
        Returns:
            bool: True表示存在泛解析，False表示不存在
        """
        if domain in self.wildcard_cache:
            return self.wildcard_cache[domain]
        
        # 生成3个随机子域名进行测试
        test_subdomains = [self._generate_random_subdomain(domain) for _ in range(3)]
        resolved_ips = []
        
        for test_domain in test_subdomains:
            ip = self._resolve_domain(test_domain)
            if ip:
                resolved_ips.append(ip)
        
        # 如果有2个或以上随机子域名都能解析，认为存在泛解析
        has_wildcard = len(resolved_ips) >= 2
        self.wildcard_cache[domain] = has_wildcard
        
        logger.debug(f"Wildcard detection for {domain}: {'YES' if has_wildcard else 'NO'}")
        return has_wildcard
    
    def validate_subdomains(self, subdomains: List[str], main_domain: str, 
                          max_workers: int = 200) -> Tuple[List[str], List[str]]:
        """批量验证子域名的DNS解析
        
        Args:
            subdomains: 子域名列表
            main_domain: 主域名
            max_workers: 最大并发数
            
        Returns:
            Tuple[List[str], List[str]]: (有效子域名列表, 无效子域名列表)
        """
        valid_subdomains = []
        invalid_subdomains = []
        
        # 检测泛解析
        has_wildcard = self.detect_wildcard(main_domain)
        
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # 提交所有解析任务
            future_to_domain = {
                executor.submit(self._resolve_domain, subdomain): subdomain 
                for subdomain in subdomains
            }
            
            # 收集结果
            for future in as_completed(future_to_domain):
                subdomain = future_to_domain[future]
                try:
                    ip = future.result()
                    if ip:
                        # 如果存在泛解析，需要额外验证
                        if has_wildcard:
                            # 对于泛解析域名，我们仍然保留能解析的子域名
                            # 但会在结果中标注这是泛解析域名
                            valid_subdomains.append(subdomain)
                        else:
                            valid_subdomains.append(subdomain)
                    else:
                        invalid_subdomains.append(subdomain)
                except Exception as e:
                    logger.debug(f"Error resolving {subdomain}: {e}")
                    invalid_subdomains.append(subdomain)
        
        return valid_subdomains, invalid_subdomains


class ComprehensiveSubdomainDiscovery:
    """综合子域名发现工具"""
    
    def __init__(self, timeout: float = 3.0):
        self.validator = SubdomainValidator(timeout)
        self.results = {}
    
    def _parse_tool_result(self, result: any, tool_name: str) -> List[str]:
        """解析各个工具的返回结果，统一格式为域名列表"""
        subdomains = []
        
        try:
            if isinstance(result, str):
                # 尝试解析JSON格式
                try:
                    json_data = json.loads(result)
                    if isinstance(json_data, list):
                        subdomains = [item for item in json_data if isinstance(item, str)]
                    elif isinstance(json_data, dict):
                        # 处理可能的嵌套结构
                        if 'domains' in json_data:
                            subdomains = json_data['domains']
                        elif 'subdomains' in json_data:
                            subdomains = json_data['subdomains']
                except json.JSONDecodeError:
                    # 不是JSON格式，按行分割
                    lines = result.strip().split('\n')
                    subdomains = [line.strip() for line in lines if line.strip()]
            
            elif isinstance(result, list):
                subdomains = [item for item in result if isinstance(item, str)]
            
            # 过滤和清理域名
            cleaned_subdomains = []
            for subdomain in subdomains:
                subdomain = subdomain.strip().lower()
                # 基本格式验证
                if subdomain and '.' in subdomain and not subdomain.startswith('http'):
                    # 移除可能的协议前缀
                    if '://' in subdomain:
                        subdomain = subdomain.split('://', 1)[1]
                    # 移除可能的路径
                    if '/' in subdomain:
                        subdomain = subdomain.split('/', 1)[0]
                    # 移除端口号
                    if ':' in subdomain:
                        subdomain = subdomain.split(':', 1)[0]
                    
                    cleaned_subdomains.append(subdomain)
            
            logger.debug(f"{tool_name} found {len(cleaned_subdomains)} subdomains")
            return cleaned_subdomains
            
        except Exception as e:
            logger.error(f"Error parsing {tool_name} result: {e}")
            return []
    
    def run_all_tools(self, domain: str, vt_api_key: Optional[str] = None,
                           st_api_key: Optional[str] = None) -> Dict[str, List[str]]:
        """运行所有子域名发现工具
        
        Args:
            domain: 目标域名
            vt_api_key: VirusTotal API密钥
            st_api_key: SecurityTrails API密钥
            
        Returns:
            Dict[str, List[str]]: 各工具的发现结果
        """
        results = {}
        
        # 1. 运行 Subfinder
        try:
            logger.debug("Running Subfinder...")
            subfinder_result = run_subfinder(domain, "text")
            results['subfinder'] = self._parse_tool_result(subfinder_result, 'Subfinder')
        except Exception as e:
            logger.error(f"Subfinder failed: {e}")
            results['subfinder'] = []
        
        # 2. 运行 CRT.sh
        try:
            logger.debug("Running CRT.sh...")
            crt_result = crt_get_subdomains(domain)
            results['crt'] = self._parse_tool_result(crt_result, 'CRT.sh')
        except Exception as e:
            logger.error(f"CRT.sh failed: {e}")
            results['crt'] = []
        
        # 3. 运行 VirusTotal (如果有API密钥)
        if vt_api_key:
            try:
                logger.debug("Running VirusTotal...")
                vt_result = virustotal_get_subdomains(domain, vt_api_key)
                results['virustotal'] = self._parse_tool_result(vt_result, 'VirusTotal')
            except Exception as e:
                logger.error(f"VirusTotal failed: {e}")
                results['virustotal'] = []
        else:
            logger.debug("VirusTotal API key not provided, skipping...")
            results['virustotal'] = []
        
        # 4. 运行 SecurityTrails (如果有API密钥)
        if st_api_key:
            try:
                logger.debug("Running SecurityTrails...")
                st_result = securitytrails_get_subdomains(domain, st_api_key)
                results['securitytrails'] = self._parse_tool_result(st_result, 'SecurityTrails')
            except Exception as e:
                logger.error(f"SecurityTrails failed: {e}")
                results['securitytrails'] = []
        else:
            logger.debug("SecurityTrails API key not provided, skipping...")
            results['securitytrails'] = []
        
        return results
    
    def merge_and_deduplicate(self, tool_results: Dict[str, List[str]]) -> List[str]:
        """合并和去重所有工具的结果"""
        all_subdomains = set()
        
        for tool_name, subdomains in tool_results.items():
            for subdomain in subdomains:
                all_subdomains.add(subdomain.lower().strip())
        
        return sorted(list(all_subdomains))


def comprehensive_subdomain_discovery(
    domain: str,
    vt_api_key: Optional[str] = None,
    st_api_key: Optional[str] = None,
    timeout: float = 3.0
) -> str:
    """综合子域名发现主函数
    
    Args:
        domain: 目标域名
        vt_api_key: VirusTotal API密钥
        st_api_key: SecurityTrails API密钥  
        timeout: DNS解析超时时间
        
    Returns:
        str: JSON格式的发现结果
    """
    discovery = ComprehensiveSubdomainDiscovery(timeout)
    
    try:
        # 1. 运行所有工具
        logger.info(f"Starting comprehensive subdomain discovery for {domain}")
        tool_results = discovery.run_all_tools(domain, vt_api_key, st_api_key)
        
        # 2. 合并和去重
        all_subdomains = discovery.merge_and_deduplicate(tool_results)
        logger.debug(f"Found {len(all_subdomains)} unique subdomains before validation")
        
        # 3. DNS验证 (可选)
        valid_subdomains = []
        invalid_subdomains = []
        has_wildcard = False
        
        if all_subdomains:
            logger.debug("Starting DNS validation...")
            valid_subdomains, invalid_subdomains = discovery.validator.validate_subdomains(
                all_subdomains, domain
            )
            has_wildcard = discovery.validator.detect_wildcard(domain)
        else:
            valid_subdomains = all_subdomains
        
        # 4. 生成结果报告
        result = {
            "domain": domain,
            "wildcard_detected": has_wildcard,
            "summary": {
                "total_found": len(all_subdomains),
                "valid_subdomains": len(valid_subdomains),
                "invalid_subdomains": len(invalid_subdomains),
            },
            "subdomains": {
                "valid": sorted(valid_subdomains),
            }
        }
        
        return json.dumps(result, ensure_ascii=False, indent=2)
        
    except Exception as e:
        logger.error(f"Comprehensive subdomain discovery failed: {e}")
        return json.dumps({
            "error": f"Comprehensive subdomain discovery failed: {str(e)}",
            "domain": domain
        }, ensure_ascii=False)
