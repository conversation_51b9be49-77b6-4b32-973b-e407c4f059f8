# Encdoding: utf-8
import requests
def virustotal_get_subdomains(domain: str, VT_API_KEY: str):
    """使用 VirusTotal API 获取子域名信息

    通过 VirusTotal 的威胁情报数据库查询目标域名的子域名记录。

    参数:
        domain: 目标域名
        VT_API_KEY: VirusTotal API 密钥

    返回:
        list 或 str: 成功时返回子域名列表，失败时返回错误信息
    """
    vt_api = f"https://www.virustotal.com/api/v3/domains/{domain}/subdomains"
    headers = {
        "Accept": "application/json",
        "x-apikey": VT_API_KEY
    }

    response = requests.get(vt_api, headers=headers)
    if response.status_code != 200:
        return "请求virustotal接口出错"
    else:
        domains = list({item["id"] for item in response.json()["data"]})
        return domains
    