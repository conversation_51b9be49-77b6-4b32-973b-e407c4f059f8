# Encdoding: utf-8
from typing import Optional

import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent.parent.parent))
from utils import exec


def run_subfinder(
    domain: str,
    output_format: Optional[str] = "text",
) -> str:
    """Run subfinder to enumerate subdomains.
    
    Args:
        domain: Target domain to enumerate
        output_format: Output format (text or json)
    
    Returns:
        str: Subfinder output
    """
    if output_format == "json":
        return exec.run_executable(['subfinder', "-d", domain, '-silent', '-oJ'])
    else:
        return exec.run_executable(['subfinder', "-d", domain, '-silent'])