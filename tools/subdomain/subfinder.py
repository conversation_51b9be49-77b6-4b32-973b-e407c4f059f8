# Encdoding: utf-8
from typing import Optional

import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent.parent.parent))
from utils import exec


def run_subfinder(
    domain: str,
    output_format: Optional[str] = "text",
) -> str:
    """运行 Subfinder 工具进行子域名枚举

    使用 Subfinder 工具对目标域名进行子域名发现和枚举。

    参数:
        domain: 目标域名
        output_format: 输出格式，可选 "text" 或 "json"

    返回:
        str: Subfinder 的枚举结果
    """
    if output_format == "json":
        return exec.run_executable(['subfinder', "-d", domain, '-silent', '-oJ'])
    else:
        return exec.run_executable(['subfinder', "-d", domain, '-silent'])