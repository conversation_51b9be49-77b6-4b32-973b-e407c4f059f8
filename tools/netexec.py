# Encdoding: utf-8
from utils.exec import run_executable as exec

def run_netexec(
    protocol:str,
    ip:str,
    port:str,
    username:str,
    password:str,
    command:str
) -> str:

    if protocol not in ["winrm", "ssh", "smb", "wmi", "mssql"]:
        return(f"Unsupported protocol: {protocol}")
    elif protocol == "mssql":
        cmd = ["netexec", "mssql", ip,
            "--port", port,
            "-u", username,
            "-p", password,
            "--local-auth",
            "-q", command]
    else:
        cmd = ["netexec", protocol, ip,
            "--port", port,
            "-u", username,
            "-p", password,
            "-x", command]
    return exec(cmd)
