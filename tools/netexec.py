# Encdoding: utf-8
from utils.exec import run_executable as exec

def run_netexec(
    protocol:str,
    ip:str,
    port:str,
    username:str,
    password:str,
    command:str
) -> str:
    """运行 NetExec 工具进行网络服务利用

    使用 NetExec 工具通过指定协议连接到目标服务并执行命令，支持多种网络协议。

    参数:
        protocol: 连接协议（winrm、ssh、smb、wmi、mssql）
        ip: 目标 IP 地址
        port: 目标端口号
        username: 登录用户名
        password: 登录密码
        command: 要执行的命令

    返回:
        str: 命令执行结果或错误信息
    """

    if protocol not in ["winrm", "ssh", "smb", "wmi", "mssql"]:
        return(f"Unsupported protocol: {protocol}")
    elif protocol == "mssql":
        cmd = ["netexec", "mssql", ip,
            "--port", port,
            "-u", username,
            "-p", password,
            "--local-auth",
            "-q", command]
    else:
        cmd = ["netexec", protocol, ip,
            "--port", port,
            "-u", username,
            "-p", password,
            "-x", command]
    return exec(cmd)
