# Encoding: utf-8
import requests
import json

def ipasn(ip: str, key: str) -> str:
    """
    查询IP所属组织信息

    Args:
        ip: IP地址字符串
        key: IPinfo.io API密钥

    Returns:
        包含IP所属组织信息的JSON字符串
    """
    try:
        ip_asn = f"https://ipinfo.io/{ip}?token={key}"
        headers = {
            "Accept": "application/json",
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
        }

        response = requests.get(ip_asn, headers=headers, timeout=10)
        if response.status_code != 200:
            return json.dumps({"error": "请求ipinfo接口出错", "status_code": response.status_code}, ensure_ascii=False)

        # 解析响应数据
        data = response.json()
        return json.dumps(data, ensure_ascii=False)

    except requests.exceptions.RequestException as e:
        return json.dumps({"error": f"网络请求异常: {str(e)}"}, ensure_ascii=False)
    except json.JSONDecodeError as e:
        return json.dumps({"error": f"JSON解析异常: {str(e)}"}, ensure_ascii=False)
    except Exception as e:
        return json.dumps({"error": f"未知异常: {str(e)}"}, ensure_ascii=False)
    