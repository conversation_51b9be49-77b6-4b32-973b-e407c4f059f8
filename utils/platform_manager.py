# Encoding: utf-8
import platform
import os
from pathlib import Path
from typing import Tuple, Optional
import logging

logger = logging.getLogger(__name__)

class PlatformManager:
    """跨平台工具管理器 - 负责平台检测和工具路径解析"""
    
    def __init__(self):
        """初始化平台管理器"""
        self.os_name, self.arch = self.detect_platform()
        self.bin_dir = Path(__file__).parent.parent / "bin"
        self.platform_dir = self.bin_dir / self.os_name / self.arch
        
        logger.debug(f"平台检测结果: {self.os_name}/{self.arch}")
        logger.debug(f"工具目录: {self.platform_dir}")
    
    def detect_platform(self) -> Tuple[str, str]:
        """
        检测操作系统和架构
        
        Returns:
            Tuple[str, str]: (操作系统, 架构) 例如 ('linux', 'amd64')
        """
        # 检测操作系统
        system = platform.system().lower()
        if system == 'darwin':
            os_name = 'darwin'  # macOS
        elif system == 'linux':
            os_name = 'linux'
        elif system == 'windows':
            os_name = 'windows'
        else:
            logger.warning(f"未知操作系统: {system}, 使用 linux 作为默认值")
            os_name = 'linux'
        
        # 检测架构
        machine = platform.machine().lower()
        if machine in ['x86_64', 'amd64']:
            arch = 'amd64'
        elif machine in ['arm64', 'aarch64']:
            arch = 'arm64'
        elif machine in ['i386', 'i686']:
            arch = '386'  # 32位系统
        else:
            logger.warning(f"未知架构: {machine}, 使用 amd64 作为默认值")
            arch = 'amd64'
        
        return os_name, arch
    
    def get_executable_path(self, tool_name: str) -> Optional[Path]:
        """
        获取工具的可执行文件路径
        
        Args:
            tool_name: 工具名称，如 'nuclei', 'httpx'
            
        Returns:
            Optional[Path]: 工具的完整路径，如果不存在返回 None
        """
        # 在 Windows 系统下，可执行文件需要添加 .exe 扩展名
        if self.os_name == 'windows' and not tool_name.endswith('.exe'):
            executable_name = f"{tool_name}.exe"
        else:
            executable_name = tool_name
        
        # 构建平台特定的工具路径
        tool_path = self.platform_dir / executable_name
        
        # 检查文件是否存在且可执行
        if tool_path.exists() and tool_path.is_file():
            # 在 Unix 系统上检查执行权限
            if self.os_name != 'windows':
                if not os.access(tool_path, os.X_OK):
                    logger.warning(f"工具文件存在但没有执行权限: {tool_path}")
                    return None
            return tool_path
        
        logger.debug(f"工具不存在: {tool_path}")
        return None
    
    def is_tool_available(self, tool_name: str) -> bool:
        """
        检查工具是否可用
        
        Args:
            tool_name: 工具名称
            
        Returns:
            bool: 工具是否可用
        """
        return self.get_executable_path(tool_name) is not None
    
    def get_platform_info(self) -> dict:
        """
        获取平台信息
        
        Returns:
            dict: 包含平台详细信息的字典
        """
        return {
            'os': self.os_name,
            'arch': self.arch,
            'platform_dir': str(self.platform_dir),
            'python_platform': platform.platform(),
            'python_version': platform.python_version()
        }
    
    def list_available_tools(self) -> list:
        """
        列出当前平台可用的工具
        
        Returns:
            list: 可用工具列表
        """
        available_tools = []
        
        if not self.platform_dir.exists():
            logger.warning(f"平台目录不存在: {self.platform_dir}")
            return available_tools
        
        # 遍历平台目录中的所有文件
        for file_path in self.platform_dir.iterdir():
            if file_path.is_file():
                # 移除 Windows 系统的 .exe 扩展名
                tool_name = file_path.stem if file_path.suffix == '.exe' else file_path.name
                
                # 检查是否有执行权限（Unix 系统）
                if self.os_name != 'windows':
                    if os.access(file_path, os.X_OK):
                        available_tools.append(tool_name)
                else:
                    available_tools.append(tool_name)
        
        return sorted(available_tools)
    
    def validate_environment(self) -> dict:
        """
        验证运行环境
        
        Returns:
            dict: 验证结果
        """
        result = {
            'platform_supported': True,
            'platform_dir_exists': self.platform_dir.exists(),
            'available_tools': self.list_available_tools(),
            'total_tools': len(self.list_available_tools()),
            'platform_info': self.get_platform_info()
        }
        
        # 检查是否支持当前平台
        if not self.platform_dir.exists():
            result['platform_supported'] = False
            logger.error(f"不支持的平台: {self.os_name}/{self.arch}")
        
        return result


# 创建全局平台管理器实例
_platform_manager = None

def get_platform_manager() -> PlatformManager:
    """
    获取全局平台管理器实例（单例模式）
    
    Returns:
        PlatformManager: 平台管理器实例
    """
    global _platform_manager
    if _platform_manager is None:
        _platform_manager = PlatformManager()
    return _platform_manager