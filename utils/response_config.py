# Encoding: utf-8
"""
MCP响应处理配置管理模块
支持环境变量、配置文件等多种配置方式
"""

import os
import yaml
import logging
from typing import Dict, Any, Optional
from dataclasses import dataclass, asdict
from pathlib import Path

logger = logging.getLogger(__name__)

@dataclass
class ResponseConfig:
    """响应处理配置类"""
    # 数据大小阈值 (字节) - 统一阈值，既是直接返回的上限，也是截断后的大小
    threshold: int = 100_000           # 100KB - 小于此值直接返回，大于此值截断到此大小
    preserve_structure: bool = True     # 保持JSON结构完整性

    # 性能配置
    max_processing_time_ms: int = 30_000  # 30秒最大处理时间

    # 调试配置
    debug_mode: bool = False            # 调试模式
    log_processing_stats: bool = True   # 记录处理统计

class ResponseConfigManager:
    """响应配置管理器"""
    
    def __init__(self, config_file: Optional[str] = None):
        self.config_file = config_file
        self._config = None
        self._load_config()
    
    def _load_config(self) -> None:
        """加载配置"""
        # 默认配置
        config_dict = asdict(ResponseConfig())
        
        # 从配置文件加载
        if self.config_file and Path(self.config_file).exists():
            try:
                config_dict.update(self._load_from_file())
                logger.info(f"Loaded response config from {self.config_file}")
            except Exception as e:
                logger.warning(f"Failed to load config file {self.config_file}: {e}")
        
        # 从环境变量加载
        config_dict.update(self._load_from_env())
        
        # 创建配置对象
        self._config = ResponseConfig(**config_dict)
        
        # 验证配置
        self._validate_config()
    
    def _load_from_file(self) -> Dict[str, Any]:
        """从配置文件加载"""
        config_dict = {}
        
        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                if self.config_file.endswith('.yaml') or self.config_file.endswith('.yml'):
                    file_config = yaml.safe_load(f)
                    # 提取响应处理相关配置
                    if 'response_handling' in file_config:
                        config_dict = file_config['response_handling']
                    elif 'mcp_response' in file_config:
                        config_dict = file_config['mcp_response']
                else:
                    logger.warning(f"Unsupported config file format: {self.config_file}")
        
        except Exception as e:
            logger.error(f"Error loading config file: {e}")
            raise
        
        return config_dict
    
    def _load_from_env(self) -> Dict[str, Any]:
        """从环境变量加载配置"""
        config_dict = {}
        
        # 环境变量映射
        env_mappings = {
            'MCP_THRESHOLD': ('threshold', int),
            'MCP_PRESERVE_STRUCTURE': ('preserve_structure', self._str_to_bool),
            'MCP_MAX_PROCESSING_TIME_MS': ('max_processing_time_ms', int),
            'MCP_DEBUG_MODE': ('debug_mode', self._str_to_bool),
            'MCP_LOG_PROCESSING_STATS': ('log_processing_stats', self._str_to_bool),
        }
        
        for env_var, (config_key, converter) in env_mappings.items():
            value = os.getenv(env_var)
            if value is not None:
                try:
                    config_dict[config_key] = converter(value)
                    logger.debug(f"Loaded {config_key} from environment: {value}")
                except (ValueError, TypeError) as e:
                    logger.warning(f"Invalid value for {env_var}: {value}, error: {e}")
        
        return config_dict
    
    @staticmethod
    def _str_to_bool(value: str) -> bool:
        """字符串转布尔值"""
        return value.lower() in ('true', '1', 'yes', 'on', 'enabled')
    
    def _validate_config(self) -> None:
        """验证配置有效性"""
        config = self._config

        # 验证阈值
        if config.threshold <= 0:
            raise ValueError("Threshold must be positive")

        # 验证处理时间
        if config.max_processing_time_ms <= 0:
            raise ValueError("Max processing time must be positive")

        logger.info("Response configuration validated successfully")
    
    def get_config(self) -> ResponseConfig:
        """获取配置对象"""
        return self._config
    
    def update_config(self, **kwargs) -> None:
        """更新配置"""
        config_dict = asdict(self._config)
        config_dict.update(kwargs)
        
        # 重新创建配置对象
        self._config = ResponseConfig(**config_dict)
        
        # 重新验证
        self._validate_config()
        
        logger.info(f"Configuration updated: {kwargs}")
    
    def save_config(self, file_path: str) -> None:
        """保存配置到文件"""
        try:
            config_dict = {
                'response_handling': asdict(self._config)
            }
            
            with open(file_path, 'w', encoding='utf-8') as f:
                yaml.dump(config_dict, f, default_flow_style=False, 
                         allow_unicode=True, indent=2)
            
            logger.info(f"Configuration saved to {file_path}")
            
        except Exception as e:
            logger.error(f"Failed to save configuration: {e}")
            raise
    
    def get_summary(self) -> Dict[str, Any]:
        """获取配置摘要"""
        config = self._config
        return {
            'strategy': {
                'direct_threshold': f"{config.direct_threshold:,} bytes ({config.direct_threshold/1024:.0f}KB)",
                'description': '数据 ≤ 200KB: 直接返回, 数据 > 200KB: 截断处理'
            },
            'truncation': {
                'size': f"{config.truncate_size:,} bytes ({config.truncate_size/1024:.0f}KB)",
                'preserve_structure': config.preserve_structure
            },
            'performance': {
                'max_processing_time': f"{config.max_processing_time_ms:,} ms"
            },
            'debug': {
                'debug_mode': config.debug_mode,
                'log_stats': config.log_processing_stats
            }
        }

# 全局配置管理器实例
_global_config_manager: Optional[ResponseConfigManager] = None

def get_response_config_manager(config_file: Optional[str] = None) -> ResponseConfigManager:
    """获取全局配置管理器实例"""
    global _global_config_manager
    
    if _global_config_manager is None:
        # 默认配置文件路径
        if config_file is None:
            config_file = os.getenv('MCP_RESPONSE_CONFIG', 'config/response_config.yaml')
        
        _global_config_manager = ResponseConfigManager(config_file)
    
    return _global_config_manager

def get_response_config() -> ResponseConfig:
    """获取响应配置对象"""
    return get_response_config_manager().get_config()

# 便捷函数
def set_response_config(**kwargs) -> None:
    """设置响应配置"""
    get_response_config_manager().update_config(**kwargs)

def get_config_summary() -> Dict[str, Any]:
    """获取配置摘要"""
    return get_response_config_manager().get_summary()
