# -*- coding: utf-8 -*-
"""
工作流执行器
提供固定的渗透测试工作流，减少AI选择的随机性
"""

import asyncio
import logging
import re
import yaml
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from enum import Enum

logger = logging.getLogger(__name__)

class WorkflowStatus(Enum):
    """工作流状态枚举"""
    PENDING = "pending"
    RUNNING = "running"
    SUCCESS = "success"
    FAILED = "failed"
    SKIPPED = "skipped"

@dataclass
class WorkflowStep:
    """工作流步骤数据类"""
    step: int
    tool: str
    description: str
    required_params: List[str]
    default_params: Dict[str, Any]
    depends_on: List[int]
    condition: Optional[str] = None
    cascade_workflow: Optional[str] = None
    status: WorkflowStatus = WorkflowStatus.PENDING
    result: Optional[str] = None
    error: Optional[str] = None

@dataclass
class WorkflowResult:
    """工作流执行结果"""
    workflow_name: str
    target: str
    status: WorkflowStatus
    steps: List[WorkflowStep]
    execution_time: float
    summary: Dict[str, Any]

class WorkflowExecutor:
    """工作流执行器"""
    
    def __init__(self, config_path: str = "config/workflow_config.yaml"):
        self.config_path = config_path
        self.workflows = {}
        self.parameter_templates = {}
        self.target_detection = {}
        self.load_config()
        
    def load_config(self):
        """加载工作流配置"""
        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
                
            self.workflows = config.get('workflows', {})
            self.parameter_templates = config.get('parameter_templates', {})
            self.target_detection = config.get('target_detection', {})
            
            logger.info(f"已加载 {len(self.workflows)} 个工作流配置")
            
        except Exception as e:
            logger.error(f"加载工作流配置失败: {e}")
            raise
    
    def detect_target_type(self, target: str) -> Tuple[str, str]:
        """
        检测目标类型并推荐工作流
        
        Args:
            target: 目标地址
            
        Returns:
            (target_type, recommended_workflow)
        """
        # URL模式检测
        for pattern_config in self.target_detection.get('url_patterns', []):
            if re.match(pattern_config['pattern'], target):
                return pattern_config['type'], pattern_config['workflow']
        
        # IP模式检测
        for pattern_config in self.target_detection.get('ip_patterns', []):
            if re.match(pattern_config['pattern'], target):
                return pattern_config['type'], pattern_config['workflow']
                
        # 域名模式检测
        for pattern_config in self.target_detection.get('domain_patterns', []):
            if re.match(pattern_config['pattern'], target):
                return pattern_config['type'], pattern_config['workflow']
        
        return "unknown", "web_pentest"  # 默认使用web测试流程
    
    def get_available_workflows(self) -> Dict[str, str]:
        """获取可用的工作流列表"""
        return {
            name: config['description'] 
            for name, config in self.workflows.items()
        }
    
    def prepare_workflow_steps(self, workflow_name: str) -> List[WorkflowStep]:
        """准备工作流步骤"""
        if workflow_name not in self.workflows:
            raise ValueError(f"工作流 '{workflow_name}' 不存在")
            
        workflow_config = self.workflows[workflow_name]
        steps = []
        
        for step_config in workflow_config['steps']:
            step = WorkflowStep(
                step=step_config['step'],
                tool=step_config['tool'],
                description=step_config['description'],
                required_params=step_config['required_params'],
                default_params=step_config.get('default_params', {}),
                depends_on=step_config.get('depends_on', []),
                condition=step_config.get('condition'),
                cascade_workflow=step_config.get('cascade_workflow')
            )
            steps.append(step)
            
        return sorted(steps, key=lambda x: x.step)
    
    def build_tool_parameters(self, step: WorkflowStep, target: str, 
                            context: Dict[str, Any]) -> Dict[str, Any]:
        """
        构建工具参数
        
        Args:
            step: 工作流步骤
            target: 目标地址
            context: 执行上下文
            
        Returns:
            工具参数字典
        """
        params = {}
        
        # 设置必需参数
        for param_name in step.required_params:
            if param_name in ['target', 'url', 'host']:
                params[param_name] = target
            elif param_name == 'domain':
                # 从URL或主机名提取域名
                if target.startswith(('http://', 'https://')):
                    from urllib.parse import urlparse
                    parsed = urlparse(target)
                    params[param_name] = parsed.netloc
                else:
                    params[param_name] = target
            elif param_name == 'ip':
                # 对于需要IP参数的工具，使用目标地址
                params[param_name] = target
            elif param_name == 'port':
                # 对于需要端口参数的工具，从上下文中提取第一个发现的端口
                ports = self._extract_ports_from_context(context)
                if ports:
                    params[param_name] = str(ports[0])  # 使用第一个端口进行测试
                else:
                    params[param_name] = "80"  # 默认端口
            elif param_name in context:
                params[param_name] = context[param_name]
        
        # 设置默认参数
        params.update(step.default_params)
        
        # 应用参数模板
        if 'headers' in params and params['headers'] == 'common':
            params['headers'] = self.parameter_templates.get('common_headers', {})
            
        return params
    
    def _extract_ports_from_context(self, context: Dict[str, Any]) -> List[int]:
        """从上下文中提取端口列表"""
        ports = []
        
        for key, value in context.items():
            if key.startswith('step_') and 'result' in key:
                result_str = str(value)
                
                # 解析rustscan结果格式: "IP -> [port1,port2,port3]"
                import re
                
                # 匹配方括号内的端口列表
                port_matches = re.findall(r'\[([0-9,\s]+)\]', result_str)
                for match in port_matches:
                    port_list = [int(p.strip()) for p in match.split(',') if p.strip().isdigit()]
                    ports.extend(port_list)
                
                # 也尝试匹配单独的端口号格式
                single_port_matches = re.findall(r'port[:\s]+(\d+)', result_str, re.IGNORECASE)
                for port_str in single_port_matches:
                    try:
                        ports.append(int(port_str))
                    except ValueError:
                        continue
                
                # 匹配 "IP:PORT" 格式
                ip_port_matches = re.findall(r'\d+\.\d+\.\d+\.\d+:(\d+)', result_str)
                for port_str in ip_port_matches:
                    try:
                        ports.append(int(port_str))
                    except ValueError:
                        continue
        
        unique_ports = sorted(list(set(ports)))
        logger.debug(f"从上下文中提取到端口: {unique_ports}")
        return unique_ports
    
    def check_step_condition(self, step: WorkflowStep, context: Dict[str, Any]) -> bool:
        """检查步骤执行条件"""
        if not step.condition:
            return True
            
        if step.condition == "if_https":
            target = context.get('target', '')
            return target.startswith('https://')
        elif step.condition == "success":
            return True  # 默认条件
        elif step.condition == "web_services_found":
            # 检查是否发现了Web服务
            return self._check_web_services_in_context(context)
        elif step.condition == "alive_web_services_found":
            # 检查是否发现了存活的Web服务
            return self._check_alive_web_services_in_context(context)
        elif step.condition == "resolved_ips_found":
            # 检查是否解析到了IP地址
            return self._check_resolved_ips_in_context(context)
        elif step.condition == "potential_web_ports_found":
            # 检查是否发现了潜在的Web端口
            return self._check_potential_web_ports_in_context(context)
        elif step.condition == "verified_web_services_found":
            # 检查是否验证了Web服务
            return self._check_verified_web_services_in_context(context)
            
        return True
    
    def _check_web_services_in_context(self, context: Dict[str, Any]) -> bool:
        """检查上下文中是否发现了Web服务"""
        # 从端口扫描结果中提取端口
        discovered_ports = self._extract_ports_from_context(context)
        
        # 常见的Web端口
        web_ports = [80, 443, 8080, 8443, 3000, 5000, 8000, 9000, 8888, 9090]
        
        # 检查是否有Web端口
        for port in discovered_ports:
            if port in web_ports:
                logger.info(f"发现Web端口: {port}")
                return True
        
        # 额外检查服务指纹识别结果中的Web服务
        for key, value in context.items():
            if key.startswith('step_') and 'result' in key:
                result_str = str(value).lower()
                # 检查服务类型指示器
                web_service_indicators = [
                    'http', 'https', 'web', 'apache', 'nginx', 'iis',
                    'tomcat', 'jetty', 'lighttpd', 'caddy'
                ]
                if any(indicator in result_str for indicator in web_service_indicators):
                    logger.info(f"在服务指纹中发现Web服务指示器")
                    return True
        
        logger.info(f"未发现Web服务，扫描到的端口: {discovered_ports}")
        return False
    
    def _check_alive_web_services_in_context(self, context: Dict[str, Any]) -> bool:
        """检查上下文中是否发现了存活的Web服务"""
        # 检查httpx结果中是否有存活的Web服务
        for key, value in context.items():
            if key.startswith('step_') and 'result' in key:
                result_str = str(value).lower()
                # 检查HTTP响应状态码
                alive_indicators = [
                    '200', '301', '302', '403', '404', '500',
                    'status_code', 'content-length', 'server:'
                ]
                if any(indicator in result_str for indicator in alive_indicators):
                    return True
        return False
    
    def _check_resolved_ips_in_context(self, context: Dict[str, Any]) -> bool:
        """检查上下文中是否有解析的IP地址"""
        resolved_ips = self._extract_resolved_ips_from_context(context)
        return len(resolved_ips) > 0
    
    def _check_potential_web_ports_in_context(self, context: Dict[str, Any]) -> bool:
        """检查上下文中是否发现了需要验证的端口"""
        # 从端口扫描结果中提取所有端口
        discovered_ports = self._extract_ports_from_context(context)
        
        if not discovered_ports:
            logger.info("未发现任何开放端口")
            return False
        
        # 排除明显不可能是Web服务的端口
        excluded_ports = [
            22,    # SSH
            23,    # Telnet
            25,    # SMTP
            53,    # DNS
            110,   # POP3
            143,   # IMAP
            993,   # IMAPS
            995,   # POP3S
            21,    # FTP
            20,    # FTP Data
            111,   # RPC
            135,   # RPC
            139,   # NetBIOS
            445,   # SMB
            1433,  # MSSQL
            3306,  # MySQL
            5432,  # PostgreSQL
            6379,  # Redis
            27017, # MongoDB
        ]
        
        # 检查是否有需要验证的端口（排除明显的非Web端口）
        ports_to_verify = [port for port in discovered_ports if port not in excluded_ports]
        
        if ports_to_verify:
            logger.info(f"发现需要Web验证的端口: {ports_to_verify}")
            logger.info(f"排除的明显非Web端口: {[p for p in discovered_ports if p in excluded_ports]}")
            return True
        else:
            logger.info(f"所有发现的端口都是明显的非Web服务: {discovered_ports}")
            return False
    
    def _check_verified_web_services_in_context(self, context: Dict[str, Any]) -> bool:
        """检查上下文中是否有已验证的Web服务"""
        # 检查httpx验证结果
        for key, value in context.items():
            if key.startswith('step_') and 'result' in key:
                result_str = str(value).lower()
                # 检查HTTP响应状态码和Web服务指示器
                web_indicators = [
                    'status_code', 'content-length', 'server:', 'content-type',
                    '200', '301', '302', '403', '404', '500', '502', '503',
                    'http/1.1', 'http/2', 'nginx', 'apache', 'iis'
                ]
                if any(indicator in result_str for indicator in web_indicators):
                    logger.info("发现已验证的Web服务")
                    return True
        
        logger.info("未发现已验证的Web服务")
        return False
    
    def _resolve_subdomains_to_ips(self, subdomains: List[str]) -> Dict[str, str]:
        """将子域名解析为IP地址"""
        import socket
        resolved_ips = {}
        
        for subdomain in subdomains:
            try:
                # 去除协议前缀
                clean_domain = subdomain.replace('http://', '').replace('https://', '').split('/')[0]
                ip = socket.gethostbyname(clean_domain)
                resolved_ips[subdomain] = ip
                logger.info(f"DNS解析: {clean_domain} -> {ip}")
            except socket.gaierror:
                logger.warning(f"DNS解析失败: {clean_domain}")
                continue
        
        return resolved_ips
    
    def _extract_subdomains_from_context(self, context: Dict[str, Any]) -> List[str]:
        """从上下文中提取子域名列表"""
        subdomains = []
        
        for key, value in context.items():
            if key.startswith('step_') and 'result' in key:
                result_str = str(value)
                
                # 解析子域名发现结果
                import re
                
                # 匹配域名格式
                domain_matches = re.findall(r'[a-zA-Z0-9][a-zA-Z0-9-]{1,61}[a-zA-Z0-9]\.[a-zA-Z]{2,}', result_str)
                subdomains.extend(domain_matches)
                
                # 匹配JSON格式的子域名列表
                try:
                    import json
                    if result_str.strip().startswith('{') or result_str.strip().startswith('['):
                        data = json.loads(result_str)
                        if isinstance(data, dict) and 'subdomains' in data:
                            subdomains.extend(data['subdomains'])
                        elif isinstance(data, list):
                            subdomains.extend([item for item in data if isinstance(item, str)])
                except:
                    pass
        
        # 去重并返回
        return list(set(subdomains))
    
    def _extract_resolved_ips_from_context(self, context: Dict[str, Any]) -> List[str]:
        """从上下文中提取解析的IP地址"""
        ips = []
        
        for key, value in context.items():
            if key.startswith('step_') and 'result' in key:
                result_str = str(value)
                
                # 解析IP地址
                import re
                ip_matches = re.findall(r'\b(?:\d{1,3}\.){3}\d{1,3}\b', result_str)
                ips.extend(ip_matches)
        
        # 去重并过滤私有IP（可选）
        unique_ips = list(set(ips))
        return unique_ips
    
    def _extract_web_targets_from_context(self, context: Dict[str, Any]) -> List[str]:
        """从上下文中提取Web目标"""
        web_targets = []
        target_ip = context.get('target', '')
        
        # 从端口扫描结果中提取Web端口
        discovered_ports = self._extract_ports_from_context(context)
        web_ports = [80, 443, 8080, 8443, 3000, 5000, 8000, 9000, 8888, 9090]
        
        # 根据发现的Web端口构建URL
        for port in discovered_ports:
            if port in web_ports:
                if port in [443, 8443, 9443]:
                    web_targets.append(f"https://{target_ip}:{port}")
                else:
                    web_targets.append(f"http://{target_ip}:{port}")
        
        # 从扫描结果中查找已存在的URL
        for key, value in context.items():
            if key.startswith('step_') and 'result' in key:
                result_str = str(value)
                
                # 匹配存活的HTTP/HTTPS URL
                import re
                url_matches = re.findall(r'https?://[^\s\'"<>]+', result_str)
                web_targets.extend(url_matches)
                
                # 匹配IP:端口格式并构建URL
                ip_port_matches = re.findall(r'(\d+\.\d+\.\d+\.\d+):(\d+)', result_str)
                for ip, port in ip_port_matches:
                    port_num = int(port)
                    if port_num in web_ports:
                        if port_num in [443, 8443, 9443]:
                            web_targets.append(f"https://{ip}:{port}")
                        else:
                            web_targets.append(f"http://{ip}:{port}")
        
        # 去重并返回
        unique_targets = list(set(web_targets))
        logger.info(f"提取的Web目标: {unique_targets}")
        return unique_targets
    
    def check_dependencies(self, step: WorkflowStep, completed_steps: List[int]) -> bool:
        """检查步骤依赖"""
        return all(dep in completed_steps for dep in step.depends_on)
    
    async def execute_workflow(self, workflow_name: str, target: str, 
                             tool_executor, **kwargs) -> WorkflowResult:
        """
        执行工作流
        
        Args:
            workflow_name: 工作流名称
            target: 目标地址
            tool_executor: 工具执行器实例
            **kwargs: 额外参数
            
        Returns:
            工作流执行结果
        """
        import time
        start_time = time.time()
        
        logger.info(f"开始执行工作流: {workflow_name}, 目标: {target}")
        
        try:
            steps = self.prepare_workflow_steps(workflow_name)
            completed_steps = []
            context = {'target': target, **kwargs}
            
            for step in steps:
                logger.info(f"执行步骤 {step.step}: {step.description}")
                
                # 检查依赖
                if not self.check_dependencies(step, completed_steps):
                    step.status = WorkflowStatus.SKIPPED
                    step.error = "依赖步骤未完成"
                    logger.warning(f"步骤 {step.step} 跳过: 依赖未满足")
                    continue
                
                # 检查条件
                if not self.check_step_condition(step, context):
                    step.status = WorkflowStatus.SKIPPED
                    step.error = "执行条件不满足"
                    logger.info(f"步骤 {step.step} 跳过: 条件不满足")
                    continue
                
                # 构建参数
                try:
                    # 检查是否是级联测试步骤
                    if step.tool == "cascade_web_test":
                        # 执行级联Web测试
                        cascade_result = await self._execute_cascade_web_test(
                            step, context, tool_executor, kwargs
                        )
                        step.result = cascade_result
                        step.status = WorkflowStatus.SUCCESS
                        completed_steps.append(step.step)
                        context[f'step_{step.step}_result'] = cascade_result
                        logger.info(f"级联Web测试步骤 {step.step} 完成")
                        continue
                    elif step.tool == "cascade_network_test":
                        # 执行级联网络测试
                        cascade_result = await self._execute_cascade_network_test(
                            step, context, tool_executor, kwargs
                        )
                        step.result = cascade_result
                        step.status = WorkflowStatus.SUCCESS
                        completed_steps.append(step.step)
                        context[f'step_{step.step}_result'] = cascade_result
                        logger.info(f"级联网络测试步骤 {step.step} 完成")
                        continue
                    elif step.tool == "dns_resolution":
                        # 执行DNS解析
                        dns_result = await self._execute_dns_resolution(step, context)
                        step.result = dns_result
                        step.status = WorkflowStatus.SUCCESS
                        completed_steps.append(step.step)
                        context[f'step_{step.step}_result'] = dns_result
                        logger.info(f"DNS解析步骤 {step.step} 完成")
                        continue
                    elif step.tool == "httpx_web_verification":
                        # 执行Web服务验证
                        verification_result = await self._execute_web_verification(
                            step, context, tool_executor
                        )
                        step.result = verification_result
                        step.status = WorkflowStatus.SUCCESS
                        completed_steps.append(step.step)
                        context[f'step_{step.step}_result'] = verification_result
                        logger.info(f"Web服务验证步骤 {step.step} 完成")
                        continue
                    
                    params = self.build_tool_parameters(step, target, context)
                    logger.debug(f"步骤 {step.step} 参数: {params}")
                    
                    # 执行工具
                    step.status = WorkflowStatus.RUNNING
                    
                    # 通过MCP工具调用代理执行工具
                    result = await self._call_mcp_tool(tool_executor, step.tool, params)
                    step.result = result
                    step.status = WorkflowStatus.SUCCESS
                    completed_steps.append(step.step)
                    
                    # 更新上下文
                    context[f'step_{step.step}_result'] = result
                    
                    logger.info(f"步骤 {step.step} 完成")
                    
                except Exception as e:
                    step.status = WorkflowStatus.FAILED
                    step.error = str(e)
                    logger.error(f"步骤 {step.step} 执行失败: {e}")
                    
                    # 根据配置决定是否继续
                    if kwargs.get('stop_on_error', False):
                        break
            
            # 生成执行结果
            execution_time = time.time() - start_time
            success_count = sum(1 for s in steps if s.status == WorkflowStatus.SUCCESS)
            failed_count = sum(1 for s in steps if s.status == WorkflowStatus.FAILED)
            
            overall_status = WorkflowStatus.SUCCESS if failed_count == 0 else WorkflowStatus.FAILED
            
            result = WorkflowResult(
                workflow_name=workflow_name,
                target=target,
                status=overall_status,
                steps=steps,
                execution_time=execution_time,
                summary={
                    'total_steps': len(steps),
                    'success_count': success_count,
                    'failed_count': failed_count,
                    'skipped_count': len(steps) - success_count - failed_count,
                    'execution_time': execution_time
                }
            )
            
            logger.info(f"工作流执行完成: {workflow_name}, 状态: {overall_status.value}")
            return result
            
        except Exception as e:
            logger.error(f"工作流执行异常: {e}")
            raise

    async def _call_mcp_tool(self, tool_executor, tool_name: str, params: Dict[str, Any]) -> str:
        """调用MCP工具的代理方法"""
        try:
            # 直接调用工具执行器上的工具方法
            # 这些方法在WebSecMCP类中通过装饰器注册，但也可以直接调用
            
            # 工具调用映射
            tool_calls = {
                'httpx_wrapper': lambda: tool_executor._call_httpx_tool(**params),
                'tlsx_wrapper': lambda: tool_executor._call_tlsx_tool(**params),
                'spray_wrapper': lambda: tool_executor._call_spray_tool(**params),
                'katana_wrapper': lambda: tool_executor._call_katana_tool(**params),
                'webleak_wrapper': lambda: tool_executor._call_webleak_tool(**params),
                'nuclei_scan_wrapper': lambda: tool_executor._call_nuclei_tool(**params),
                'rustscan_wrapper': lambda: tool_executor._call_rustscan_tool(**params),
                'fingerprintx_wrapper': lambda: tool_executor._call_fingerprintx_tool(**params),
                'netexec_wrapper': lambda: tool_executor._call_netexec_tool(**params),
                'comprehensive_subdomain_discovery_wrapper': lambda: tool_executor._call_subdomain_tool(**params),
                'iplocation_wrapper': lambda: tool_executor._call_iplocation_tool(**params),
                'ipreverse_wrapper': lambda: tool_executor._call_ipreverse_tool(**params),
                'ipasn_wrapper': lambda: tool_executor._call_ipasn_tool(**params)
            }
            
            if tool_name in tool_calls:
                logger.debug(f"调用工具 {tool_name}，参数: {params}")
                result = await tool_calls[tool_name]()
                return result
            else:
                raise ValueError(f"不支持的工具: {tool_name}")
            
        except Exception as e:
            logger.error(f"调用MCP工具 {tool_name} 失败: {e}")
            raise

    async def _execute_dns_resolution(self, step: WorkflowStep, context: Dict[str, Any]) -> str:
        """执行DNS解析"""
        try:
            # 从上下文中提取子域名
            subdomains = self._extract_subdomains_from_context(context)
            
            if not subdomains:
                return "未发现子域名，跳过DNS解析"
            
            logger.info(f"开始解析 {len(subdomains)} 个子域名")
            
            # 解析子域名到IP
            resolved_ips = self._resolve_subdomains_to_ips(subdomains)
            
            # 生成解析报告
            report = []
            report.append("# DNS解析报告")
            report.append("")
            report.append(f"**解析子域名数量**: {len(subdomains)}")
            report.append(f"**成功解析数量**: {len(resolved_ips)}")
            report.append("")
            
            # 按IP分组
            ip_to_domains = {}
            for domain, ip in resolved_ips.items():
                if ip not in ip_to_domains:
                    ip_to_domains[ip] = []
                ip_to_domains[ip].append(domain)
            
            report.append("## 解析结果")
            for ip, domains in ip_to_domains.items():
                report.append(f"### IP: {ip}")
                for domain in domains:
                    report.append(f"- {domain}")
                report.append("")
            
            # 保存解析结果到上下文
            context['resolved_ips'] = list(ip_to_domains.keys())
            context['ip_to_domains'] = ip_to_domains
            
            return "\n".join(report)
            
        except Exception as e:
            logger.error(f"DNS解析执行失败: {e}")
            return f"DNS解析执行失败: {str(e)}"

    async def _execute_web_verification(self, step: WorkflowStep, context: Dict[str, Any], 
                                      tool_executor) -> str:
        """执行Web服务验证 - 对所有发现的端口进行HTTP验证"""
        try:
            # 从上下文中提取目标和所有发现的端口
            target_ip = context.get('target', '')
            discovered_ports = self._extract_ports_from_context(context)
            
            if not discovered_ports:
                return "未发现任何开放端口，跳过Web服务验证"
            
            logger.info(f"开始对 {len(discovered_ports)} 个端口进行Web服务验证: {discovered_ports}")
            
            verification_results = []
            verified_web_services = []
            
            # 对每个发现的端口都进行HTTP和HTTPS验证
            for port in discovered_ports:
                # 构建HTTP和HTTPS URL进行测试
                urls_to_test = []
                
                # 对于443端口，优先尝试HTTPS
                if port == 443:
                    urls_to_test = [f"https://{target_ip}:{port}", f"http://{target_ip}:{port}"]
                # 对于其他常见HTTPS端口，优先尝试HTTPS
                elif port in [8443, 9443, 8444]:
                    urls_to_test = [f"https://{target_ip}:{port}", f"http://{target_ip}:{port}"]
                # 对于其他端口，优先尝试HTTP
                else:
                    urls_to_test = [f"http://{target_ip}:{port}", f"https://{target_ip}:{port}"]
                
                port_verified = False
                
                # 对每个端口的HTTP/HTTPS进行验证
                for url in urls_to_test:
                    if port_verified:  # 如果该端口已经验证成功，跳过其他协议
                        break
                        
                    logger.info(f"验证端口 {port}: {url}")
                    
                    try:
                        # 调用httpx工具进行验证
                        result = await tool_executor._call_httpx_tool(
                            url=url,
                            method="GET",
                            timeout=8  # 短超时用于快速验证
                        )
                        
                        # 检查结果是否表明这是一个Web服务
                        result_lower = result.lower()
                        
                        # 更全面的Web服务指示器
                        web_indicators = [
                            # HTTP状态码
                            'status_code', 'http/1.1', 'http/2',
                            # HTTP头部
                            'content-length', 'content-type', 'server:', 'date:',
                            'cache-control', 'set-cookie', 'location:',
                            # 状态码
                            '200', '201', '301', '302', '303', '307', '308',
                            '400', '401', '403', '404', '405', '500', '502', '503',
                            # Web服务器
                            'nginx', 'apache', 'iis', 'tomcat', 'jetty', 'lighttpd',
                            # Web技术
                            'html', 'javascript', 'css', 'json', 'xml'
                        ]
                        
                        if any(indicator in result_lower for indicator in web_indicators):
                            verified_web_services.append(url)
                            verification_results.append({
                                'port': port,
                                'url': url,
                                'status': 'verified',
                                'result_preview': result[:300] + '...' if len(result) > 300 else result
                            })
                            logger.info(f"✅ 端口 {port} 验证成功: {url}")
                            port_verified = True
                        else:
                            # 检查是否是连接错误还是非Web服务
                            if 'connection' in result_lower and ('refused' in result_lower or 'timeout' in result_lower):
                                logger.debug(f"🔌 端口 {port} 连接失败: {url}")
                            else:
                                verification_results.append({
                                    'port': port,
                                    'url': url,
                                    'status': 'not_web_service',
                                    'result_preview': result[:200] + '...' if len(result) > 200 else result
                                })
                                logger.info(f"❌ 端口 {port} 非Web服务: {url}")
                            
                    except Exception as e:
                        error_msg = str(e).lower()
                        # 区分不同类型的错误
                        if 'timeout' in error_msg or 'connection' in error_msg:
                            logger.debug(f"🔌 端口 {port} 连接失败: {url} - {e}")
                        else:
                            verification_results.append({
                                'port': port,
                                'url': url,
                                'status': 'error',
                                'error': str(e)
                            })
                            logger.warning(f"⚠️  端口 {port} 验证异常: {url} - {e}")
                
                # 如果该端口没有验证成功，记录为非Web服务
                if not port_verified:
                    verification_results.append({
                        'port': port,
                        'url': f"http://{target_ip}:{port}",
                        'status': 'not_web_service',
                        'result_preview': f"端口 {port} 未响应HTTP/HTTPS请求"
                    })
                    logger.info(f"❌ 端口 {port} 确认为非Web服务")
            
            # 生成验证报告
            report = self._generate_web_verification_report(
                verification_results, verified_web_services, discovered_ports
            )
            
            # 将验证成功的Web服务保存到上下文
            context['verified_web_services'] = verified_web_services
            
            logger.info(f"Web服务验证完成: 发现 {len(verified_web_services)} 个Web服务")
            
            return report
            
        except Exception as e:
            logger.error(f"Web服务验证执行失败: {e}")
            return f"Web服务验证执行失败: {str(e)}"

    def _generate_web_verification_report(self, verification_results: List[Dict], 
                                        verified_services: List[str], 
                                        total_ports: List[int]) -> str:
        """生成Web服务验证报告"""
        report = []
        report.append("# Web服务验证报告")
        report.append("")
        report.append(f"**扫描端口总数**: {len(total_ports)}")
        report.append(f"**发现Web服务数量**: {len(verified_services)}")
        report.append(f"**扫描的端口**: {', '.join(map(str, sorted(total_ports)))}")
        report.append("")
        
        if verified_services:
            report.append("## ✅ 验证成功的Web服务")
            # 按端口排序显示
            services_by_port = {}
            for service in verified_services:
                try:
                    port = int(service.split(':')[-1])
                    services_by_port[port] = service
                except:
                    services_by_port[9999] = service  # 无法解析端口的放在最后
            
            for port in sorted(services_by_port.keys()):
                service = services_by_port[port]
                report.append(f"- **端口 {port}**: {service}")
            report.append("")
        
        # 按端口分组显示详细结果
        results_by_port = {}
        for result in verification_results:
            port = result.get('port', 0)
            if port not in results_by_port:
                results_by_port[port] = []
            results_by_port[port].append(result)
        
        report.append("## 详细验证结果")
        for port in sorted(results_by_port.keys()):
            port_results = results_by_port[port]
            
            # 确定该端口的总体状态
            has_verified = any(r['status'] == 'verified' for r in port_results)
            status_icon = "✅" if has_verified else "❌"
            
            report.append(f"### {status_icon} 端口 {port}")
            
            for result in port_results:
                result_icon = {
                    'verified': '✅',
                    'not_web_service': '❌',
                    'error': '⚠️'
                }.get(result['status'], '❓')
                
                report.append(f"**{result_icon} {result['url']}**")
                report.append(f"- 状态: {result['status']}")
                
                if result['status'] == 'verified':
                    report.append("- 响应预览:")
                    report.append("```")
                    report.append(result['result_preview'])
                    report.append("```")
                elif result['status'] == 'error':
                    report.append(f"- 错误: {result['error']}")
                elif result['status'] == 'not_web_service':
                    report.append(f"- 说明: {result['result_preview']}")
                
                report.append("")
        
        # 添加总结
        non_web_ports = [port for port in total_ports 
                        if not any(service.endswith(f":{port}") for service in verified_services)]
        
        if non_web_ports:
            report.append("## 📋 非Web服务端口")
            report.append(f"以下端口经验证不是Web服务: {', '.join(map(str, sorted(non_web_ports)))}")
            report.append("")
        
        return "\n".join(report)

    async def _execute_cascade_network_test(self, step: WorkflowStep, context: Dict[str, Any], 
                                          tool_executor, kwargs: Dict[str, Any]) -> str:
        """执行级联网络测试"""
        try:
            # 从上下文中提取解析的IP地址
            resolved_ips = context.get('resolved_ips', [])
            
            if not resolved_ips:
                return "未发现解析的IP地址，跳过级联网络测试"
            
            logger.info(f"发现 {len(resolved_ips)} 个IP地址，开始级联网络测试")
            
            cascade_results = []
            cascade_workflow = step.cascade_workflow or 'network_pentest'
            
            # 对每个IP执行网络渗透测试工作流
            for i, ip in enumerate(resolved_ips[:10]):  # 限制最多10个IP
                logger.info(f"级联网络测试目标 {i+1}/{len(resolved_ips)}: {ip}")
                
                try:
                    # 执行网络渗透测试工作流
                    cascade_result = await self.execute_workflow(
                        workflow_name=cascade_workflow,
                        target=ip,
                        tool_executor=tool_executor,
                        stop_on_error=False,
                        timeout=kwargs.get('timeout', 600)  # 网络测试需要更长时间
                    )
                    
                    # 获取相关的域名信息
                    ip_to_domains = context.get('ip_to_domains', {})
                    related_domains = ip_to_domains.get(ip, [])
                    
                    cascade_results.append({
                        'target': ip,
                        'related_domains': related_domains,
                        'status': cascade_result.status.value,
                        'summary': cascade_result.summary,
                        'key_findings': self._extract_key_findings(cascade_result)
                    })
                    
                except Exception as e:
                    logger.error(f"级联网络测试失败 {ip}: {e}")
                    cascade_results.append({
                        'target': ip,
                        'status': 'failed',
                        'error': str(e)
                    })
            
            # 生成级联网络测试报告
            report = self._generate_cascade_network_report(cascade_results)
            return report
            
        except Exception as e:
            logger.error(f"级联网络测试执行失败: {e}")
            return f"级联网络测试执行失败: {str(e)}"

    def _generate_cascade_network_report(self, cascade_results: List[Dict]) -> str:
        """生成级联网络测试报告"""
        report = []
        report.append("# 级联网络渗透测试报告")
        report.append("")
        report.append(f"**测试IP数量**: {len(cascade_results)}")
        
        success_count = sum(1 for r in cascade_results if r.get('status') == 'success')
        report.append(f"**成功测试**: {success_count}/{len(cascade_results)}")
        report.append("")
        
        for i, result in enumerate(cascade_results, 1):
            status_icon = "✅" if result.get('status') == 'success' else "❌"
            report.append(f"## {status_icon} IP {i}: {result['target']}")
            
            # 显示相关域名
            related_domains = result.get('related_domains', [])
            if related_domains:
                report.append(f"**相关域名**: {', '.join(related_domains)}")
            
            if result.get('status') == 'success':
                summary = result.get('summary', {})
                report.append(f"- **执行状态**: 成功")
                report.append(f"- **成功步骤**: {summary.get('success_count', 0)}")
                report.append(f"- **执行时间**: {summary.get('execution_time', 0):.1f}秒")
                
                key_findings = result.get('key_findings', [])
                if key_findings:
                    report.append("- **关键发现**:")
                    for finding in key_findings:
                        report.append(f"  - {finding}")
            else:
                report.append(f"- **执行状态**: 失败")
                if result.get('error'):
                    report.append(f"- **错误信息**: {result['error']}")
            
            report.append("")
        
        return "\n".join(report)

    async def _execute_cascade_web_test(self, step: WorkflowStep, context: Dict[str, Any], 
                                      tool_executor, kwargs: Dict[str, Any]) -> str:
        """执行级联Web测试"""
        try:
            # 优先使用已验证的Web服务
            web_targets = context.get('verified_web_services', [])
            
            # 如果没有已验证的服务，尝试从上下文中提取
            if not web_targets:
                web_targets = self._extract_web_targets_from_context(context)
            
            if not web_targets:
                return "未发现已验证的Web服务，跳过级联测试"
            
            logger.info(f"发现 {len(web_targets)} 个Web目标，开始级联测试")
            
            cascade_results = []
            cascade_workflow = step.cascade_workflow or 'web_pentest'
            
            # 对每个Web目标执行Web工作流
            for i, web_target in enumerate(web_targets[:5]):  # 限制最多5个目标
                logger.info(f"级联测试目标 {i+1}/{len(web_targets)}: {web_target}")
                
                try:
                    # 执行Web工作流
                    cascade_result = await self.execute_workflow(
                        workflow_name=cascade_workflow,
                        target=web_target,
                        tool_executor=tool_executor,
                        stop_on_error=False,
                        timeout=kwargs.get('timeout', 300)
                    )
                    
                    cascade_results.append({
                        'target': web_target,
                        'status': cascade_result.status.value,
                        'summary': cascade_result.summary,
                        'key_findings': self._extract_key_findings(cascade_result)
                    })
                    
                except Exception as e:
                    logger.error(f"级联测试失败 {web_target}: {e}")
                    cascade_results.append({
                        'target': web_target,
                        'status': 'failed',
                        'error': str(e)
                    })
            
            # 生成级联测试报告
            report = self._generate_cascade_report(cascade_results)
            return report
            
        except Exception as e:
            logger.error(f"级联Web测试执行失败: {e}")
            return f"级联Web测试执行失败: {str(e)}"
    
    def _extract_key_findings(self, workflow_result: WorkflowResult) -> List[str]:
        """从工作流结果中提取关键发现"""
        findings = []
        
        for step in workflow_result.steps:
            if step.status == WorkflowStatus.SUCCESS and step.result:
                result_str = str(step.result).lower()
                
                # 检查常见的安全发现
                if 'vulnerability' in result_str or 'critical' in result_str:
                    findings.append(f"{step.description}: 发现潜在漏洞")
                elif 'high' in result_str or 'medium' in result_str:
                    findings.append(f"{step.description}: 发现安全问题")
                elif 'directory' in result_str and 'found' in result_str:
                    findings.append(f"{step.description}: 发现敏感目录")
                elif 'admin' in result_str or 'login' in result_str:
                    findings.append(f"{step.description}: 发现管理界面")
        
        return findings[:3]  # 返回最多3个关键发现
    
    def _generate_cascade_report(self, cascade_results: List[Dict]) -> str:
        """生成级联测试报告"""
        report = []
        report.append("# 级联Web服务测试报告")
        report.append("")
        report.append(f"**测试目标数量**: {len(cascade_results)}")
        
        success_count = sum(1 for r in cascade_results if r.get('status') == 'success')
        report.append(f"**成功测试**: {success_count}/{len(cascade_results)}")
        report.append("")
        
        for i, result in enumerate(cascade_results, 1):
            status_icon = "✅" if result.get('status') == 'success' else "❌"
            report.append(f"## {status_icon} 目标 {i}: {result['target']}")
            
            if result.get('status') == 'success':
                summary = result.get('summary', {})
                report.append(f"- **执行状态**: 成功")
                report.append(f"- **成功步骤**: {summary.get('success_count', 0)}")
                report.append(f"- **执行时间**: {summary.get('execution_time', 0):.1f}秒")
                
                key_findings = result.get('key_findings', [])
                if key_findings:
                    report.append("- **关键发现**:")
                    for finding in key_findings:
                        report.append(f"  - {finding}")
            else:
                report.append(f"- **执行状态**: 失败")
                if result.get('error'):
                    report.append(f"- **错误信息**: {result['error']}")
            
            report.append("")
        
        return "\n".join(report)

    def generate_workflow_report(self, result: WorkflowResult) -> str:
        """生成工作流执行报告"""
        report = []
        report.append(f"# 工作流执行报告")
        report.append(f"")
        report.append(f"**工作流名称**: {result.workflow_name}")
        report.append(f"**目标**: {result.target}")
        report.append(f"**执行状态**: {result.status.value}")
        report.append(f"**执行时间**: {result.execution_time:.2f}秒")
        report.append(f"")
        
        # 执行摘要
        summary = result.summary
        report.append(f"## 执行摘要")
        report.append(f"- 总步骤数: {summary['total_steps']}")
        report.append(f"- 成功步骤: {summary['success_count']}")
        report.append(f"- 失败步骤: {summary['failed_count']}")
        report.append(f"- 跳过步骤: {summary['skipped_count']}")
        report.append(f"")
        
        # 步骤详情
        report.append(f"## 步骤执行详情")
        for step in result.steps:
            status_icon = {
                WorkflowStatus.SUCCESS: "✅",
                WorkflowStatus.FAILED: "❌",
                WorkflowStatus.SKIPPED: "⏭️",
                WorkflowStatus.RUNNING: "🔄"
            }.get(step.status, "❓")
            
            report.append(f"### {status_icon} 步骤 {step.step}: {step.description}")
            report.append(f"**工具**: {step.tool}")
            report.append(f"**状态**: {step.status.value}")
            
            if step.error:
                report.append(f"**错误**: {step.error}")
            
            if step.result and step.status == WorkflowStatus.SUCCESS:
                # 截取结果的前500字符
                result_preview = step.result[:500]
                if len(step.result) > 500:
                    result_preview += "..."
                report.append(f"**结果预览**:")
                report.append(f"```")
                report.append(result_preview)
                report.append(f"```")
            
            report.append(f"")
        
        return "\n".join(report)