# Encoding: utf-8
"""
MCP响应处理工具模块
提供数据大小检测、自动截断、压缩等功能，防止上下文长度超限
"""

import json
import gzip
import sys
import logging
from typing import Any, Dict, Optional, Tuple, Union
from dataclasses import dataclass
from enum import Enum

logger = logging.getLogger(__name__)

# 为了避免循环导入，在这里重新定义ResponseConfig
@dataclass
class ResponseConfig:
    """响应处理配置"""
    # 数据大小阈值 (字节)
    direct_threshold: int = 10_000      # 10KB - 直接返回
    compress_threshold: int = 100_000   # 100KB - 压缩返回
    paginate_threshold: int = 1_000_000 # 1MB - 分页返回
    max_size: int = 10_000_000         # 10MB - 最大处理大小

    # 压缩配置
    compression_level: int = 6          # gzip压缩级别 (1-9)
    min_compression_ratio: float = 0.8  # 最小压缩比，低于此值不压缩

    # 截断配置
    truncate_size: int = 50_000        # 50KB - 截断大小
    preserve_structure: bool = True     # 保持JSON结构完整性

class ResponseStrategy(Enum):
    """响应处理策略枚举"""
    DIRECT = "direct"           # 直接返回
    TRUNCATED = "truncated"     # 截断返回



class DataSizeEstimator:
    """数据大小估算器"""
    
    @staticmethod
    def estimate_size(data: Any) -> Tuple[int, int]:
        """
        估算数据大小
        
        Args:
            data: 要估算的数据
            
        Returns:
            Tuple[内存占用大小, 序列化后大小]
        """
        try:
            # 计算内存占用大小
            memory_size = sys.getsizeof(data)
            
            # 计算序列化后大小
            if isinstance(data, (dict, list)):
                serialized = json.dumps(data, ensure_ascii=False)
                serialized_size = len(serialized.encode('utf-8'))
            elif isinstance(data, str):
                serialized_size = len(data.encode('utf-8'))
            elif isinstance(data, bytes):
                serialized_size = len(data)
            else:
                # 其他类型转换为字符串
                serialized = str(data)
                serialized_size = len(serialized.encode('utf-8'))
            
            return memory_size, serialized_size
            
        except Exception as e:
            logger.warning(f"Failed to estimate data size: {e}")
            # 返回保守估计
            return 1024, 1024

    @staticmethod
    def estimate_json_size(data: Union[dict, list]) -> int:
        """
        快速估算JSON数据序列化后的大小
        
        Args:
            data: JSON数据
            
        Returns:
            估算的字节大小
        """
        try:
            if isinstance(data, dict):
                # 估算字典大小：键值对数量 * 平均长度
                size = 0
                for key, value in data.items():
                    size += len(str(key)) + 4  # 键 + 引号和冒号
                    if isinstance(value, (dict, list)):
                        size += DataSizeEstimator.estimate_json_size(value)
                    else:
                        size += len(str(value)) + 2  # 值 + 引号
                return size + len(data) * 2  # 逗号和空格
                
            elif isinstance(data, list):
                # 估算列表大小
                size = 0
                for item in data:
                    if isinstance(item, (dict, list)):
                        size += DataSizeEstimator.estimate_json_size(item)
                    else:
                        size += len(str(item)) + 2
                return size + len(data) * 2  # 逗号和空格
                
            else:
                return len(str(data))
                
        except Exception as e:
            logger.warning(f"Failed to estimate JSON size: {e}")
            return 1024

class ResponseTruncator:
    """响应截断器"""
    
    def __init__(self, config: ResponseConfig):
        self.config = config
    
    def truncate_data(self, data: Any, max_size: int = None) -> Tuple[Any, bool]:
        """
        截断数据到指定大小
        
        Args:
            data: 要截断的数据
            max_size: 最大大小（字节），默认使用配置值
            
        Returns:
            Tuple[截断后的数据, 是否被截断]
        """
        max_size = max_size or self.config.truncate_size
        
        try:
            if isinstance(data, str):
                return self._truncate_string(data, max_size)
            elif isinstance(data, (dict, list)):
                return self._truncate_json(data, max_size)
            else:
                # 其他类型转换为字符串处理
                str_data = str(data)
                truncated_str, was_truncated = self._truncate_string(str_data, max_size)
                return truncated_str, was_truncated
                
        except Exception as e:
            logger.error(f"Failed to truncate data: {e}")
            # 最后的降级处理：返回错误信息
            error_msg = f"[数据处理失败: {type(data).__name__} - {str(e)[:100]}]"
            return error_msg[:max_size], True
    
    def _truncate_string(self, text: str, max_size: int) -> Tuple[str, bool]:
        """截断字符串"""
        encoded = text.encode('utf-8')
        if len(encoded) <= max_size:
            return text, False
        
        # 截断到最大大小，确保不破坏UTF-8编码
        truncated = encoded[:max_size-100]  # 留出空间给提示信息
        
        # 找到最后一个完整的UTF-8字符
        while truncated and (truncated[-1] & 0x80):
            truncated = truncated[:-1]
        
        result = truncated.decode('utf-8', errors='ignore')
        result += f"\n\n[数据被截断 - 原始大小: {len(encoded)} 字节, 显示: {len(truncated)} 字节]"
        
        return result, True
    
    def _truncate_json(self, data: Union[dict, list], max_size: int) -> Tuple[Union[dict, list], bool]:
        """截断JSON数据，保持结构完整性"""
        if not self.config.preserve_structure:
            # 简单截断：转换为字符串后截断
            json_str = json.dumps(data, ensure_ascii=False, indent=2)
            truncated_str, was_truncated = self._truncate_string(json_str, max_size)
            return truncated_str, was_truncated
        
        # 智能截断：保持JSON结构
        if isinstance(data, dict):
            return self._truncate_dict(data, max_size)
        elif isinstance(data, list):
            return self._truncate_list(data, max_size)
    
    def _truncate_dict(self, data: dict, max_size: int) -> Tuple[dict, bool]:
        """截断字典，保持结构"""
        result = {}
        current_size = 0
        was_truncated = False
        
        for key, value in data.items():
            # 估算添加这个键值对后的大小
            item_size = DataSizeEstimator.estimate_json_size({key: value})
            
            if current_size + item_size > max_size:
                # 添加截断提示
                result["_truncated_info"] = {
                    "message": "数据被截断以防止上下文溢出",
                    "original_keys": len(data),
                    "displayed_keys": len(result),
                    "estimated_original_size": DataSizeEstimator.estimate_json_size(data)
                }
                was_truncated = True
                break
            
            result[key] = value
            current_size += item_size
        
        return result, was_truncated
    
    def _truncate_list(self, data: list, max_size: int) -> Tuple[list, bool]:
        """截断列表，保持结构"""
        result = []
        current_size = 0
        was_truncated = False
        
        for item in data:
            item_size = DataSizeEstimator.estimate_json_size(item)
            
            if current_size + item_size > max_size:
                # 添加截断提示
                result.append({
                    "_truncated_info": {
                        "message": "列表被截断以防止上下文溢出",
                        "original_length": len(data),
                        "displayed_length": len(result),
                        "estimated_original_size": DataSizeEstimator.estimate_json_size(data)
                    }
                })
                was_truncated = True
                break
            
            result.append(item)
            current_size += item_size
        
        return result, was_truncated

class CompressionEngine:
    """数据压缩引擎"""

    def __init__(self, config: ResponseConfig):
        self.config = config

    def compress_data(self, data: str) -> Tuple[bytes, float]:
        """
        压缩字符串数据

        Args:
            data: 要压缩的字符串

        Returns:
            Tuple[压缩后的数据, 压缩比]
        """
        try:
            original_bytes = data.encode('utf-8')
            original_size = len(original_bytes)

            # 使用gzip压缩
            compressed = gzip.compress(
                original_bytes,
                compresslevel=self.config.compression_level
            )
            compressed_size = len(compressed)

            # 计算压缩比
            compression_ratio = compressed_size / original_size if original_size > 0 else 1.0

            return compressed, compression_ratio

        except Exception as e:
            logger.error(f"Compression failed: {e}")
            return data.encode('utf-8'), 1.0

    def should_compress(self, data_size: int, compression_ratio: float = None) -> bool:
        """
        判断是否应该压缩数据

        Args:
            data_size: 数据大小
            compression_ratio: 压缩比（可选）

        Returns:
            是否应该压缩
        """
        # 数据太小不压缩
        if data_size < 1024:  # 1KB
            return False

        # 如果已知压缩比，检查是否值得压缩
        if compression_ratio is not None:
            return compression_ratio < self.config.min_compression_ratio

        return True

    def decompress_data(self, compressed_data: bytes) -> str:
        """
        解压缩数据

        Args:
            compressed_data: 压缩的数据

        Returns:
            解压缩后的字符串
        """
        try:
            decompressed = gzip.decompress(compressed_data)
            return decompressed.decode('utf-8')
        except Exception as e:
            logger.error(f"Decompression failed: {e}")
            return str(compressed_data)


@dataclass
class ResponseMetadata:
    """响应元数据"""
    original_size: int
    processed_size: int
    strategy: ResponseStrategy
    was_truncated: bool = False
    processing_time_ms: int = 0


class MCPResponseWrapper:
    """MCP响应包装器 - 统一处理响应数据"""

    def __init__(self, config: ResponseConfig = None):
        self.config = config or ResponseConfig()
        self.estimator = DataSizeEstimator()
        self.truncator = ResponseTruncator(self.config)

    def process_response(self, data: Any) -> Tuple[Any, ResponseMetadata]:
        """
        处理响应数据

        Args:
            data: 原始响应数据

        Returns:
            Tuple[处理后的数据, 响应元数据]
        """
        import time
        start_time = time.time()

        try:
            # 估算数据大小
            _, serialized_size = self.estimator.estimate_size(data)

            # 选择处理策略
            strategy = self._select_strategy(serialized_size)

            # 根据策略处理数据
            processed_data, metadata = self._apply_strategy(data, strategy, serialized_size)

            # 计算处理时间
            processing_time = int((time.time() - start_time) * 1000)
            metadata.processing_time_ms = processing_time

            logger.debug(f"Response processed: {strategy.value}, "
                        f"original: {serialized_size}B, "
                        f"processed: {metadata.processed_size}B, "
                        f"time: {processing_time}ms")

            return processed_data, metadata

        except Exception as e:
            logger.error(f"Response processing failed: {e}")
            # 降级处理：简单截断
            fallback_data, was_truncated = self.truncator.truncate_data(data)
            metadata = ResponseMetadata(
                original_size=serialized_size if 'serialized_size' in locals() else 0,
                processed_size=len(str(fallback_data)),
                strategy=ResponseStrategy.TRUNCATED,
                was_truncated=was_truncated,
                processing_time_ms=int((time.time() - start_time) * 1000)
            )
            return fallback_data, metadata

    def _select_strategy(self, data_size: int) -> ResponseStrategy:
        """选择响应处理策略"""
        if data_size < self.config.threshold:
            return ResponseStrategy.DIRECT
        else:
            return ResponseStrategy.TRUNCATED

    def _apply_strategy(self, data: Any, strategy: ResponseStrategy, original_size: int) -> Tuple[Any, ResponseMetadata]:
        """应用处理策略"""
        metadata = ResponseMetadata(
            original_size=original_size,
            processed_size=original_size,
            strategy=strategy
        )

        if strategy == ResponseStrategy.DIRECT:
            return data, metadata
        elif strategy == ResponseStrategy.TRUNCATED:
            return self._apply_truncation(data, metadata)
        else:
            # 默认截断处理
            return self._apply_truncation(data, metadata)



    def _apply_truncation(self, data: Any, metadata: ResponseMetadata) -> Tuple[Any, ResponseMetadata]:
        """应用截断策略"""
        try:
            truncated_data, was_truncated = self.truncator.truncate_data(data, self.config.threshold)

            metadata.was_truncated = was_truncated
            metadata.processed_size = len(str(truncated_data))

            return truncated_data, metadata

        except Exception as e:
            logger.error(f"Truncation failed: {e}")
            # 最后的降级处理
            error_msg = f"[截断处理失败: {type(data).__name__}]"
            metadata.processed_size = len(error_msg)
            return error_msg, metadata
