# Encdoding: utf-8
import subprocess
from pathlib import Path
import shutil
import logging
from .platform_manager import get_platform_manager

logger = logging.getLogger(__name__)

def run_executable(executable_path, *args):
    """跨平台执行外部工具并返回输出

    Args:
        executable_path: 可执行文件路径或工具名称
        *args: 命令行参数

    Returns:
        执行结果字符串或错误信息
    """
    platform_manager = get_platform_manager()
    
    # 1. 检查是否是绝对路径或相对路径
    abs_path = Path(executable_path).expanduser().resolve()
    if abs_path.exists() and abs_path.is_file():
        exec_path = str(abs_path)
        logger.debug(f"使用绝对路径: {exec_path}")
    else:
        # 2. 尝试使用平台管理器查找工具
        tool_path = platform_manager.get_executable_path(executable_path)
        if tool_path:
            exec_path = str(tool_path)
            logger.debug(f"使用平台特定路径: {exec_path}")
        else:
            # 3. 在旧的 bin 目录下查找（向后兼容）
            bin_path = Path(__file__).parent.parent / "bin" / executable_path
            if bin_path.exists() and bin_path.is_file():
                exec_path = str(bin_path.resolve())
                logger.debug(f"使用旧版bin路径: {exec_path}")
            else:
                # 4. PATH 环境变量查找
                exec_path = shutil.which(executable_path)
                if exec_path:
                    logger.debug(f"在PATH中找到: {exec_path}")
                else:
                    # 提供详细的错误信息
                    platform_info = platform_manager.get_platform_info()
                    available_tools = platform_manager.list_available_tools()
                    
                    error_msg = f"""工具未找到: {executable_path}

平台信息: {platform_info['os']}/{platform_info['arch']}
平台目录: {platform_info['platform_dir']}
可用工具: {', '.join(available_tools) if available_tools else '无'}

搜索路径:
1. 绝对路径: {abs_path}
2. 平台目录: {platform_manager.platform_dir / executable_path}
3. 旧版目录: {bin_path}
4. PATH环境变量

请确保工具已安装在正确的平台目录中。"""
                    
                    return error_msg

    command = [exec_path] + list(args)
    logger.debug(f"执行命令: {' '.join(command)}")
    
    try:
        result = subprocess.run(
            command, 
            capture_output=True, 
            text=True, 
            check=True,
            timeout=300  # 5分钟超时
        )
        logger.debug(f"命令执行成功，输出长度: {len(result.stdout)} 字符")
        return result.stdout
    except subprocess.CalledProcessError as e:
        error_output = e.stderr.strip() if e.stderr else str(e)
        logger.error(f"命令执行失败 (退出码: {e.returncode}): {error_output}")
        return f"Error: {error_output}"
    except subprocess.TimeoutExpired:
        logger.error(f"命令执行超时: {executable_path}")
        return f"Error: Command timed out after 5 minutes"
    except Exception as e:
        logger.error(f"意外错误: {str(e)}")
        return f"Unexpected error: {str(e)}"


def get_tool_info(tool_name: str) -> dict:
    """获取工具信息

    Args:
        tool_name: 工具名称

    Returns:
        dict: 工具信息字典
    """
    platform_manager = get_platform_manager()
    
    return {
        'tool_name': tool_name,
        'available': platform_manager.is_tool_available(tool_name),
        'path': str(platform_manager.get_executable_path(tool_name)) if platform_manager.is_tool_available(tool_name) else None,
        'platform': f"{platform_manager.os_name}/{platform_manager.arch}"
    }


def validate_tools(tool_list: list) -> dict:
    """验证多个工具的可用性

    Args:
        tool_list: 工具名称列表

    Returns:
        dict: 验证结果
    """
    platform_manager = get_platform_manager()
    
    results = {
        'platform': f"{platform_manager.os_name}/{platform_manager.arch}",
        'platform_dir_exists': platform_manager.platform_dir.exists(),
        'tools': {},
        'available_count': 0,
        'total_count': len(tool_list)
    }
    
    for tool_name in tool_list:
        tool_available = platform_manager.is_tool_available(tool_name)
        tool_path = platform_manager.get_executable_path(tool_name)
        
        results['tools'][tool_name] = {
            'available': tool_available,
            'path': str(tool_path) if tool_path else None
        }
        
        if tool_available:
            results['available_count'] += 1
    
    return results
