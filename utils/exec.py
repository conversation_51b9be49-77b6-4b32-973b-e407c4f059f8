# Encdoding: utf-8
import subprocess
import logging
from .platform_manager import get_platform_manager

logger = logging.getLogger(__name__)

def run_executable(cmd):
    """根据系统环境自动选择合适的可执行文件版本并执行

    Args:
        cmd: 命令参数列表，第一个元素为工具名称，后续为参数
             例如: ['nuclei', '-target', 'http://example.com']

    Returns:
        执行结果字符串或错误信息
    """
    if not cmd or not isinstance(cmd, list) or len(cmd) == 0:
        return "Error: cmd 参数必须是非空的命令参数列表"

    tool_name = cmd[0]
    args = cmd[1:] if len(cmd) > 1 else []

    platform_manager = get_platform_manager()

    # 1. 自动检测系统信息并查找对应版本的可执行文件
    tool_path = platform_manager.get_executable_path(tool_name)

    if not tool_path:
        # 2. 如果未找到匹配的可执行文件，返回明确的错误信息
        platform_info = platform_manager.get_platform_info()
        return f"工具不存在错误: 未找到适用于 {platform_info['os']}/{platform_info['arch']} 平台的 {tool_name} 工具"

    # 3. 构建完整命令并执行
    exec_path = str(tool_path)
    command = [exec_path] + args

    logger.debug(f"执行命令: {' '.join(command)}")
    logger.debug(f"使用工具路径: {exec_path}")

    try:
        result = subprocess.run(
            command,
            capture_output=True,
            text=True,
            check=True,
            timeout=300  # 5分钟超时
        )
        logger.debug(f"命令执行成功，输出长度: {len(result.stdout)} 字符")
        return result.stdout
    except subprocess.CalledProcessError as e:
        error_output = e.stderr.strip() if e.stderr else str(e)
        logger.error(f"命令执行失败 (退出码: {e.returncode}): {error_output}")
        return f"执行失败: {error_output}"
    except subprocess.TimeoutExpired:
        logger.error(f"命令执行超时: {tool_name}")
        return f"执行失败: 命令执行超时（5分钟）"
    except Exception as e:
        logger.error(f"意外错误: {str(e)}")
        return f"执行失败: {str(e)}"


def get_tool_info(tool_name: str) -> dict:
    """获取工具信息

    Args:
        tool_name: 工具名称

    Returns:
        dict: 工具信息字典
    """
    platform_manager = get_platform_manager()
    
    return {
        'tool_name': tool_name,
        'available': platform_manager.is_tool_available(tool_name),
        'path': str(platform_manager.get_executable_path(tool_name)) if platform_manager.is_tool_available(tool_name) else None,
        'platform': f"{platform_manager.os_name}/{platform_manager.arch}"
    }


def validate_tools(tool_list: list) -> dict:
    """验证多个工具的可用性

    Args:
        tool_list: 工具名称列表

    Returns:
        dict: 验证结果
    """
    platform_manager = get_platform_manager()
    
    results = {
        'platform': f"{platform_manager.os_name}/{platform_manager.arch}",
        'platform_dir_exists': platform_manager.platform_dir.exists(),
        'tools': {},
        'available_count': 0,
        'total_count': len(tool_list)
    }
    
    for tool_name in tool_list:
        tool_available = platform_manager.is_tool_available(tool_name)
        tool_path = platform_manager.get_executable_path(tool_name)
        
        results['tools'][tool_name] = {
            'available': tool_available,
            'path': str(tool_path) if tool_path else None
        }
        
        if tool_available:
            results['available_count'] += 1
    
    return results
