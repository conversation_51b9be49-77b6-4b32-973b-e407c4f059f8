# Encoding: utf-8
import asyncio
import json
import logging
import signal
import sys
import re
import time
import inspect
import warnings
from typing import Optional, Dict, Any
from functools import wraps
import traceback
from concurrent.futures import ThreadPoolExecutor, TimeoutError as FuturesTimeoutError

from fastmcp import FastMCP

from config.configmanager import ConfigManager
from config.validation.rules import (
    validate_parameter_value, is_forbidden_value,
    get_parameter_examples, get_suggested_alternatives, ParameterType
)
from tools.nuclei import run_nuclei
from tools.spray import run_spray
from tools.crack import run_crack
from tools.rustscan import run_rustscan
from tools.katana import run_katana
from tools.httpx import run_httpx
from tools.tlsx import run_tlsx
from tools.fingerprintx import run_fingerprintx
from tools.netexec import run_netexec
from tools.webleak import run_webleak
from tools.subdomain.subdomain import comprehensive_subdomain_discovery
from tools.ip import iplocation
from tools.ipreverse import ipreverse
from tools.ipasn import ipasn
from tools.js_api_analyzer import get_valid_apis_only
from database.db_manager import initialize_database
from utils.response_handler import MCPResponseWrapper
from utils.response_config import get_response_config
# from utils.workflow_executor import WorkflowExecutor, WorkflowStatus

# 过滤 websockets 弃用警告
warnings.filterwarnings("ignore", category=DeprecationWarning, module="websockets.legacy")
warnings.filterwarnings("ignore", category=DeprecationWarning, module="uvicorn.protocols.websockets.websockets_impl")


# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('./logs/websec_mcp.log'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

# Global configuration
DEFAULT_TIMEOUT = 400  # 5 minutes default timeout
MAX_WORKERS = 10  # Maximum number of concurrent workers
SHUTDOWN_TIMEOUT = 30  # Graceful shutdown timeout


class MCPError(Exception):
    """Custom exception for MCP operations"""
    pass

class TimeoutHandler:
    """Handle timeouts for operations"""

    def __init__(self, timeout: int = DEFAULT_TIMEOUT):
        self.timeout = timeout

    async def run_with_timeout(self, func, *args, **kwargs):
        """Run a function with timeout in thread pool"""
        loop = asyncio.get_event_loop()

        with ThreadPoolExecutor(max_workers=MAX_WORKERS) as executor:
            try:
                # Submit the blocking function to thread pool
                future = executor.submit(func, *args, **kwargs)
                # Wait for completion with timeout
                result = await asyncio.wait_for(
                    loop.run_in_executor(None, future.result),
                    timeout=self.timeout
                )
                return result
            except asyncio.TimeoutError:
                logger.error(f"Operation timed out after {self.timeout} seconds")
                return(f"Operation timed out after {self.timeout} seconds")
            except FuturesTimeoutError:
                logger.error(f"Thread pool operation timed out after {self.timeout} seconds")
                return(f"Thread pool operation timed out after {self.timeout} seconds")
            except Exception as e:
                logger.error(f"Operation failed: {str(e)}")
                return(f"Operation failed: {str(e)}")

# 全局响应处理器实例
_global_response_wrapper = None

def get_global_response_wrapper():
    """获取全局响应处理器实例"""
    global _global_response_wrapper
    if _global_response_wrapper is None:
        try:
            from utils.response_config import get_response_config
            from utils.response_handler import MCPResponseWrapper
            response_config = get_response_config()
            _global_response_wrapper = MCPResponseWrapper(response_config)
            logger.info("Global response handler initialized successfully")
        except Exception as e:
            logger.warning(f"Failed to initialize global response handler: {e}")
            _global_response_wrapper = None
    return _global_response_wrapper

def error_handler(func):
    """Enhanced decorator for error handling, logging, database storage and response processing"""
    @wraps(func)
    async def wrapper(*args, **kwargs):
        tool_name = func.__name__
        start_time = time.time()

        # 提取函数参数名和值
        try:
            sig = inspect.signature(func)
            bound_args = sig.bind(*args, **kwargs)
            bound_args.apply_defaults()

            # 过滤掉 self 参数
            parameters = {k: v for k, v in bound_args.arguments.items() if k != 'self'}
        except Exception as e:
            logger.error(f"参数绑定失败 {tool_name}: {e}")
            parameters = {}

        logger.info(f"Starting {tool_name} with args: {args[1:]} kwargs: {kwargs}")

        try:
            result = await func(*args, **kwargs)
            end_time = time.time()
            execution_time = int(end_time - start_time)
            execution_time_ms = int((end_time - start_time) * 1000)

            # 获取全局响应处理器
            response_wrapper = get_global_response_wrapper()

            # 应用响应处理
            if response_wrapper:
                try:
                    processed_result, metadata = response_wrapper.process_response(result)

                    # 记录处理统计
                    if metadata.was_truncated:
                        logger.info(f"Response processed for {tool_name}: "
                                  f"strategy={metadata.strategy.value}, "
                                  f"original={metadata.original_size}B, "
                                  f"processed={metadata.processed_size}B, "
                                  f"truncated={metadata.was_truncated}")

                    result = processed_result
                except Exception as e:
                    logger.warning(f"Response processing failed for {tool_name}: {e}")
                    # 继续使用原始结果

            logger.info(f"Completed {tool_name} successfully in {execution_time}s")

            # 简化的数据库记录 - 使用线程池避免事件循环问题
            try:
                import threading
                from database.db_manager import _db_manager
                if _db_manager and _db_manager._initialized:
                    # 在新线程中记录，避免事件循环冲突
                    def record_in_thread():
                        try:
                            import asyncio
                            import psycopg2
                            from config.configmanager import ConfigManager

                            # 使用同步连接记录
                            config_manager = ConfigManager('config/config.yaml')
                            db_config = config_manager.get('Database')

                            conn = psycopg2.connect(
                                host=db_config['host'],
                                port=db_config['port'],
                                user=db_config['user'],
                                password=db_config['password'],
                                database=db_config['name']
                            )

                            with conn.cursor() as cursor:
                                cursor.execute("""
                                    INSERT INTO mcp_tool_calls (
                                        call_id, tool_name, function_name, call_parameters,
                                        call_result, result_size, status, execution_time_ms,
                                        start_time, end_time, metadata
                                    ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                                """, (
                                    str(__import__('uuid').uuid4()), tool_name, func.__name__,
                                    __import__('json').dumps(parameters), str(result), len(str(result)),
                                    'success', execution_time_ms,
                                    __import__('datetime').datetime.now(__import__('datetime').timezone.utc),
                                    __import__('datetime').datetime.now(__import__('datetime').timezone.utc),
                                    __import__('json').dumps({'start_timestamp': start_time, 'caller_info': 'mcp_server'})
                                ))
                            conn.commit()
                            conn.close()
                            logger.debug(f"Recorded MCP call: {tool_name} (success)")
                        except Exception as e:
                            logger.debug(f"Failed to record MCP call in thread: {e}")

                    # 启动后台线程记录
                    thread = threading.Thread(target=record_in_thread, daemon=True)
                    thread.start()
            except Exception as db_e:
                logger.debug(f"Failed to setup database recording: {db_e}")

            return result

        except Exception as e:
            end_time = time.time()
            execution_time_ms = int((end_time - start_time) * 1000)
            error_msg = f"Unexpected error in {func.__name__}: {str(e)}"
            logger.error(f"{error_msg}\n{traceback.format_exc()}")

            # 简化的数据库记录 - 错误情况
            try:
                import threading
                from database.db_manager import _db_manager
                if _db_manager and _db_manager._initialized:
                    # 在新线程中记录，避免事件循环冲突
                    def record_error_in_thread():
                        try:
                            import psycopg2
                            from config.configmanager import ConfigManager

                            # 使用同步连接记录
                            config_manager = ConfigManager('config/config.yaml')
                            db_config = config_manager.get('Database')

                            conn = psycopg2.connect(
                                host=db_config['host'],
                                port=db_config['port'],
                                user=db_config['user'],
                                password=db_config['password'],
                                database=db_config['name']
                            )

                            with conn.cursor() as cursor:
                                cursor.execute("""
                                    INSERT INTO mcp_tool_calls (
                                        call_id, tool_name, function_name, call_parameters,
                                        call_result, result_size, status, error_message, execution_time_ms,
                                        start_time, end_time, metadata
                                    ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                                """, (
                                    str(__import__('uuid').uuid4()), tool_name, func.__name__,
                                    __import__('json').dumps(parameters), error_msg, len(error_msg),
                                    'error', str(e), execution_time_ms,
                                    __import__('datetime').datetime.now(__import__('datetime').timezone.utc),
                                    __import__('datetime').datetime.now(__import__('datetime').timezone.utc),
                                    __import__('json').dumps({'start_timestamp': start_time, 'caller_info': 'mcp_server'})
                                ))
                            conn.commit()
                            conn.close()
                            logger.debug(f"Recorded MCP call: {tool_name} (error)")
                        except Exception as e:
                            logger.debug(f"Failed to record MCP call error in thread: {e}")

                    # 启动后台线程记录
                    thread = threading.Thread(target=record_error_in_thread, daemon=True)
                    thread.start()
            except Exception as db_e:
                logger.debug(f"Failed to setup database error recording: {db_e}")

            return error_msg
    return wrapper

def validate_input(validation_rules: Dict[str, Any]):
    """Enhanced decorator for input validation with comprehensive security checks"""
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # Get the bound arguments
            bound_args = func.__code__.co_varnames[1:func.__code__.co_argcount]
            func_name = func.__name__

            for i, arg_name in enumerate(bound_args):
                if i < len(args) - 1:  # Skip 'self' argument
                    value = args[i + 1]
                elif arg_name in kwargs:
                    value = kwargs[arg_name]
                else:
                    continue

                if arg_name in validation_rules:
                    rule = validation_rules[arg_name]

                    # 必需参数检查
                    if 'required' in rule and rule['required'] and not value:
                        examples = get_parameter_examples(func_name, arg_name)
                        example_text = f" Examples: {', '.join(examples[:3])}" if examples else ""
                        return f"Error: Required parameter '{arg_name}' is missing. Please provide a real value.{example_text}"

                    # 跳过空的可选参数
                    if not value:
                        continue

                    # 类型检查
                    if 'type' in rule and value is not None and not isinstance(value, rule['type']):
                        return f"Error: Parameter '{arg_name}' must be of type {rule['type'].__name__}"

                    # 长度检查
                    if 'max_length' in rule and isinstance(value, str) and len(value) > rule['max_length']:
                        return f"Error: Parameter '{arg_name}' exceeds maximum length of {rule['max_length']}"

                    # 增强的示例值检查
                    if isinstance(value, str) and is_forbidden_value(value):
                        examples = get_parameter_examples(func_name, arg_name)
                        if not examples:
                            # 根据参数名推断类型并获取建议
                            if arg_name in ['url', 'target'] and value.startswith(('http://', 'https://')):
                                examples = get_suggested_alternatives(ParameterType.URL)
                            elif arg_name in ['domain']:
                                examples = get_suggested_alternatives(ParameterType.DOMAIN)
                            elif arg_name in ['host', 'target']:
                                examples = get_suggested_alternatives(ParameterType.HOST)

                        example_text = f" Try: {', '.join(examples[:3])}" if examples else ""
                        return f"Error: '{value}' is a placeholder value. Please provide a real {arg_name}.{example_text}"

                    # 增强的格式验证
                    param_type = rule.get('param_type')
                    if param_type and isinstance(value, str):
                        is_valid, error_msg = validate_parameter_value(arg_name, value, param_type)
                        if not is_valid:
                            examples = get_parameter_examples(func_name, arg_name)
                            example_text = f" Examples: {', '.join(examples[:3])}" if examples else ""
                            return f"{error_msg}{example_text}"

                    # 传统的URL/IP格式检查（向后兼容）
                    if arg_name in ['target', 'url', 'host'] and isinstance(value, str):
                        if not (value.startswith(('http://', 'https://')) or
                                re.match(r'^\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}$', value) or
                                re.match(r'^[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?)*$', value)):
                            examples = get_parameter_examples(func_name, arg_name)
                            example_text = f" Examples: {', '.join(examples[:3])}" if examples else ""
                            return f"Error: '{value}' is not a valid format. Please provide a real {arg_name}.{example_text}"

                    # 正则表达式验证
                    if 'pattern' in rule and isinstance(value, str):
                        if not re.match(rule['pattern'], value):
                            description = rule.get('description', f"valid {arg_name}")
                            return f"Error: '{value}' does not match required format for {description}"

            return await func(*args, **kwargs)
        return wrapper
    return decorator

class WebSecMCP:
    """Enhanced WebSec MCP Server with async support and error handling"""

    def __init__(self):
        self.mcp = FastMCP(
            name="Websec-mcp-async"
        )
        self.timeout_handler = TimeoutHandler()
        self.config_manager = None
        self._shutdown_event = asyncio.Event()
        self._is_shutdown = False
        self.executor = ThreadPoolExecutor(max_workers=MAX_WORKERS)
        
        self.workflow_executor = None

        # 初始化响应处理器
        try:
            response_config = get_response_config()
            self.response_wrapper = MCPResponseWrapper(response_config)
            logger.info("Response handler initialized successfully")
        except Exception as e:
            logger.warning(f"Failed to initialize response handler: {e}")
            self.response_wrapper = None

        self.setup_signal_handlers()
        self.register_tools()
        self._db_initialized = False

    def setup_signal_handlers(self):
        """Setup graceful shutdown signal handlers"""
        def signal_handler(signum, frame):
            logger.info(f"Received signal {signum}, initiating graceful shutdown...")
            self._shutdown_event.set()

        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)


    def get_config_manager(self):
        """Lazy initialization of config manager"""
        if self.config_manager is None:
            try:
                self.config_manager = ConfigManager('config/config.yaml')
            except Exception as e:
                logger.error(f"Failed to initialize config manager: {e}")
                raise MCPError(f"Configuration error: {e}")
        return self.config_manager

    async def initialize_database(self):
        """Initialize database connection"""
        if self._db_initialized:
            return

        try:
            config_manager = self.get_config_manager()
            db_config = config_manager.get('Database')

            if db_config:
                await initialize_database(db_config)
                self._db_initialized = True
                logger.info("Database initialized successfully")
            else:
                logger.warning("Database configuration not found, skipping database initialization")
        except Exception as e:
            logger.error(f"Failed to initialize database: {e}")
            # 不抛出异常，允许服务器在没有数据库的情况下运行



    def register_tools(self):
        """Register all MCP tools"""
        self._register_scan_tools()
        self._register_web_tools()
        self._register_network_tools()
        self._register_subdomain_tools()
        self._register_ip_tools()
        # 只有在工作流执行器成功初始化时才注册工作流工具
        # if self.workflow_executor:
        #     try:
        #         self._register_workflow_tools()
        #         logger.info("工作流工具注册成功")
        #     except Exception as e:
        #         logger.error(f"工作流工具注册失败: {e}")
        #         # 不抛出异常，允许服务器在没有工作流功能的情况下运行

    def _register_scan_tools(self):
        """Register scanning tools (nuclei, tlsx, fingerprintx)"""
        @self.mcp.tool()
        @error_handler
        @validate_input({
            'target': {
                'required': True,
                'type': str,
                'max_length': 500,
                'param_type': ParameterType.TARGET,
                'description': 'Target URL or IP address. Examples: "http://scanme.nmap.org", "************", "scanme.nmap.org"'
            },
            'service': {
                'required': False,
                'type': str,
                'max_length': 100,
                'description': 'Target service type for intelligent workflow selection. Examples: "apache", "mysql", "ssh", "wordpress", "jenkins"'
            }
        })
        async def nuclei_scan_wrapper(
            target: str,
            service: Optional[str] = None,
            timeout: Optional[int] = DEFAULT_TIMEOUT
        ) -> str:
            """Nuclei Security Scanner with Intelligent Workflow Selection

            Performs comprehensive security scanning using Nuclei templates with intelligent
            workflow selection based on target service type.

            IMPORTANT PARAMETER REQUIREMENTS:
            - target: MUST be a valid URL (with http/https) or IP address
            - service: service type for intelligent workflow selection

            Usage Examples:
            - nuclei_scan_wrapper("http://example.com", "apache")
            - nuclei_scan_wrapper("***********", "ssh")
            - nuclei_scan_wrapper("http://jenkins.example.com", "jenkins")

            DO NOT use placeholder values like "target.com", "example", etc.
            ALWAYS ask user for specific target if not provided.

            Args:
                target: Target URL/IP - MUST be real and accessible
                service: Service type for workflow selection (apache, mysql, ssh, wordpress, etc.)
                timeout: Max execution time in seconds (default: 300)

            Returns:
                Detailed scan results with vulnerabilities found
            """

            handler = TimeoutHandler(timeout or DEFAULT_TIMEOUT)
            raw_result = await handler.run_with_timeout(run_nuclei, target, service)

            return raw_result

        @self.mcp.tool()
        @error_handler
        @validate_input({
            'host': {
                'required': True,
                'type': str,
                'max_length': 255,
                'param_type': ParameterType.HOST,
                'description': 'Target hostname or IP address for TLS analysis. Examples: "scanme.nmap.org", "************", "httpbin.org"'
            },
            'port': {
                'type': int,
                'description': 'Target port number for TLS service (default: 443). Examples: 443, 8443, 9443'
            }
        })
        async def tlsx_wrapper(
            host: str,
            port: Optional[int] = 443,
            timeout: Optional[int] = DEFAULT_TIMEOUT
        ) -> str:
            """Advanced TLS/SSL Configuration Analyzer

            Comprehensive TLS/SSL security analysis and certificate inspection tool.

            IMPORTANT PARAMETER REQUIREMENTS:
            - host: MUST be a real hostname or IP address
            - port: Valid port number (1-65535), default 443 for HTTPS

            Usage Examples:
            - tlsx_wrapper("scanme.nmap.org")
            - tlsx_wrapper("httpbin.org", 443)
            - tlsx_wrapper("************", 8443)

            Analysis Features:
            - Certificate chain validation
            - Cipher suite enumeration
            - TLS version detection
            - Security configuration assessment

            DO NOT use placeholder values like "example.com", "localhost", etc.
            ALWAYS ask user for specific target if not provided.

            Args:
                host: Target hostname/IP - MUST be real and accessible
                port: Target port number (default: 443)
                timeout: Max execution time in seconds (default: 300)

            Returns:
                Comprehensive TLS/SSL analysis results in JSON format
            """
            if port and (port < 1 or port > 65535):
                return("Port must be between 1 and 65535")

            handler = TimeoutHandler(timeout or DEFAULT_TIMEOUT)
            raw_result = await handler.run_with_timeout(run_tlsx, host, port)

            return raw_result

        @self.mcp.tool()
        @error_handler
        @validate_input({
            'ip': {
                'required': True,
                'type': str,
                'max_length': 255,
                'param_type': ParameterType.IP,
                'description': 'Target IP address for service fingerprinting. Examples: "************", "*******", "*******"'
            },
            'port': {
                'required': True,
                'type': str,
                'max_length': 10,
                'description': 'Target port number as string. Examples: "80", "443", "22", "3389"'
            }
        })
        async def fingerprintx_wrapper(
            ip: str,
            port: str,
            timeout: Optional[int] = DEFAULT_TIMEOUT
        ) -> str:
            """Advanced Service Fingerprinting Tool

            Accurate service and version detection for network services.

            IMPORTANT PARAMETER REQUIREMENTS:
            - ip: MUST be a valid IP address (not hostname)
            - port: Valid port number as string (1-65535)

            Usage Examples:
            - fingerprintx_wrapper("************", "80")
            - fingerprintx_wrapper("*******", "53")
            - fingerprintx_wrapper("*******", "443")

            Detection Features:
            - Service identification
            - Version detection
            - Protocol analysis
            - Banner grabbing

            DO NOT use placeholder values like "127.0.0.1", "localhost", etc.
            ALWAYS ask user for specific IP address if not provided.

            Args:
                ip: Target IP address - MUST be valid IPv4 address
                port: Target port number as string - MUST be 1-65535
                timeout: Max execution time in seconds (default: 300)

            Returns:
                Detailed service fingerprint and version information
            """
            try:
                port_int = int(port)
                if port_int < 1 or port_int > 65535:
                    return("Port must be between 1 and 65535")
            except ValueError:
                return("Port must be a valid integer")

            handler = TimeoutHandler(timeout or DEFAULT_TIMEOUT)
            raw_result = await handler.run_with_timeout(run_fingerprintx, ip, port)

            return raw_result

    def _register_web_tools(self):
        """Register web testing tools (ffuf, spray, httpx, katana)"""

        @self.mcp.tool()
        @error_handler
        @validate_input({
            'url': {
                'required': True,
                'type': str,
                'max_length': 500,
                'param_type': ParameterType.URL,
                'description': 'Target URL with protocol. Example: "http://scanme.nmap.org" or "https://httpbin.org"'
            }
        })
        async def spray_wrapper(
            url: str,
            timeout: Optional[int] = DEFAULT_TIMEOUT
        ) -> str:
            """Web Directory Brute Forcer

            Advanced directory and file enumeration tool.

            IMPORTANT PARAMETER REQUIREMENTS:
            - url: MUST be a complete URL with http/https protocol

            Usage Examples:
            - spray_wrapper("http://example.com")
            - spray_wrapper("https://target.com:8080")
            - spray_wrapper("https://subdomain.company.com")

            DO NOT use placeholder URLs like "example.com", "target", etc.
            ALWAYS ask user for specific URL if not provided.

            Args:
                url: Target URL - MUST include protocol (http/https)
                timeout: Max execution time in seconds (default: 300)

            Returns:
                Directory enumeration results with discovered paths
            """

            handler = TimeoutHandler(timeout or DEFAULT_TIMEOUT)
            raw_result = await handler.run_with_timeout(run_spray, url)

            return raw_result

        @self.mcp.tool()
        @error_handler
        @validate_input({
            'url': {
                'required': True,
                'type': str,
                'max_length': 500,
                'param_type': ParameterType.URL,
                'description': 'Target URL for HTTP probing. Examples: "http://scanme.nmap.org", "https://httpbin.org"'
            },
            'method': {
                'required': True,
                'type': str,
                'max_length': 10,
                'description': 'HTTP method to use. Examples: "GET", "POST", "PUT", "DELETE", "HEAD", "OPTIONS"'
            },
            'headers': {
                'type': dict,
                'max_length': 500,
                'description': 'HTTP headers as dictionary. Example: {"User-Agent": "Mozilla/5.0", "Accept": "application/json"}'
            },
            'data': {
                'type': str,
                'max_length': 1000,
                'description': 'HTTP request body data. Examples: "param1=value1&param2=value2", \'{"key": "value"}\''
            }
        })
        async def httpx_wrapper(
            url: str,
            method: str = "GET",
            headers: Optional[dict] = None,
            data: Optional[str] = None,
            timeout: Optional[int] = DEFAULT_TIMEOUT
        ) -> str:
            """Advanced HTTP Probe & Analysis Tool

            Fast and multi-purpose HTTP toolkit for probing web servers and applications.

            IMPORTANT PARAMETER REQUIREMENTS:
            - url: MUST be a complete URL with http/https protocol
            - method: Valid HTTP method (GET, POST, PUT, DELETE, HEAD, OPTIONS, PATCH)
            - headers: Optional HTTP headers as dictionary format
            - data: Optional request body for POST/PUT requests

            Usage Examples:
            - httpx_wrapper("http://scanme.nmap.org")
            - httpx_wrapper("https://httpbin.org/get", "GET")
            - httpx_wrapper("https://httpbin.org/post", "POST", {"Content-Type": "application/json"}, '{"test": "data"}')
            - httpx_wrapper("http://testphp.vulnweb.com", "HEAD")

            Analysis Features:
            - HTTP response analysis
            - Header inspection
            - Status code detection
            - Technology fingerprinting
            - Custom request crafting

            DO NOT use placeholder URLs like "example.com", "localhost", etc.
            ALWAYS ask user for specific URL if not provided.

            Args:
                url: Target URL - MUST include protocol (http/https)
                method: HTTP method (default: GET) - Must be valid HTTP verb
                headers: HTTP headers dictionary (optional)
                data: Request body data (optional, mainly for POST/PUT)
                timeout: Max execution time in seconds (default: 300)

            Returns:
                Comprehensive HTTP probe results with response analysis
            """
            valid_methods = ['GET', 'POST', 'PUT', 'DELETE', 'HEAD', 'OPTIONS', 'PATCH']
            if method and method.upper() not in valid_methods:
                return(f"Invalid HTTP method. Must be one of: {', '.join(valid_methods)}")

            handler = TimeoutHandler(timeout or DEFAULT_TIMEOUT)
            raw_result = await handler.run_with_timeout(run_httpx, url, method, headers, data)

            return raw_result

        @self.mcp.tool()
        @error_handler
        @validate_input({
            'target': {
                'required': True,
                'type': str,
                'max_length': 500,
                'param_type': ParameterType.URL,
                'description': 'Target URL to crawl. Example: "http://scanme.nmap.org" or "https://httpbin.org"'
            }
        })
        async def katana_wrapper(
            target: str,
            timeout: Optional[int] = DEFAULT_TIMEOUT
        ) -> str:
            """Advanced Web Crawler

            Next-generation crawling and spidering framework.

            IMPORTANT PARAMETER REQUIREMENTS:
            - target: MUST be a complete URL with http/https protocol

            Usage Examples:
            - katana_wrapper("http://example.com")
            - katana_wrapper("https://target.com:8080")
            - katana_wrapper("https://app.company.com/dashboard")

            DO NOT use placeholder URLs like "example.com", "target", etc.
            ALWAYS ask user for specific URL if not provided.

            Args:
                target: Target URL - MUST include protocol (http/https)
                timeout: Max execution time in seconds (default: 300)

            Returns:
                Comprehensive crawling results with discovered endpoints
            """
            handler = TimeoutHandler(timeout or DEFAULT_TIMEOUT)
            raw_result = await handler.run_with_timeout(run_katana, target)

            return raw_result

        @self.mcp.tool()
        @error_handler
        @validate_input({
            'target': {
                'type': str,
                'max_length': 500,
                'param_type': ParameterType.URL,
                'description': 'Single target URL to scan. Example: "http://scanme.nmap.org" or "https://httpbin.org"'
            }
        })
        async def webleak_wrapper(
            target: Optional[str] = None,
            timeout: Optional[int] = DEFAULT_TIMEOUT
        ) -> str:
            """Web Application Vulnerability Scanner

            Scan common web services for weak passwords and unauthorized vulnerabilities.

            IMPORTANT PARAMETER REQUIREMENTS:
            - target: Single URL with http/https protocol

            Usage Examples:
            - webleak_wrapper(target="http://scanme.nmap.org")

            Vulnerability Detection Features:
            - Weak password detection
            - Unauthorized access checks

            DO NOT use placeholder URLs like "example.com", "localhost", etc.
            ALWAYS ask user for specific URL if not provided.

            Args:
                target: Single target URL - MUST include protocol (http/https)

            Returns:
                Web vulnerability scan results with detected issues
            """
            if not target:
                return "Error: Either target URL must be specified"

            handler = TimeoutHandler(timeout or DEFAULT_TIMEOUT)
            raw_result = await handler.run_with_timeout(
                run_webleak,
                target
            )

            return raw_result

        @self.mcp.tool()
        @error_handler
        @validate_input({
            'target': {
                'required': True,
                'type': str,
                'max_length': 500,
                'param_type': ParameterType.URL,
                'description': 'Target URL with protocol for JS API analysis. Examples: "https://example.com", "http://target.com:8080"'
            },
            'timeout': {
                'required': False,
                'type': int,
                'description': 'Request timeout in seconds (default: 10)'
            },
            'max_depth': {
                'required': False,
                'type': int,
                'description': 'Maximum crawling depth (default: 2)'
            },
            'auth_headers': {
                'required': False,
                'type': str,
                'description': 'Authentication headers in JSON format. Example: \'{"Authorization": "Bearer token"}\''
            },
            'max_workers': {
                'required': False,
                'type': int,
                'description': 'Maximum concurrent workers (default: 10)'
            },
            'include_404': {
                'required': False,
                'type': bool,
                'description': 'Include 404 status code APIs in results (default: False)'
            },
            'verbose': {
                'required': False,
                'type': bool,
                'description': 'Enable verbose output (default: False)'
            }
        })
        async def js_api_analyzer_wrapper(
            target: str,
            timeout: Optional[int] = DEFAULT_TIMEOUT
        ) -> str:
            """JavaScript API Analyzer - Valid APIs Only

            Analyze JavaScript files to discover and test API endpoints, returning only valid APIs.

            IMPORTANT PARAMETER REQUIREMENTS:
            - target: MUST be a complete URL with http/https protocol

            Usage Examples:
            - js_api_analyzer_wrapper("https://example.com")

            Features:
            - Crawls JavaScript files from target website
            - Extracts API endpoints using intelligent pattern matching
            - Tests discovered APIs for validity
            - Returns only working APIs (non-404 by default)
            - Supports authentication headers
            - Configurable crawling depth and concurrency

            DO NOT use placeholder URLs like "example.com", "target", etc.
            ALWAYS ask user for specific URL if not provided.

            Args:
                target: Target URL - MUST include protocol (http/https)
                timeout: Request timeout in seconds (default: 10)

            Returns:
                JSON formatted list of valid API endpoints with status codes
            """
            if not target.startswith(('http://', 'https://')):
                return json.dumps({
                    'error': 'Target URL must include protocol (http:// or https://)',
                    'example': 'https://example.com'
                }, ensure_ascii=False, indent=2)

            # 直接调用异步函数，因为get_valid_apis_only是异步的
            raw_result = await get_valid_apis_only(
                target=target,
                timeout=timeout or 10,
                max_depth=2,
                auth_headers=None,
                max_workers=10,
                include_404=False,
                verbose=False
            )

            return raw_result

    def _register_network_tools(self):
        """Register network scanning tools (rustscan, netexec)"""
        @self.mcp.tool()
        @error_handler
        @validate_input({
            'target': {
                'required': True,
                'type': str,
                'max_length': 255,
                'param_type': ParameterType.HOST,
                'description': 'Target IP address or hostname. Examples: "************", "scanme.nmap.org"'
            },
            'ports': {
                'required': True,
                'type': str,
                'max_length': 100,
                'description': 'Port specification. Examples: "80,443", "1-1000", "22,80,443,8080"'
            }
        })
        async def rustscan_wrapper(
            target: str,
            ports: str = 'top',
            timeout: Optional[int] = DEFAULT_TIMEOUT
        ) -> str:
            """Ultra-Fast Port Scanner

            Modern, blazing-fast port scanner built in Rust.

            IMPORTANT PARAMETER REQUIREMENTS:
            - target: MUST be a real IP address or resolvable hostname
            - ports: Optional port specification (default: 1-65535 ports)

            Usage Examples:
            - rustscan_wrapper("***********")
            - rustscan_wrapper("scanme.nmap.org", "22,80,443")
            - rustscan_wrapper("********", "1-65535")
            - rustscan_wrapper("***********", "top")

            DO NOT use placeholder targets like "localhost", "example.com", etc.
            ALWAYS ask user for specific target if not provided.

            Args:
                target: Target IP/hostname - MUST be real and accessible
                ports: Port range or list (optional, defaults to top 1000)
                timeout: Max execution time in seconds (default: 300)

            Returns:
                Fast port scan results with discovered open ports
            """
            handler = TimeoutHandler(timeout or DEFAULT_TIMEOUT)
            raw_result = await handler.run_with_timeout(run_rustscan, target, ports)

            return raw_result

    async def _call_rustscan_tool(self, target: str, ports: str = "1-65535", timeout: int = DEFAULT_TIMEOUT) -> str:
        """内部调用rustscan工具的方法"""
        handler = TimeoutHandler(timeout or DEFAULT_TIMEOUT)
        return await handler.run_with_timeout(run_rustscan, target, ports)

        @self.mcp.tool()
        @error_handler
        @validate_input({
            'protocol': {
                'required': True,
                'type': str,
                'max_length': 20,
                'param_type': ParameterType.PROTOCOL,
                'description': 'Protocol for authentication. Supported: ssh, smb, winrm, wmi, mssql'
            },
            'target': {
                'required': True,
                'type': str,
                'max_length': 500,
                'param_type': ParameterType.HOST,
                'description': 'Target IP address or hostname. Examples: "***********00", "scanme.nmap.org"'
            },
            'port': {
                'required': True,
                'type': str,
                'max_length': 20,
                'description': 'Target port number as string. Examples: "22" (SSH), "445" (SMB), "5985" (WinRM), "1433" (MSSQL)'
            },
            'user': {
                'required': True,
                'type': str,
                'max_length': 20,
                'description': 'Username for authentication. Examples: "admin", "administrator", "root", "sa"'
            },
            'password': {
                'required': True,
                'type': str,
                'max_length': 50,
                'description': 'Password for authentication. Must be real password, not placeholder'
            },
            'command': {
                'required': True,
                'type': str,
                'max_length': 500,
                'description': 'Command to execute. Examples: "whoami", "id", "SELECT @@version" (for MSSQL)'
            }
        })
        async def netexec_wrapper(
            protocol: str,
            target: str,
            port: str,
            user: str,
            password: str,
            command: str,
            timeout: Optional[int] = DEFAULT_TIMEOUT
        ) -> str:
            """Network Service Exploitation & Command Execution

            Advanced network service penetration testing and remote command execution tool.

            IMPORTANT PARAMETER REQUIREMENTS:
            - protocol: MUST be one of: ssh, smb, winrm, wmi, mssql
            - target: MUST be a real IP address or resolvable hostname
            - port: MUST be valid port for the specified protocol
            - user: MUST be a real username (not placeholder)
            - password: MUST be a real password (not placeholder)
            - command: MUST be a valid command for the target system

            Protocol & Port Examples:
            - SSH: netexec_wrapper("ssh", "***********00", "22", "root", "password123", "whoami")
            - SMB: netexec_wrapper("smb", "***********00", "445", "administrator", "password123", "whoami")
            - WinRM: netexec_wrapper("winrm", "***********00", "5985", "administrator", "password123", "whoami")
            - MSSQL: netexec_wrapper("mssql", "***********00", "1433", "sa", "password123", "SELECT @@version")

            Use Cases:
            - Remote command execution
            - Service authentication testing
            - Lateral movement simulation
            - Database query execution (MSSQL)

            SECURITY WARNING:
            - Only use with explicit authorization
            - Use real credentials for authorized testing
            - Commands will be executed on target system

            DO NOT use placeholder values like "localhost", "admin", "password", etc.
            ALWAYS ensure you have authorization before executing commands.

            Args:
                protocol: Authentication protocol - Must be supported type
                target: Target host - MUST be real and accessible
                port: Target port - Must match protocol requirements
                user: Username - MUST be real credential
                password: Password - MUST be real credential
                command: Command to execute - Will run on target system
                timeout: Max execution time in seconds (default: 300)

            Returns:
                Command execution results from target system
            """
            handler = TimeoutHandler(timeout or DEFAULT_TIMEOUT)
            raw_result = await handler.run_with_timeout(run_netexec, protocol, target, port, user, password, command)

            return raw_result

        @self.mcp.tool()
        @error_handler
        @validate_input({
            'protocol': {
                'required': True,
                'type': str,
                'max_length': 20,
                'param_type': ParameterType.PROTOCOL,
                'description': 'Protocol to attack. Supported: ssh, ftp, smb, mysql, mssql, redis, mongodb, snmp, imap, pop3, smtp, http, https, rdp, vnc'
            },
            'targets': {
                'required': True,
                'type': str,
                'max_length': 500,
                'description': 'Target services in format "IP:PORT". Examples: "***********00:22", "***********00:22,***********01:3389"'
            },
            'usernames': {
                'type': str,
                'max_length': 500,
                'description': 'Usernames for brute force. Examples: "admin,root,user", "/path/to/userlist.txt", or leave empty for default dictionary'
            },
            'passwords': {
                'type': str,
                'max_length': 500,
                'description': 'Passwords for brute force. Examples: "password123,admin,123456", "/path/to/passlist.txt", or leave empty for default dictionary'
            }
        })
        async def crack_wrapper(
            protocol: str,
            targets: str,
            usernames: Optional[str] = None,
            passwords: Optional[str] = None,
            timeout: Optional[int] = DEFAULT_TIMEOUT
        ) -> str:
            """Network Service Brute Force & Credential Attack

            Advanced brute force tool for network service authentication testing.

            IMPORTANT PARAMETER REQUIREMENTS:
            - protocol: MUST be one of the supported protocols
            - targets: MUST be in "IP:PORT" format with real IP addresses
            - usernames: Optional username list (uses default dictionary if empty)
            - passwords: Optional password list (uses default dictionary if empty)

            Protocol & Default Port Examples:
            - SSH: crack_wrapper("ssh", "***********00:22", "root,admin", "password123,admin")
            - FTP: crack_wrapper("ftp", "***********00:21", "admin,user", "password,123456")
            - SMB: crack_wrapper("smb", "***********00:445", "administrator,guest", "password123")
            - MySQL: crack_wrapper("mysql", "***********00:3306", "root,mysql", "password,root")
            - MSSQL: crack_wrapper("mssql", "***********00:1433", "sa,admin", "password123")
            - RDP: crack_wrapper("rdp", "***********00:3389", "administrator", "password123")

            Attack Features:
            - Multi-protocol support
            - Concurrent connection testing
            - Custom wordlist support
            - Built-in common credentials
            - Batch target processing

            Username/Password Formats:
            - Comma-separated: "admin,root,user"
            - File path: "/path/to/wordlist.txt"
            - Empty: Uses protocol-specific default dictionary

            SECURITY WARNING:
            - Only use with explicit authorization
            - Can trigger account lockouts
            - May generate significant network traffic
            - Results may contain sensitive credentials

            DO NOT use placeholder values like "localhost", "example.com", etc.
            ALWAYS ensure you have authorization before attacking services.

            Args:
                protocol: Target protocol - Must be from supported list
                targets: Target services - Format: "IP:PORT" or "IP1:PORT1,IP2:PORT2"
                usernames: Username list - Comma-separated, file path, or empty for defaults
                passwords: Password list - Comma-separated, file path, or empty for defaults
                timeout: Max execution time in seconds (default: 300)

            Returns:
                Brute force attack results with discovered credentials
            """
            if protocol not in ["ssh", "http_proxy", "ftp", "telnet", "mysql", "postgresql", "oracle", "mssql", "redis", "mongodb", "smb", "rdp", "vnc", "snmp", "imap", "pop3", "smtp", "http", "https"]:
                return(f"Unsupported protocol: {protocol}")

            handler = TimeoutHandler(timeout or DEFAULT_TIMEOUT)
            raw_result = await handler.run_with_timeout(run_crack, protocol, targets, usernames, passwords)

            return raw_result

    def _register_subdomain_tools(self):
        """Register subdomain enumeration tools"""
        @self.mcp.tool()
        @error_handler
        @validate_input({
            'domain': {
                'required': True,
                'type': str,
                'max_length': 255,
                'param_type': ParameterType.DOMAIN,
                'description': 'Target domain for comprehensive subdomain discovery. Examples: "hackerone.com", "bugcrowd.com"'
            },
            'timeout': {
                'type': int,
                'description': 'DNS resolution timeout in seconds (default: 3)'
            }
        })
        async def subdomain_wrapper(
            domain: str,
            timeout: Optional[int] = DEFAULT_TIMEOUT
        ) -> str:
            """Comprehensive Subdomain Discovery Tool

            Executes all available subdomain discovery tools, integrates results,
            performs DNS validation, and detects wildcard DNS configurations.

            IMPORTANT PARAMETER REQUIREMENTS:
            - domain: MUST be a real, valid domain name

            Features:
            - Runs subfinder, CRT.sh, VirusTotal, and SecurityTrails
            - Merges and deduplicates all results
            - DNS resolution validation
            - Wildcard DNS detection
            - Filters out unresolvable domains

            Usage Examples:
            - subdomain_wrapper("hackerone.com")
            - subdomain_wrapper("target.org", timeout=5)

            DO NOT use placeholder domains like "example.com", "target.com", etc.
            ALWAYS ask user for specific domain if not provided.

            Args:
                domain: Target domain - MUST be real and valid
                validate_dns: Enable DNS validation and wildcard detection (default: true)
                timeout: DNS resolution timeout in seconds (default: 3)

            Returns:
                JSON formatted comprehensive subdomain discovery results
            """
            try:
                # 获取API密钥
                config_manager = self.get_config_manager()
                vt_api_key = config_manager.get('Subdomain', 'virustotal_api_key')
                st_api_key = config_manager.get('Subdomain', 'securitytrails_api_key')

                # 执行综合子域名发现
                handler = TimeoutHandler(timeout or DEFAULT_TIMEOUT)
                raw_result = await handler.run_with_timeout(
                    comprehensive_subdomain_discovery,
                    domain,
                    vt_api_key,
                    st_api_key,
                    float(3)
                )

                return raw_result

            except Exception as e:
                logger.error(f"Comprehensive subdomain discovery failed: {e}")
                return json.dumps({
                    "error": f"Comprehensive subdomain discovery failed: {str(e)}",
                    "domain": domain
                }, ensure_ascii=False)

    def _register_ip_tools(self):
        """Register IP analysis tools"""
        @self.mcp.tool()
        @error_handler
        @validate_input({
            'ip': {
                'required': True,
                'type': str,
                'max_length': 45,  # IPv6 addresses can be up to 45 characters
                'param_type': ParameterType.IP,
                'description': 'IP address to query location information. Examples: "*******", "*******", "***************"'
            }
        })
        async def iplocation_wrapper(
            ip: str,
            timeout: Optional[int] = DEFAULT_TIMEOUT
        ) -> str:
            """IP Address Geolocation & Information Lookup

            Query comprehensive IP address information including geolocation, ISP, and network details.

            IMPORTANT PARAMETER REQUIREMENTS:
            - ip: MUST be a valid IPv4 or IPv6 address

            Usage Examples:
            - iplocation_wrapper("*******")
            - iplocation_wrapper("*******")
            - iplocation_wrapper("***************")
            - iplocation_wrapper("2001:4860:4860::8888")

            Information Provided:
            - Geographic location (country, region, city)
            - ISP and organization details
            - Network information
            - Timezone information

            DO NOT use placeholder IPs like "127.0.0.1", "***********", etc.
            ALWAYS ask user for specific public IP address if not provided.

            Args:
                ip: Target IP address - MUST be valid IPv4/IPv6 address
                timeout: Max execution time in seconds (default: 300)

            Returns:
                Comprehensive IP geolocation and network information in JSON format
            """
            handler = TimeoutHandler(timeout or DEFAULT_TIMEOUT)
            raw_result = await handler.run_with_timeout(iplocation, ip)

            return raw_result

        @self.mcp.tool()
        @error_handler
        @validate_input({
            'ip': {
                'required': True,
                'type': str,
                'max_length': 45,  # IPv6 addresses can be up to 45 characters
                'param_type': ParameterType.IP,
                'description': 'IP address to query historical domain resolutions. Examples: "*******", "*******", "***************"'
            }
        })
        async def ipreverse_wrapper(
            ip: str,
            timeout: Optional[int] = DEFAULT_TIMEOUT
        ) -> str:
            """IP Address Historical Domain Resolution Lookup

            Query historical domain names that have resolved to the specified IP address using VirusTotal intelligence.

            IMPORTANT PARAMETER REQUIREMENTS:
            - ip: MUST be a valid IPv4 or IPv6 address
            - Requires VirusTotal API key configuration

            Usage Examples:
            - ipreverse_wrapper("*******")
            - ipreverse_wrapper("*******")
            - ipreverse_wrapper("***************")
            - ipreverse_wrapper("2001:4860:4860::8888")

            Information Provided:
            - Historical domain names that resolved to the IP
            - Resolution timestamps
            - Total and unique domain counts
            - Domain list for easy processing

            DO NOT use placeholder IPs like "127.0.0.1", "***********", etc.
            ALWAYS ask user for specific public IP address if not provided.

            Args:
                ip: Target IP address - MUST be valid IPv4/IPv6 address
                timeout: Max execution time in seconds (default: 300)

            Returns:
                Historical domain resolution records in JSON format with domain list and metadata
            """
            try:
                config_manager = self.get_config_manager()
                vt_api_key = config_manager.get('Subdomain', 'virustotal_api_key')

                if not vt_api_key:
                    return json.dumps({
                        "error": "VirusTotal API key not configured",
                        "message": "Please configure VirusTotal API key in config file"
                    }, ensure_ascii=False)

                handler = TimeoutHandler(timeout or DEFAULT_TIMEOUT)
                raw_result = await handler.run_with_timeout(ipreverse, ip, vt_api_key)

                return raw_result

            except Exception as e:
                logger.error(f"IP reverse lookup failed: {e}")
                return json.dumps({
                    "error": f"IP reverse lookup failed: {str(e)}"
                }, ensure_ascii=False)

        @self.mcp.tool()
        @error_handler
        @validate_input({
            'ip': {
                'required': True,
                'type': str,
                'max_length': 45,  # IPv6 addresses can be up to 45 characters
                'param_type': ParameterType.IP,
                'description': 'IP address to query ASN and organization information. Examples: "*******", "*******", "***************"'
            }
        })
        async def ipasn_wrapper(
            ip: str,
            timeout: Optional[int] = DEFAULT_TIMEOUT
        ) -> str:
            """IP Address ASN & Organization Information Lookup

            Query comprehensive IP address ASN (Autonomous System Number) and organization details using IPinfo.io.

            IMPORTANT PARAMETER REQUIREMENTS:
            - ip: MUST be a valid IPv4 or IPv6 address
            - Requires IPinfo.io API key configuration

            Usage Examples:
            - ipasn_wrapper("*******")
            - ipasn_wrapper("*******")
            - ipasn_wrapper("***************")
            - ipasn_wrapper("2001:4860:4860::8888")

            Information Provided:
            - ASN (Autonomous System Number)
            - Organization name and details
            - ISP information
            - Geographic location
            - Network range information
            - Hosting provider details

            DO NOT use placeholder IPs like "127.0.0.1", "***********", etc.
            ALWAYS ask user for specific public IP address if not provided.

            Args:
                ip: Target IP address - MUST be valid IPv4/IPv6 address
                timeout: Max execution time in seconds (default: 300)

            Returns:
                Comprehensive ASN and organization information in JSON format
            """
            try:
                config_manager = self.get_config_manager()
                # 从配置中获取IPinfo API密钥
                ipinfo_api_key = config_manager.get('Ipinformation', 'ipinfo')

                if not ipinfo_api_key:
                    return json.dumps({
                        "error": "IPinfo.io API key not configured",
                        "message": "Please configure IPinfo.io API key in config file under 'Ipinformation.ipinfo'"
                    }, ensure_ascii=False)

                handler = TimeoutHandler(timeout or DEFAULT_TIMEOUT)
                raw_result = await handler.run_with_timeout(ipasn, ip, ipinfo_api_key)

                return raw_result

            except Exception as e:
                logger.error(f"IP ASN lookup failed: {e}")
                return json.dumps({
                    "error": f"IP ASN lookup failed: {str(e)}"
                }, ensure_ascii=False)

    # def _register_workflow_tools(self):
    #     """注册工作流相关工具"""
    #     
    #     @self.mcp.tool()
    #     @error_handler
    #     async def list_workflows_wrapper() -> str:
    #         """列出可用的渗透测试工作流
    #         
    #         显示所有预定义的渗透测试工作流及其描述，帮助用户选择合适的测试流程。
    #         
    #         Returns:
    #             可用工作流列表及描述
    #         """
    #         try:
    #             workflows = self.workflow_executor.get_available_workflows()
    #             
    #             result = {
    #                 "available_workflows": workflows,
    #                 "total_count": len(workflows),
    #                 "usage_tip": "使用 execute_workflow_wrapper 执行指定的工作流"
    #             }
    #             
    #             return json.dumps(result, ensure_ascii=False, indent=2)
    #             
    #         except Exception as e:
    #             logger.error(f"获取工作流列表失败: {e}")
    #             return json.dumps({
    #                 "error": f"获取工作流列表失败: {str(e)}"
    #             }, ensure_ascii=False)

    #     @self.mcp.tool()
    #     @error_handler
    #     @validate_input({
    #         'target': {
    #             'required': True,
    #             'type': str,
    #             'max_length': 500,
    #             'param_type': ParameterType.TARGET,
    #             'description': '目标地址，可以是URL、IP地址或域名。例如: "http://example.com", "***********", "example.com"'
    #         }
    #     })
    #     async def detect_target_type_wrapper(target: str) -> str:
    #         """检测目标类型并推荐工作流
    #         
    #         分析目标地址的类型（URL、IP、域名），并推荐最适合的渗透测试工作流。
    #         
    #         Args:
    #             target: 目标地址 - 必须是真实有效的地址
    #             
    #         Returns:
    #             目标类型分析结果和推荐的工作流
    #         """
    #         try:
    #             target_type, recommended_workflow = self.workflow_executor.detect_target_type(target)
    #             
    #             workflows = self.workflow_executor.get_available_workflows()
    #             workflow_description = workflows.get(recommended_workflow, "未知工作流")
    #             
    #             result = {
    #                 "target": target,
    #                 "target_type": target_type,
    #                 "recommended_workflow": recommended_workflow,
    #                 "workflow_description": workflow_description,
    #                 "available_workflows": workflows
    #             }
    #             
    #             return json.dumps(result, ensure_ascii=False, indent=2)
    #             
    #         except Exception as e:
    #             logger.error(f"目标类型检测失败: {e}")
    #             return json.dumps({
    #                 "error": f"目标类型检测失败: {str(e)}"
    #             }, ensure_ascii=False)

    #     @self.mcp.tool()
    #     @error_handler
    #     @validate_input({
    #         'workflow_name': {
    #             'required': True,
    #             'type': str,
    #             'max_length': 100,
    #             'description': '工作流名称。可用选项: "web_pentest", "network_pentest", "subdomain_discovery"'
    #         },
    #         'target': {
    #             'required': True,
    #             'type': str,
    #             'max_length': 500,
    #             'param_type': ParameterType.TARGET,
    #             'description': '目标地址，必须是真实有效的URL、IP地址或域名'
    #         }
    #     })
    #     async def execute_workflow_wrapper(
    #         workflow_name: str,
    #         target: str,
    #         stop_on_error: Optional[bool] = False,
    #         timeout: Optional[int] = DEFAULT_TIMEOUT
    #     ) -> str:
    #         """执行指定的渗透测试工作流
    #         
    #         按照预定义的流程执行完整的渗透测试，减少工具选择的随机性，确保测试的一致性和完整性。
    #         
    #         重要参数要求:
    #         - workflow_name: 必须是有效的工作流名称
    #         - target: 必须是真实有效的目标地址
    #         
    #         可用工作流:
    #         - web_pentest: Web应用渗透测试（适用于HTTP/HTTPS服务）
    #         - network_pentest: 网络服务渗透测试（适用于IP地址）
    #         - subdomain_discovery: 子域名发现（适用于域名）
    #         
    #         使用示例:
    #         - execute_workflow_wrapper("web_pentest", "http://example.com")
    #         - execute_workflow_wrapper("network_pentest", "***********")
    #         - execute_workflow_wrapper("subdomain_discovery", "example.com")
    #         
    #         Args:
    #             workflow_name: 工作流名称 - 必须是有效的工作流
    #             target: 目标地址 - 必须是真实有效的地址
    #             stop_on_error: 遇到错误时是否停止执行（默认: False）
    #             timeout: 最大执行时间（秒，默认: 300）
    #             
    #         Returns:
    #             完整的工作流执行报告，包含所有步骤的结果
    #         """
    #         try:
    #             # 验证工作流名称
    #             available_workflows = self.workflow_executor.get_available_workflows()
    #             if workflow_name not in available_workflows:
    #                 return json.dumps({
    #                     "error": f"工作流 '{workflow_name}' 不存在",
    #                     "available_workflows": list(available_workflows.keys())
    #                 }, ensure_ascii=False)
    #             
    #             logger.info(f"开始执行工作流: {workflow_name}, 目标: {target}")
    #             
    #             # 执行工作流
    #             result = await self.workflow_executor.execute_workflow(
    #                 workflow_name=workflow_name,
    #                 target=target,
    #                 tool_executor=self,
    #                 stop_on_error=stop_on_error,
    #                 timeout=timeout
    #             )
    #             
    #             # 生成报告
    #             report = self.workflow_executor.generate_workflow_report(result)
    #             
    #             return report
    #             
    #         except Exception as e:
    #             logger.error(f"工作流执行失败: {e}")
    #             return json.dumps({
    #                 "error": f"工作流执行失败: {str(e)}"
    #             }, ensure_ascii=False)

    #     @self.mcp.tool()
    #     @error_handler
    #     @validate_input({
    #         'target': {
    #             'required': True,
    #             'type': str,
    #             'max_length': 500,
    #             'param_type': ParameterType.TARGET,
    #             'description': '目标地址，必须是真实有效的URL、IP地址或域名'
    #         }
    #     })
    #     async def auto_pentest_wrapper(
    #         target: str,
    #         timeout: Optional[int] = DEFAULT_TIMEOUT
    #     ) -> str:
    #         """自动渗透测试 - 智能选择工作流
    #         
    #         根据目标类型自动选择最适合的渗透测试工作流并执行，这是最简单的使用方式。
    #         
    #         功能特点:
    #         - 自动检测目标类型（URL/IP/域名）
    #         - 智能选择最适合的工作流
    #         - 执行完整的渗透测试流程
    #         - 生成详细的测试报告
    #         
    #         使用示例:
    #         - auto_pentest_wrapper("http://example.com")  # 自动选择web_pentest
    #         - auto_pentest_wrapper("***********")        # 自动选择network_pentest
    #         - auto_pentest_wrapper("example.com")         # 自动选择subdomain_discovery
    #         
    #         Args:
    #             target: 目标地址 - 必须是真实有效的地址
    #             timeout: 最大执行时间（秒，默认: 300）
    #             
    #         Returns:
    #             完整的自动渗透测试报告
    #         """
    #         try:
    #             # 检测目标类型并推荐工作流
    #             target_type, recommended_workflow = self.workflow_executor.detect_target_type(target)
    #             
    #             logger.info(f"自动渗透测试 - 目标: {target}, 类型: {target_type}, 推荐工作流: {recommended_workflow}")
    #             
    #             # 执行推荐的工作流
    #             result = await self.workflow_executor.execute_workflow(
    #                 workflow_name=recommended_workflow,
    #                 target=target,
    #                 tool_executor=self,
    #                 stop_on_error=False,
    #                 timeout=timeout
    #             )
    #             
    #             # 生成报告
    #             report = self.workflow_executor.generate_workflow_report(result)
    #             
    #             # 添加自动选择信息
    #             auto_info = f"""# 自动渗透测试报告

    # **目标类型检测**: {target_type}
    # **自动选择工作流**: {recommended_workflow}

    # ---

    # {report}"""
    #             
    #             return auto_info
    #             
    #         except Exception as e:
    #             logger.error(f"自动渗透测试失败: {e}")
    #             return json.dumps({
    #                 "error": f"自动渗透测试失败: {str(e)}"
    #             }, ensure_ascii=False)

    # 工具调用方法 - 用于工作流执行器 (已禁用)
    # async def _call_httpx_tool(self, url: str, method: str = "GET", headers: dict = None, data: str = None, timeout: int = DEFAULT_TIMEOUT) -> str:
    #     """内部调用httpx工具的方法"""
    #     handler = TimeoutHandler(timeout or DEFAULT_TIMEOUT)
    #     return await handler.run_with_timeout(run_httpx, url, method, headers, data)

    # async def _call_tlsx_tool(self, host: str, port: int = 443, timeout: int = DEFAULT_TIMEOUT) -> str:
    #     """内部调用tlsx工具的方法"""
    #     handler = TimeoutHandler(timeout or DEFAULT_TIMEOUT)
    #     return await handler.run_with_timeout(run_tlsx, host, port)

    # async def _call_spray_tool(self, url: str, timeout: int = DEFAULT_TIMEOUT) -> str:
    #     """内部调用spray工具的方法"""
    #     handler = TimeoutHandler(timeout or DEFAULT_TIMEOUT)
    #     return await handler.run_with_timeout(run_spray, url)

    # async def _call_katana_tool(self, url: str, timeout: int = DEFAULT_TIMEOUT) -> str:
    #     """内部调用katana工具的方法"""
    #     handler = TimeoutHandler(timeout or DEFAULT_TIMEOUT)
    #     return await handler.run_with_timeout(run_katana, url)

    # async def _call_webleak_tool(self, target: str, timeout: int = DEFAULT_TIMEOUT) -> str:
    #     """内部调用webleak工具的方法"""
    #     handler = TimeoutHandler(timeout or DEFAULT_TIMEOUT)
    #     return await handler.run_with_timeout(run_webleak, target)

    # async def _call_nuclei_tool(self, target: str, service: str = None, timeout: int = DEFAULT_TIMEOUT) -> str:
    #     """内部调用nuclei工具的方法"""
    #     handler = TimeoutHandler(timeout or DEFAULT_TIMEOUT)
    #     return await handler.run_with_timeout(run_nuclei, target, service)

    # async def _call_fingerprintx_tool(self, ip: str, port: str, timeout: int = DEFAULT_TIMEOUT) -> str:
    #     """内部调用fingerprintx工具的方法"""
    #     handler = TimeoutHandler(timeout or DEFAULT_TIMEOUT)
    #     return await handler.run_with_timeout(run_fingerprintx, ip, port)

    # async def _call_netexec_tool(self, protocol: str, target: str, port: str, timeout: int = DEFAULT_TIMEOUT) -> str:
    #     """内部调用netexec工具的方法"""
    #     handler = TimeoutHandler(timeout or DEFAULT_TIMEOUT)
    #     return await handler.run_with_timeout(run_netexec, protocol, target, port)

    async def _call_rustscan_tool(self, target: str, ports: str = "1-65535", timeout: int = DEFAULT_TIMEOUT) -> str:
        """内部调用rustscan工具的方法"""
        handler = TimeoutHandler(timeout or DEFAULT_TIMEOUT)
        return await handler.run_with_timeout(run_rustscan, target, ports)

    # async def _call_subdomain_tool(self, domain: str, timeout: int = DEFAULT_TIMEOUT) -> str:
    #     """内部调用子域名发现工具的方法"""
    #     try:
    #         config_manager = self.get_config_manager()
    #         subdomain_config = config_manager.get('Subdomain')
    #         
    #         handler = TimeoutHandler(timeout or DEFAULT_TIMEOUT)
    #         return await handler.run_with_timeout(
    #             comprehensive_subdomain_discovery, 
    #             domain, 
    #             subdomain_config
    #         )
    #     except Exception as e:
    #         logger.error(f"子域名发现失败: {e}")
    #         return f"子域名发现失败: {str(e)}"

    # async def _call_iplocation_tool(self, ip: str, timeout: int = DEFAULT_TIMEOUT) -> str:
    #     """内部调用IP位置查询工具的方法"""
    #     handler = TimeoutHandler(timeout or DEFAULT_TIMEOUT)
    #     return await handler.run_with_timeout(iplocation, ip)

    # async def _call_ipreverse_tool(self, ip: str, timeout: int = DEFAULT_TIMEOUT) -> str:
    #     """内部调用IP反向查询工具的方法"""
    #     try:
    #         config_manager = self.get_config_manager()
    #         vt_api_key = config_manager.get('Subdomain', 'virustotal_api_key')
    #         
    #         if not vt_api_key:
    #             return json.dumps({
    #                 "error": "VirusTotal API key not configured"
    #             }, ensure_ascii=False)
    #         
    #         handler = TimeoutHandler(timeout or DEFAULT_TIMEOUT)
    #         return await handler.run_with_timeout(ipreverse, ip, vt_api_key)
    #     except Exception as e:
    #         logger.error(f"IP反向查询失败: {e}")
    #         return json.dumps({
    #             "error": f"IP反向查询失败: {str(e)}"
    #         }, ensure_ascii=False)

    # async def _call_ipasn_tool(self, ip: str, timeout: int = DEFAULT_TIMEOUT) -> str:
    #     """内部调用IP ASN查询工具的方法"""
    #     try:
    #         config_manager = self.get_config_manager()
    #         ipinfo_api_key = config_manager.get('Ipinformation', 'ipinfo')
    #         
    #         if not ipinfo_api_key:
    #             return json.dumps({
    #                 "error": "IPinfo.io API key not configured"
    #             }, ensure_ascii=False)
    #         
    #         handler = TimeoutHandler(timeout or DEFAULT_TIMEOUT)
    #         return await handler.run_with_timeout(ipasn, ip, ipinfo_api_key)
    #     except Exception as e:
    #         logger.error(f"IP ASN查询失败: {e}")
    #         return json.dumps({
    #             "error": f"IP ASN查询失败: {str(e)}"
    #         }, ensure_ascii=False)

    # async def _call_subdomain_tool(self, domain: str, timeout: int = DEFAULT_TIMEOUT) -> str:
    #     """内部调用子域名发现工具的方法"""
    #     try:
    #         config_manager = self.get_config_manager()
    #         subdomain_config = config_manager.get('Subdomain')
    #         
    #         handler = TimeoutHandler(timeout or DEFAULT_TIMEOUT)
    #         return await handler.run_with_timeout(
    #             comprehensive_subdomain_discovery, 
    #             domain, 
    #             subdomain_config
    #         )
    #     except Exception as e:
    #         logger.error(f"子域名发现失败: {e}")
    #         return f"子域名发现失败: {str(e)}"

    # async def _call_iplocation_tool(self, ip: str, timeout: int = DEFAULT_TIMEOUT) -> str:
    #     """内部调用IP位置查询工具的方法"""
    #     handler = TimeoutHandler(timeout or DEFAULT_TIMEOUT)
    #     return await handler.run_with_timeout(iplocation, ip)

    # async def _call_ipreverse_tool(self, ip: str, timeout: int = DEFAULT_TIMEOUT) -> str:
    #     """内部调用IP反向查询工具的方法"""
    #     try:
    #         config_manager = self.get_config_manager()
    #         vt_api_key = config_manager.get('Subdomain', 'virustotal_api_key')
    #         
    #         if not vt_api_key:
    #             return json.dumps({
    #                 "error": "VirusTotal API key not configured"
    #             }, ensure_ascii=False)
    #         
    #         handler = TimeoutHandler(timeout or DEFAULT_TIMEOUT)
    #         return await handler.run_with_timeout(ipreverse, ip, vt_api_key)
    #     except Exception as e:
    #         logger.error(f"IP反向查询失败: {e}")
    #         return json.dumps({
    #             "error": f"IP反向查询失败: {str(e)}"
    #         }, ensure_ascii=False)

    # async def _call_ipasn_tool(self, ip: str, timeout: int = DEFAULT_TIMEOUT) -> str:
    #     """内部调用IP ASN查询工具的方法"""
    #     try:
    #         config_manager = self.get_config_manager()
    #         ipinfo_api_key = config_manager.get('Ipinformation', 'ipinfo')
    #         
    #         if not ipinfo_api_key:
    #             return json.dumps({
    #                 "error": "IPinfo.io API key not configured"
    #             }, ensure_ascii=False)
    #         
    #         handler = TimeoutHandler(timeout or DEFAULT_TIMEOUT)
    #         return await handler.run_with_timeout(ipasn, ip, ipinfo_api_key)
    #     except Exception as e:
    #         logger.error(f"IP ASN查询失败: {e}")
    #         return json.dumps({
    #             "error": f"IP ASN查询失败: {str(e)}"
    #         }, ensure_ascii=False)

    def run_server(self):
        """运行 MCP server 并监听关闭事件"""
        try:
            logger.info("Starting WebSec MCP Server...")
            
            # 验证服务器配置
            self._validate_server_config()

            # 同步初始化数据库（在启动前）
            try:
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                loop.run_until_complete(self.initialize_database())
                loop.close()
                logger.info("Database initialization completed")
            except Exception as db_e:
                logger.warning(f"Database initialization failed: {db_e}")
                logger.info("Server will continue without database functionality")

            # 运行 MCP 服务器（FastMCP 会处理自己的事件循环）
            logger.info("Starting MCP server on http://0.0.0.0:8080/mcp/")
            logger.info("Server endpoints:")
            logger.info("  - MCP Protocol: POST http://0.0.0.0:8080/mcp/")
            logger.info("  - Health Check: GET http://0.0.0.0:8080/")
            
            self.mcp.run(transport="streamable-http", host="0.0.0.0", port=8080)

        except Exception as e:
            logger.error(f"Server error: {e}")
            import traceback
            traceback.print_exc()
            raise
    
    def _validate_server_config(self):
        """验证服务器配置"""
        try:
            # 检查工作流执行器
            # if not self.workflow_executor:
            #     logger.warning("工作流执行器未初始化，工作流功能将不可用")
            logger.info("工作流执行器已禁用")
            
            # 检查配置管理器
            config_manager = self.get_config_manager()
            logger.info("配置管理器验证成功")
            
            # 检查响应处理器
            if not self.response_wrapper:
                logger.warning("响应处理器未初始化，将使用默认响应格式")
            
            logger.info("服务器配置验证完成")
            
        except Exception as e:
            logger.error(f"服务器配置验证失败: {e}")
            raise

def main():
    """Main entry point"""
    server = WebSecMCP()

    try:
        server.run_server()
    except KeyboardInterrupt:
        logger.info("Server stopped by user")
    except Exception as e:
        logger.error(f"Server failed to start: {e}")
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    #print(run_nuclei("http://112.74.93.147:8081/"))
    main()
