# Encoding: utf-8
import asyncio
import json
import logging
import signal
import sys
import re
import time
import inspect
import warnings
from typing import Optional, Dict, Any
from functools import wraps
import traceback
from concurrent.futures import ThreadPoolExecutor, TimeoutError as FuturesTimeoutError

from fastmcp import FastMCP

from config.configmanager import ConfigManager
from config.validation.rules import (
    validate_parameter_value, is_forbidden_value,
    get_parameter_examples, get_suggested_alternatives, ParameterType
)
from tools.nuclei import run_nuclei
from tools.spray import run_spray
from tools.crack import run_crack
from tools.rustscan import run_rustscan
from tools.wappalyzer import run_wappalyzer
from tools.katana import run_katana
from tools.httpx import run_httpx
from tools.tlsx import run_tlsx
from tools.fingerprintx import run_fingerprintx
from tools.netexec import run_netexec
from tools.webleak import run_webleak
from tools.subdomain.subdomain import comprehensive_subdomain_discovery
from tools.ip import iplocation
from tools.ipreverse import ipreverse
from tools.ipasn import ipasn
from tools.js_api_analyzer import get_valid_apis_only
from database.db_manager import initialize_database
from utils.response_handler import MCPResponseWrapper
from utils.response_config import get_response_config

# 过滤 websockets 弃用警告
warnings.filterwarnings("ignore", category=DeprecationWarning, module="websockets.legacy")
warnings.filterwarnings("ignore", category=DeprecationWarning, module="uvicorn.protocols.websockets.websockets_impl")


# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('./logs/websec_mcp.log'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

# Global configuration
DEFAULT_TIMEOUT = 400  # 5 minutes default timeout
MAX_WORKERS = 10  # Maximum number of concurrent workers
SHUTDOWN_TIMEOUT = 30  # Graceful shutdown timeout


class MCPError(Exception):
    """Custom exception for MCP operations"""
    pass

class TimeoutHandler:
    """Handle timeouts for operations"""

    def __init__(self, timeout: int = DEFAULT_TIMEOUT):
        self.timeout = timeout

    async def run_with_timeout(self, func, *args, **kwargs):
        """Run a function with timeout in thread pool"""
        loop = asyncio.get_event_loop()

        with ThreadPoolExecutor(max_workers=MAX_WORKERS) as executor:
            try:
                # Submit the blocking function to thread pool
                future = executor.submit(func, *args, **kwargs)
                # Wait for completion with timeout
                result = await asyncio.wait_for(
                    loop.run_in_executor(None, future.result),
                    timeout=self.timeout
                )
                return result
            except asyncio.TimeoutError:
                logger.error(f"Operation timed out after {self.timeout} seconds")
                return(f"Operation timed out after {self.timeout} seconds")
            except FuturesTimeoutError:
                logger.error(f"Thread pool operation timed out after {self.timeout} seconds")
                return(f"Thread pool operation timed out after {self.timeout} seconds")
            except Exception as e:
                logger.error(f"Operation failed: {str(e)}")
                return(f"Operation failed: {str(e)}")

# 全局响应处理器实例
_global_response_wrapper = None

def get_global_response_wrapper():
    """获取全局响应处理器实例"""
    global _global_response_wrapper
    if _global_response_wrapper is None:
        try:
            from utils.response_config import get_response_config
            from utils.response_handler import MCPResponseWrapper
            response_config = get_response_config()
            _global_response_wrapper = MCPResponseWrapper(response_config)
            logger.info("Global response handler initialized successfully")
        except Exception as e:
            logger.warning(f"Failed to initialize global response handler: {e}")
            _global_response_wrapper = None
    return _global_response_wrapper

def error_handler(func):
    """Enhanced decorator for error handling, logging, database storage and response processing"""
    @wraps(func)
    async def wrapper(*args, **kwargs):
        tool_name = func.__name__
        start_time = time.time()

        # 提取函数参数名和值
        try:
            sig = inspect.signature(func)
            bound_args = sig.bind(*args, **kwargs)
            bound_args.apply_defaults()

            # 过滤掉 self 参数
            parameters = {k: v for k, v in bound_args.arguments.items() if k != 'self'}
        except Exception as e:
            logger.error(f"参数绑定失败 {tool_name}: {e}")
            parameters = {}

        logger.info(f"Starting {tool_name} with args: {args[1:]} kwargs: {kwargs}")

        try:
            result = await func(*args, **kwargs)
            end_time = time.time()
            execution_time = int(end_time - start_time)
            execution_time_ms = int((end_time - start_time) * 1000)

            # 获取全局响应处理器
            response_wrapper = get_global_response_wrapper()

            # 应用响应处理
            if response_wrapper:
                try:
                    processed_result, metadata = response_wrapper.process_response(result)

                    # 记录处理统计
                    if metadata.was_truncated:
                        logger.info(f"Response processed for {tool_name}: "
                                  f"strategy={metadata.strategy.value}, "
                                  f"original={metadata.original_size}B, "
                                  f"processed={metadata.processed_size}B, "
                                  f"truncated={metadata.was_truncated}")

                    result = processed_result
                except Exception as e:
                    logger.warning(f"Response processing failed for {tool_name}: {e}")
                    # 继续使用原始结果

            logger.info(f"Completed {tool_name} successfully in {execution_time}s")

            # 简化的数据库记录 - 使用线程池避免事件循环问题
            try:
                import threading
                from database.db_manager import _db_manager
                if _db_manager and _db_manager._initialized:
                    # 在新线程中记录，避免事件循环冲突
                    def record_in_thread():
                        try:
                            import asyncio
                            import psycopg2
                            from config.configmanager import ConfigManager

                            # 使用同步连接记录
                            config_manager = ConfigManager('config/config.yaml')
                            db_config = config_manager.get('Database')

                            conn = psycopg2.connect(
                                host=db_config['host'],
                                port=db_config['port'],
                                user=db_config['user'],
                                password=db_config['password'],
                                database=db_config['name']
                            )

                            with conn.cursor() as cursor:
                                cursor.execute("""
                                    INSERT INTO mcp_tool_calls (
                                        call_id, tool_name, function_name, call_parameters,
                                        call_result, result_size, status, execution_time_ms,
                                        start_time, end_time, metadata
                                    ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                                """, (
                                    str(__import__('uuid').uuid4()), tool_name, func.__name__,
                                    __import__('json').dumps(parameters), str(result), len(str(result)),
                                    'success', execution_time_ms,
                                    __import__('datetime').datetime.now(__import__('datetime').timezone.utc),
                                    __import__('datetime').datetime.now(__import__('datetime').timezone.utc),
                                    __import__('json').dumps({'start_timestamp': start_time, 'caller_info': 'mcp_server'})
                                ))
                            conn.commit()
                            conn.close()
                            logger.debug(f"Recorded MCP call: {tool_name} (success)")
                        except Exception as e:
                            logger.debug(f"Failed to record MCP call in thread: {e}")

                    # 启动后台线程记录
                    thread = threading.Thread(target=record_in_thread, daemon=True)
                    thread.start()
            except Exception as db_e:
                logger.debug(f"Failed to setup database recording: {db_e}")

            return result

        except Exception as e:
            end_time = time.time()
            execution_time_ms = int((end_time - start_time) * 1000)
            error_msg = f"Unexpected error in {func.__name__}: {str(e)}"
            logger.error(f"{error_msg}\n{traceback.format_exc()}")

            # 简化的数据库记录 - 错误情况
            try:
                import threading
                from database.db_manager import _db_manager
                if _db_manager and _db_manager._initialized:
                    # 在新线程中记录，避免事件循环冲突
                    def record_error_in_thread():
                        try:
                            import psycopg2
                            from config.configmanager import ConfigManager

                            # 使用同步连接记录
                            config_manager = ConfigManager('config/config.yaml')
                            db_config = config_manager.get('Database')

                            conn = psycopg2.connect(
                                host=db_config['host'],
                                port=db_config['port'],
                                user=db_config['user'],
                                password=db_config['password'],
                                database=db_config['name']
                            )

                            with conn.cursor() as cursor:
                                cursor.execute("""
                                    INSERT INTO mcp_tool_calls (
                                        call_id, tool_name, function_name, call_parameters,
                                        call_result, result_size, status, error_message, execution_time_ms,
                                        start_time, end_time, metadata
                                    ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                                """, (
                                    str(__import__('uuid').uuid4()), tool_name, func.__name__,
                                    __import__('json').dumps(parameters), error_msg, len(error_msg),
                                    'error', str(e), execution_time_ms,
                                    __import__('datetime').datetime.now(__import__('datetime').timezone.utc),
                                    __import__('datetime').datetime.now(__import__('datetime').timezone.utc),
                                    __import__('json').dumps({'start_timestamp': start_time, 'caller_info': 'mcp_server'})
                                ))
                            conn.commit()
                            conn.close()
                            logger.debug(f"Recorded MCP call: {tool_name} (error)")
                        except Exception as e:
                            logger.debug(f"Failed to record MCP call error in thread: {e}")

                    # 启动后台线程记录
                    thread = threading.Thread(target=record_error_in_thread, daemon=True)
                    thread.start()
            except Exception as db_e:
                logger.debug(f"Failed to setup database error recording: {db_e}")

            return error_msg
    return wrapper

def validate_input(validation_rules: Dict[str, Any]):
    """Enhanced decorator for input validation with comprehensive security checks"""
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # Get the bound arguments
            bound_args = func.__code__.co_varnames[1:func.__code__.co_argcount]
            func_name = func.__name__

            for i, arg_name in enumerate(bound_args):
                if i < len(args) - 1:  # Skip 'self' argument
                    value = args[i + 1]
                elif arg_name in kwargs:
                    value = kwargs[arg_name]
                else:
                    continue

                if arg_name in validation_rules:
                    rule = validation_rules[arg_name]

                    # 必需参数检查
                    if 'required' in rule and rule['required'] and not value:
                        examples = get_parameter_examples(func_name, arg_name)
                        example_text = f" Examples: {', '.join(examples[:3])}" if examples else ""
                        return f"Error: Required parameter '{arg_name}' is missing. Please provide a real value.{example_text}"

                    # 跳过空的可选参数
                    if not value:
                        continue

                    # 类型检查
                    if 'type' in rule and value is not None and not isinstance(value, rule['type']):
                        return f"Error: Parameter '{arg_name}' must be of type {rule['type'].__name__}"

                    # 长度检查
                    if 'max_length' in rule and isinstance(value, str) and len(value) > rule['max_length']:
                        return f"Error: Parameter '{arg_name}' exceeds maximum length of {rule['max_length']}"

                    # 增强的示例值检查
                    if isinstance(value, str) and is_forbidden_value(value):
                        examples = get_parameter_examples(func_name, arg_name)
                        if not examples:
                            # 根据参数名推断类型并获取建议
                            if arg_name in ['url', 'target'] and value.startswith(('http://', 'https://')):
                                examples = get_suggested_alternatives(ParameterType.URL)
                            elif arg_name in ['domain']:
                                examples = get_suggested_alternatives(ParameterType.DOMAIN)
                            elif arg_name in ['host', 'target']:
                                examples = get_suggested_alternatives(ParameterType.HOST)

                        example_text = f" Try: {', '.join(examples[:3])}" if examples else ""
                        return f"Error: '{value}' is a placeholder value. Please provide a real {arg_name}.{example_text}"

                    # 增强的格式验证
                    param_type = rule.get('param_type')
                    if param_type and isinstance(value, str):
                        is_valid, error_msg = validate_parameter_value(arg_name, value, param_type)
                        if not is_valid:
                            examples = get_parameter_examples(func_name, arg_name)
                            example_text = f" Examples: {', '.join(examples[:3])}" if examples else ""
                            return f"{error_msg}{example_text}"

                    # 传统的URL/IP格式检查（向后兼容）
                    if arg_name in ['target', 'url', 'host'] and isinstance(value, str):
                        if not (value.startswith(('http://', 'https://')) or
                                re.match(r'^\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}$', value) or
                                re.match(r'^[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?)*$', value)):
                            examples = get_parameter_examples(func_name, arg_name)
                            example_text = f" Examples: {', '.join(examples[:3])}" if examples else ""
                            return f"Error: '{value}' is not a valid format. Please provide a real {arg_name}.{example_text}"

                    # 正则表达式验证
                    if 'pattern' in rule and isinstance(value, str):
                        if not re.match(rule['pattern'], value):
                            description = rule.get('description', f"valid {arg_name}")
                            return f"Error: '{value}' does not match required format for {description}"

            return await func(*args, **kwargs)
        return wrapper
    return decorator

class WebSecMCP:
    """Enhanced WebSec MCP Server with async support and error handling"""

    def __init__(self):
        self.mcp = FastMCP(
            name="Websec-mcp-async"
        )
        self.timeout_handler = TimeoutHandler()
        self.config_manager = None
        self._shutdown_event = asyncio.Event()
        self._is_shutdown = False
        self.executor = ThreadPoolExecutor(max_workers=MAX_WORKERS)
        
        self.workflow_executor = None

        # 初始化响应处理器
        try:
            response_config = get_response_config()
            self.response_wrapper = MCPResponseWrapper(response_config)
            logger.info("Response handler initialized successfully")
        except Exception as e:
            logger.warning(f"Failed to initialize response handler: {e}")
            self.response_wrapper = None

        self.setup_signal_handlers()
        self.register_tools()
        self._db_initialized = False

    def setup_signal_handlers(self):
        """Setup graceful shutdown signal handlers"""
        def signal_handler(signum, frame):
            logger.info(f"Received signal {signum}, initiating graceful shutdown...")
            self._shutdown_event.set()

        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)


    def get_config_manager(self):
        """Lazy initialization of config manager"""
        if self.config_manager is None:
            try:
                self.config_manager = ConfigManager('config/config.yaml')
            except Exception as e:
                logger.error(f"Failed to initialize config manager: {e}")
                raise MCPError(f"Configuration error: {e}")
        return self.config_manager

    async def initialize_database(self):
        """Initialize database connection"""
        if self._db_initialized:
            return

        try:
            config_manager = self.get_config_manager()
            db_config = config_manager.get('Database')

            if db_config:
                await initialize_database(db_config)
                self._db_initialized = True
                logger.info("Database initialized successfully")
            else:
                logger.warning("Database configuration not found, skipping database initialization")
        except Exception as e:
            logger.error(f"Failed to initialize database: {e}")
            # 不抛出异常，允许服务器在没有数据库的情况下运行



    def register_tools(self):
        """Register all MCP tools"""
        self._register_scan_tools()
        self._register_web_tools()
        self._register_network_tools()
        self._register_network_tools_continued()
        self._register_subdomain_tools()
        self._register_ip_tools()

    def _register_scan_tools(self):
        """Register scanning tools (nuclei, tlsx, fingerprintx)"""
        @self.mcp.tool()
        @error_handler
        @validate_input({
            'target': {
                'required': True,
                'type': str,
                'max_length': 500,
                'param_type': ParameterType.TARGET,
                'description': 'Target URL or IP address. Examples: "http://scanme.nmap.org", "************", "scanme.nmap.org"'
            },
            'service': {
                'required': False,
                'type': str,
                'max_length': 100,
                'description': 'Target service type for intelligent workflow selection. Examples: "apache", "mysql", "ssh", "wordpress", "jenkins"'
            }
        })
        async def nuclei_scan_wrapper(
            target: str,
            service: Optional[str] = None,
            timeout: Optional[int] = DEFAULT_TIMEOUT
        ) -> str:
            """🔍 Nuclei 安全扫描器 - 智能工作流选择

            基于目标服务类型进行智能工作流选择的综合安全扫描工具

            参数说明:
                target: 目标URL或IP地址，必须是真实可访问的地址
                       格式: "http://example.com" 或 "***********"
                service: 目标服务类型，可选参数，用于智能工作流选择，默认是使用内置参数进行扫描
                        选项: "apache", "mysql", "ssh", "wordpress", "jenkins"

            返回结果:
                详细的漏洞扫描结果，包含发现的安全问题
            """

            handler = TimeoutHandler(timeout or DEFAULT_TIMEOUT)
            raw_result = await handler.run_with_timeout(run_nuclei, target, service)

            return raw_result

        @self.mcp.tool()
        @error_handler
        @validate_input({
            'host': {
                'required': True,
                'type': str,
                'max_length': 255,
                'param_type': ParameterType.HOST,
                'description': 'Target hostname or IP address for TLS analysis. Examples: "scanme.nmap.org", "************", "httpbin.org"'
            },
            'port': {
                'type': int,
                'description': 'Target port number for TLS service (default: 443). Examples: 443, 8443, 9443'
            }
        })
        async def tlsx_wrapper(
            host: str,
            port: Optional[int] = 443,
            timeout: Optional[int] = DEFAULT_TIMEOUT
        ) -> str:
            """🔒 TLS/SSL 配置分析器 - 高级证书检测

            全面的TLS/SSL安全分析和证书检查工具

            参数说明:
                host: 目标主机名或IP地址，必须是真实可访问的地址
                     格式: "scanme.nmap.org" 或 "************"
                port: 目标端口号，可选参数，默认是443端口
                     格式: 443, 8443, 9443

            返回结果:
                JSON格式的全面TLS/SSL分析结果，包含证书链、加密套件、TLS版本等信息
            """
            if port and (port < 1 or port > 65535):
                return("Port must be between 1 and 65535")

            handler = TimeoutHandler(timeout or DEFAULT_TIMEOUT)
            raw_result = await handler.run_with_timeout(run_tlsx, host, port)

            return raw_result

        @self.mcp.tool()
        @error_handler
        @validate_input({
            'ip': {
                'required': True,
                'type': str,
                'max_length': 255,
                'param_type': ParameterType.IP,
                'description': 'Target IP address for service fingerprinting. Examples: "************", "*******", "*******"'
            },
            'port': {
                'required': True,
                'type': str,
                'max_length': 10,
                'description': 'Target port number as string. Examples: "80", "443", "22", "3389"'
            }
        })
        async def fingerprintx_wrapper(
            ip: str,
            port: str,
            timeout: Optional[int] = DEFAULT_TIMEOUT
        ) -> str:
            """🔍 服务指纹识别工具 - 高级版本检测

            精确的网络服务识别和版本检测工具

            参数说明:
                ip: 目标IP地址，必须是有效的IPv4地址
                   格式: "************", "*******", "*******"
                port: 目标端口号，字符串格式
                     格式: "80", "443", "22", "3389"

            返回结果:
                详细的服务指纹和版本信息，包含服务类型、版本号、协议分析等
            """
            try:
                port_int = int(port)
                if port_int < 1 or port_int > 65535:
                    return("Port must be between 1 and 65535")
            except ValueError:
                return("Port must be a valid integer")

            handler = TimeoutHandler(timeout or DEFAULT_TIMEOUT)
            raw_result = await handler.run_with_timeout(run_fingerprintx, ip, port)

            return raw_result

    def _register_web_tools(self):
        """Register web testing tools (spray, httpx, katana)"""

        @self.mcp.tool()
        @error_handler
        @validate_input({
            'url': {
                'required': True,
                'type': str,
                'max_length': 500,
                'param_type': ParameterType.URL,
                'description': 'Target URL with protocol. Example: "http://scanme.nmap.org" or "https://httpbin.org"'
            }
        })
        async def spray_wrapper(
            url: str,
            timeout: Optional[int] = DEFAULT_TIMEOUT
        ) -> str:
            """🌐 Web目录爆破工具 - 高级路径枚举

            先进的目录和文件枚举工具

            参数说明:
                url: 目标URL，必须包含完整的协议前缀
                    格式: "http://example.com" 或 "https://target.com:8080"

            返回结果:
                目录枚举结果，包含发现的路径和文件
            """

            handler = TimeoutHandler(timeout or DEFAULT_TIMEOUT)
            raw_result = await handler.run_with_timeout(run_spray, url)

            return raw_result

        @self.mcp.tool()
        @error_handler
        @validate_input({
            'url': {
                'required': True,
                'type': str,
                'max_length': 500,
                'param_type': ParameterType.URL,
                'description': 'Target URL for HTTP probing. Examples: "http://scanme.nmap.org", "https://httpbin.org"'
            },
            'method': {
                'required': True,
                'type': str,
                'max_length': 10,
                'description': 'HTTP method to use. Examples: "GET", "POST", "PUT", "DELETE", "HEAD", "OPTIONS"'
            },
            'headers': {
                'type': dict,
                'max_length': 500,
                'description': 'HTTP headers as dictionary. Example: {"User-Agent": "Mozilla/5.0", "Accept": "application/json"}'
            },
            'data': {
                'type': str,
                'max_length': 1000,
                'description': 'HTTP request body data. Examples: "param1=value1&param2=value2", \'{"key": "value"}\''
            }
        })
        async def httpx_wrapper(
            url: str,
            method: str = "GET",
            headers: Optional[dict] = None,
            data: Optional[str] = None,
            timeout: Optional[int] = DEFAULT_TIMEOUT
        ) -> str:
            """🌐 HTTP探测分析工具 - 多功能请求工具

            快速多用途HTTP工具包，用于探测Web服务器和应用程序

            参数说明:
                url: 目标URL，必须包含完整的协议前缀
                    格式: "http://scanme.nmap.org" 或 "https://httpbin.org"
                method: HTTP请求方法，默认GET
                       选项: "GET", "POST", "PUT", "DELETE", "HEAD", "OPTIONS", "PATCH"
                headers: HTTP请求头，可选参数，字典格式
                        格式: {"User-Agent": "Mozilla/5.0", "Accept": "application/json"}
                data: HTTP请求体数据，可选参数，主要用于POST/PUT请求
                     格式: "param1=value1&param2=value2" 或 '{"key": "value"}'

            返回结果:
                全面的HTTP探测结果，包含响应分析、状态码、技术指纹等信息
            """
            valid_methods = ['GET', 'POST', 'PUT', 'DELETE', 'HEAD', 'OPTIONS', 'PATCH']
            if method and method.upper() not in valid_methods:
                return(f"Invalid HTTP method. Must be one of: {', '.join(valid_methods)}")

            handler = TimeoutHandler(timeout or DEFAULT_TIMEOUT)
            raw_result = await handler.run_with_timeout(run_httpx, url, method, headers, data)

            return raw_result

        @self.mcp.tool()
        @error_handler
        @validate_input({
            'target': {
                'required': True,
                'type': str,
                'max_length': 500,
                'param_type': ParameterType.URL,
                'description': 'Target URL to crawl. Example: "http://scanme.nmap.org" or "https://httpbin.org"'
            }
        })
        async def katana_wrapper(
            target: str,
            timeout: Optional[int] = DEFAULT_TIMEOUT
        ) -> str:
            """🕷️ 高级Web爬虫 - 下一代爬取框架

            新一代爬虫框架

            参数说明:
                target: 目标URL，必须包含完整的协议前缀
                       格式: "http://example.com" 或 "https://app.company.com/dashboard"

            返回结果:
                全面的爬取结果，包含发现的端点和URL
            """
            handler = TimeoutHandler(timeout or DEFAULT_TIMEOUT)
            raw_result = await handler.run_with_timeout(run_katana, target)

            return raw_result

        @self.mcp.tool()
        @error_handler
        @validate_input({
            'target': {
                'type': str,
                'max_length': 500,
                'param_type': ParameterType.URL,
                'description': 'Single target URL to scan. Example: "http://scanme.nmap.org" or "https://httpbin.org"'
            }
        })
        async def webleak_wrapper(
            target: Optional[str] = None,
            timeout: Optional[int] = DEFAULT_TIMEOUT
        ) -> str:
            """🔓 Web应用漏洞扫描器 - 弱密码检测

            扫描常见Web服务的弱密码和未授权访问漏洞

            参数说明:
                target: 单个目标URL，必须包含协议前缀
                       格式: "http://scanme.nmap.org" 或 "https://httpbin.org"

            返回结果:
                Web漏洞扫描结果，包含检测到的安全问题
            """
            if not target:
                return "Error: Either target URL must be specified"

            handler = TimeoutHandler(timeout or DEFAULT_TIMEOUT)
            raw_result = await handler.run_with_timeout(
                run_webleak,
                target
            )

            return raw_result

        @self.mcp.tool()
        @error_handler
        @validate_input({
            'target': {
                'required': True,
                'type': str,
                'max_length': 500,
                'param_type': ParameterType.URL,
                'description': 'Target URL with protocol for JS API analysis. Examples: "https://example.com", "http://target.com:8080"'
            }
        })
        async def js_api_analyzer_wrapper(
            target: str,
            timeout: Optional[int] = DEFAULT_TIMEOUT
        ) -> str:
            """📊 JavaScript API分析器 - 有效API提取

            分析JavaScript文件以发现和测试API端点，仅返回有效的API

            参数说明:
                target: 目标URL，必须包含完整的协议前缀
                       格式: "https://example.com"

            返回结果:
                JSON格式的有效API端点列表，包含状态码和响应信息
            """
            if not target.startswith(('http://', 'https://')):
                return json.dumps({
                    'error': 'Target URL must include protocol (http:// or https://)',
                    'example': 'https://example.com'
                }, ensure_ascii=False, indent=2)

            # 直接调用异步函数，因为get_valid_apis_only是异步的
            raw_result = await get_valid_apis_only(
                target=target,
                timeout=timeout or 10,
                max_depth=2,
                auth_headers=None,
                max_workers=10,
                include_404=False,
                verbose=False
            )

            return raw_result

        @self.mcp.tool()
        @error_handler
        @validate_input({
            'url': {
                'required': True,
                'type': str,
                'max_length': 500,
                'param_type': ParameterType.URL,
                'description': 'Target URL with protocol for technology stack analysis. Examples: "https://example.com", "http://target.com:8080"'
            }
        })
        async def wappalyzer_wrapper(
            url: str,
            timeout: Optional[int] = DEFAULT_TIMEOUT
        ) -> str:
            """🔍 Wappalyzer 技术栈识别工具 - Web技术分析

            使用 Wappalyzer 工具分析目标网站的技术栈、框架、库和服务

            参数说明:
                url: 目标URL，必须包含完整的协议前缀
                    格式: "https://example.com" 或 "http://target.com:8080"

            返回结果:
                JSON格式的技术栈识别结果，包含检测到的技术、版本和置信度
            """
            if not url.startswith(('http://', 'https://')):
                return json.dumps({
                    'error': 'Target URL must include protocol (http:// or https://)',
                    'example': 'https://example.com'
                }, ensure_ascii=False, indent=2)

            handler = TimeoutHandler(timeout or DEFAULT_TIMEOUT)
            raw_result = await handler.run_with_timeout(run_wappalyzer, url)

            return raw_result

    def _register_network_tools(self):
        """Register network scanning tools (rustscan, netexec)"""
        @self.mcp.tool()
        @error_handler
        @validate_input({
            'target': {
                'required': True,
                'type': str,
                'max_length': 255,
                'param_type': ParameterType.HOST,
                'description': 'Target IP address or hostname. Examples: "************", "scanme.nmap.org"'
            },
            'ports': {
                'required': True,
                'type': str,
                'max_length': 100,
                'description': 'Port specification. Examples: "80,443", "1-1000", "22,80,443,8080"'
            }
        })
        async def rustscan_wrapper(
            target: str,
            ports: Optional[str] = '1-65535',
            timeout: Optional[int] = DEFAULT_TIMEOUT
        ) -> str:
            """⚡ 超快端口扫描器 - Rust构建

            现代化的超高速端口扫描器

            参数说明:
                target: 目标IP地址或可解析的主机名
                       格式: "***********" 或 "scanme.nmap.org"
                ports: 端口规范，默认扫描全端口
                      格式: "80,443", "1-1000", "22,80,443,8080", "top"

            返回结果:
                快速端口扫描结果，包含发现的开放端口
            """
            handler = TimeoutHandler(timeout or DEFAULT_TIMEOUT)
            raw_result = await handler.run_with_timeout(run_rustscan, target, ports)

            return raw_result

    def _register_network_tools_continued(self):
        """Register additional network tools (netexec, crack)"""
        @self.mcp.tool()
        @error_handler
        @validate_input({
            'protocol': {
                'required': True,
                'type': str,
                'max_length': 20,
                'param_type': ParameterType.PROTOCOL,
                'description': 'Protocol for authentication. Supported: ssh, smb, winrm, wmi, mssql'
            },
            'target': {
                'required': True,
                'type': str,
                'max_length': 500,
                'param_type': ParameterType.HOST,
                'description': 'Target IP address or hostname. Examples: "*************", "scanme.nmap.org"'
            },
            'port': {
                'required': True,
                'type': str,
                'max_length': 20,
                'description': 'Target port number as string. Examples: "22" (SSH), "445" (SMB), "5985" (WinRM), "1433" (MSSQL)'
            },
            'user': {
                'required': True,
                'type': str,
                'max_length': 20,
                'description': 'Username for authentication. Examples: "admin", "administrator", "root", "sa"'
            },
            'password': {
                'required': True,
                'type': str,
                'max_length': 50,
                'description': 'Password for authentication. Must be real password, not placeholder'
            },
            'command': {
                'required': True,
                'type': str,
                'max_length': 500,
                'description': 'Command to execute. Examples: "whoami", "id", "SELECT @@version" (for MSSQL)'
            }
        })
        async def netexec_wrapper(
            protocol: str,
            target: str,
            port: str,
            user: str,
            password: str,
            command: str,
            timeout: Optional[int] = DEFAULT_TIMEOUT
        ) -> str:
            """🔧 网络服务利用工具 - 远程命令执行

            高级网络服务渗透测试和远程命令执行工具

            参数说明:
                protocol: 认证协议，必须是支持的类型
                         选项: "ssh", "smb", "winrm", "wmi", "mssql"
                target: 目标主机，必须是真实可访问的IP或主机名
                       格式: "*************" 或 "scanme.nmap.org"
                port: 目标端口，必须匹配协议要求
                     格式: "22" (SSH), "445" (SMB), "5985" (WinRM), "1433" (MSSQL)
                user: 用户名，必须是真实凭据
                     格式: "admin", "administrator", "root", "sa"
                password: 密码，必须是真实凭据
                command: 要执行的命令，将在目标系统上运行
                        格式: "whoami", "id", "SELECT @@version" (MSSQL)

            返回结果:
                目标系统的命令执行结果
            """
            handler = TimeoutHandler(timeout or DEFAULT_TIMEOUT)
            raw_result = await handler.run_with_timeout(run_netexec, protocol, target, port, user, password, command)

            return raw_result

        @self.mcp.tool()
        @error_handler
        @validate_input({
            'protocol': {
                'required': True,
                'type': str,
                'max_length': 20,
                'param_type': ParameterType.PROTOCOL,
                'description': 'Protocol to attack. Supported: ssh, ftp, smb, mysql, mssql, redis, mongodb, snmp, imap, pop3, smtp, http, https, rdp, vnc'
            },
            'targets': {
                'required': True,
                'type': str,
                'max_length': 500,
                'description': 'Target services in format "IP:PORT". Examples: "*************:22", "*************:22,*************:3389"'
            },
            'usernames': {
                'type': str,
                'max_length': 500,
                'description': 'Usernames for brute force. Examples: "admin,root,user", "/path/to/userlist.txt", or leave empty for default dictionary'
            },
            'passwords': {
                'type': str,
                'max_length': 500,
                'description': 'Passwords for brute force. Examples: "password123,admin,123456", "/path/to/passlist.txt", or leave empty for default dictionary'
            }
        })
        async def crack_wrapper(
            protocol: str,
            targets: str,
            usernames: Optional[str] = None,
            passwords: Optional[str] = None,
            timeout: Optional[int] = DEFAULT_TIMEOUT
        ) -> str:
            """🔓 网络服务暴力破解 - 凭据攻击工具

            高级暴力破解工具，用于网络服务认证测试

            参数说明:
                protocol: 目标协议，必须是支持的协议类型
                         选项: "ssh", "ftp", "smb", "mysql", "mssql", "redis", "mongodb", "rdp", "vnc"
                targets: 目标服务，IP:PORT格式
                        格式: "*************:22" 或 "*************:22,*************:3389"
                usernames: 用户名列表，可选参数，默认是使用内置字典
                          格式: "admin,root,user" 或 "/path/to/userlist.txt" 或留空使用默认字典
                passwords: 密码列表，可选参数，默认是使用内置字典
                          格式: "password123,admin,123456" 或 "/path/to/passlist.txt" 或留空使用默认字典

            返回结果:
                暴力破解攻击结果，包含发现的有效凭据
            """
            if protocol not in ["ssh", "http_proxy", "ftp", "telnet", "mysql", "postgresql", "oracle", "mssql", "redis", "mongodb", "smb", "rdp", "vnc", "snmp", "imap", "pop3", "smtp", "http", "https"]:
                return(f"Unsupported protocol: {protocol}")

            handler = TimeoutHandler(timeout or DEFAULT_TIMEOUT)
            raw_result = await handler.run_with_timeout(run_crack, protocol, targets, usernames, passwords)

            return raw_result

    def _register_subdomain_tools(self):
        """Register subdomain enumeration tools"""
        @self.mcp.tool()
        @error_handler
        @validate_input({
            'domain': {
                'required': True,
                'type': str,
                'max_length': 255,
                'param_type': ParameterType.DOMAIN,
                'description': 'Target domain for comprehensive subdomain discovery. Examples: "hackerone.com", "bugcrowd.com"'
            },
            'timeout': {
                'type': int,
                'description': 'DNS resolution timeout in seconds (default: 3)'
            }
        })
        async def subdomain_wrapper(
            domain: str,
            timeout: Optional[int] = DEFAULT_TIMEOUT
        ) -> str:
            """🔍 综合子域名发现工具 - 多源集成

            执行所有可用的子域名发现工具，集成结果并进行DNS验证

            参数说明:
                domain: 目标域名，必须是真实有效的域名
                       格式: "hackerone.com", "bugcrowd.com"

            返回结果:
                JSON格式的综合子域名发现结果，包含DNS验证和通配符检测
            """
            try:
                # 获取API密钥
                config_manager = self.get_config_manager()
                vt_api_key = config_manager.get('Subdomain', 'virustotal_api_key')
                st_api_key = config_manager.get('Subdomain', 'securitytrails_api_key')

                # 执行综合子域名发现
                handler = TimeoutHandler(timeout or DEFAULT_TIMEOUT)
                raw_result = await handler.run_with_timeout(
                    comprehensive_subdomain_discovery,
                    domain,
                    vt_api_key,
                    st_api_key,
                    float(3)
                )

                return raw_result

            except Exception as e:
                logger.error(f"Comprehensive subdomain discovery failed: {e}")
                return json.dumps({
                    "error": f"Comprehensive subdomain discovery failed: {str(e)}",
                    "domain": domain
                }, ensure_ascii=False)

    def _register_ip_tools(self):
        """Register IP analysis tools"""
        @self.mcp.tool()
        @error_handler
        @validate_input({
            'ip': {
                'required': True,
                'type': str,
                'max_length': 45,  # IPv6 addresses can be up to 45 characters
                'param_type': ParameterType.IP,
                'description': 'IP address to query location information. Examples: "*******", "*******", "***************"'
            }
        })
        async def iplocation_wrapper(
            ip: str,
            timeout: Optional[int] = DEFAULT_TIMEOUT
        ) -> str:
            """🌍 IP地址地理位置查询 - 综合信息查找

            查询IP地址的综合信息，包括地理位置、ISP和网络详情

            参数说明:
                ip: 目标IP地址，必须是有效的IPv4或IPv6地址
                   格式: "*******", "*******", "***************", "2001:4860:4860::8888"

            返回结果:
                JSON格式的IP地理位置和网络信息，包含国家、地区、城市、ISP、时区等
            """
            handler = TimeoutHandler(timeout or DEFAULT_TIMEOUT)
            raw_result = await handler.run_with_timeout(iplocation, ip)

            return raw_result

        @self.mcp.tool()
        @error_handler
        @validate_input({
            'ip': {
                'required': True,
                'type': str,
                'max_length': 45,  # IPv6 addresses can be up to 45 characters
                'param_type': ParameterType.IP,
                'description': 'IP address to query historical domain resolutions. Examples: "*******", "*******", "***************"'
            }
        })
        async def ipreverse_wrapper(
            ip: str,
            timeout: Optional[int] = DEFAULT_TIMEOUT
        ) -> str:
            """🔍 IP历史域名解析查询 - VirusTotal情报

            查询解析到指定IP地址的历史域名记录

            参数说明:
                ip: 目标IP地址，必须是有效的IPv4或IPv6地址
                   格式: "*******", "*******", "***************", "2001:4860:4860::8888"

            返回结果:
                JSON格式的历史域名解析记录，包含域名列表、解析时间戳和统计信息
            """
            try:
                config_manager = self.get_config_manager()
                vt_api_key = config_manager.get('Subdomain', 'virustotal_api_key')

                if not vt_api_key:
                    return json.dumps({
                        "error": "VirusTotal API key not configured",
                        "message": "Please configure VirusTotal API key in config file"
                    }, ensure_ascii=False)

                handler = TimeoutHandler(timeout or DEFAULT_TIMEOUT)
                raw_result = await handler.run_with_timeout(ipreverse, ip, vt_api_key)

                return raw_result

            except Exception as e:
                logger.error(f"IP reverse lookup failed: {e}")
                return json.dumps({
                    "error": f"IP reverse lookup failed: {str(e)}"
                }, ensure_ascii=False)

        @self.mcp.tool()
        @error_handler
        @validate_input({
            'ip': {
                'required': True,
                'type': str,
                'max_length': 45,  # IPv6 addresses can be up to 45 characters
                'param_type': ParameterType.IP,
                'description': 'IP address to query ASN and organization information. Examples: "*******", "*******", "***************"'
            }
        })
        async def ipasn_wrapper(
            ip: str,
            timeout: Optional[int] = DEFAULT_TIMEOUT
        ) -> str:
            """🏢 IP地址ASN组织信息查询 - IPinfo.io

            查询IP地址的ASN（自治系统号）和组织详细信息

            参数说明:
                ip: 目标IP地址，必须是有效的IPv4或IPv6地址
                   格式: "*******", "*******", "***************", "2001:4860:4860::8888"

            返回结果:
                JSON格式的ASN和组织信息，包含自治系统号、组织名称、ISP信息、网络范围等
            """
            try:
                config_manager = self.get_config_manager()
                # 从配置中获取IPinfo API密钥
                ipinfo_api_key = config_manager.get('Ipinformation', 'ipinfo')

                if not ipinfo_api_key:
                    return json.dumps({
                        "error": "IPinfo.io API key not configured",
                        "message": "Please configure IPinfo.io API key in config file under 'Ipinformation.ipinfo'"
                    }, ensure_ascii=False)

                handler = TimeoutHandler(timeout or DEFAULT_TIMEOUT)
                raw_result = await handler.run_with_timeout(ipasn, ip, ipinfo_api_key)

                return raw_result

            except Exception as e:
                logger.error(f"IP ASN lookup failed: {e}")
                return json.dumps({
                    "error": f"IP ASN lookup failed: {str(e)}"
                }, ensure_ascii=False)
    def run_server(self):
        """运行 MCP server 并监听关闭事件"""
        try:
            logger.info("Starting WebSec MCP Server...")
            
            # 验证服务器配置
            self._validate_server_config()

            # 同步初始化数据库（在启动前）
            try:
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                loop.run_until_complete(self.initialize_database())
                loop.close()
                logger.info("Database initialization completed")
            except Exception as db_e:
                logger.warning(f"Database initialization failed: {db_e}")
                logger.info("Server will continue without database functionality")

            # 运行 MCP 服务器（FastMCP 会处理自己的事件循环）
            logger.info("Starting MCP server on http://0.0.0.0:8080/mcp/")
            
            self.mcp.run(transport="streamable-http", host="0.0.0.0", port=8080)

        except Exception as e:
            logger.error(f"Server error: {e}")
            import traceback
            traceback.print_exc()
            raise
    
    def _validate_server_config(self):
        """验证服务器配置"""
        try:
            # 检查配置管理器
            config_manager = self.get_config_manager()
            logger.info("配置管理器验证成功")
            
            # 检查响应处理器
            if not self.response_wrapper:
                logger.warning("响应处理器未初始化，将使用默认响应格式")
            
            logger.info("服务器配置验证完成")
            
        except Exception as e:
            logger.error(f"服务器配置验证失败: {e}")
            raise

def main():
    """Main entry point"""
    server = WebSecMCP()

    try:
        server.run_server()
    except KeyboardInterrupt:
        logger.info("Server stopped by user")
    except Exception as e:
        logger.error(f"Server failed to start: {e}")
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    #print(run_wappalyzer("http://101.35.247.212:5678/"))
    main()
