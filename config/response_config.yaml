# MCP响应处理配置文件
# 用于控制响应数据的大小检测和截断行为
#
# 当前策略：简化的二元策略
# - 数据 < 100KB：直接返回，保持完整数据
# - 数据 ≥ 100KB：截断到100KB，防止上下文溢出

response_handling:
  # 数据大小阈值配置 (单位: 字节)
  threshold: 100000            # 100KB - 小于此值直接返回，大于等于此值截断到此大小
  preserve_structure: true     # 保持JSON结构完整性

  # 性能配置
  max_processing_time_ms: 30000  # 30秒最大处理时间

  # 调试配置
  debug_mode: false             # 调试模式
  log_processing_stats: true    # 记录处理统计信息

# 环境变量配置说明:
# 可以通过以下环境变量覆盖配置文件设置:
#
# MCP_THRESHOLD=100000
# MCP_PRESERVE_STRUCTURE=true
# MCP_MAX_PROCESSING_TIME_MS=30000
# MCP_DEBUG_MODE=false
# MCP_LOG_PROCESSING_STATS=true

# 使用建议:
# 1. 对于开发环境，可以设置较小的阈值以便测试
# 2. 对于生产环境，根据实际网络条件和性能要求调整
# 3. 截断大小控制最终输出的数据量，建议保持在100KB以内
# 4. 调试模式会输出详细的处理信息，生产环境建议关闭
