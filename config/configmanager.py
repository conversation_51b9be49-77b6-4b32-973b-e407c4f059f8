# Encdoding: utf-8
import os
import configparser
import json
import yaml

class ConfigManager:
    def __init__(self, config_path):
        self.config_path = config_path
        self.config = None
        self._load_config()

    def _load_config(self):
        ext = os.path.splitext(self.config_path)[1]
        if ext == '.ini':
            self.config = configparser.ConfigParser()
            self.config.read(self.config_path, encoding='utf-8')
        elif ext == '.json':
            with open(self.config_path, 'r', encoding='utf-8') as file:
                self.config = json.load(file)
        elif ext == '.yaml' or ext == '.yml':
            with open(self.config_path, 'r', encoding='utf-8') as file:
                self.config = yaml.safe_load(file)
        else:
            raise ValueError("Unsupported config file format")

    def get(self, section=None, option=None):
        if section is None:
            raise ValueError("Section parameter is required")

        if isinstance(self.config, configparser.ConfigParser):
            if option is None:
                raise ValueError("Option parameter is required for INI config")
            return self.config[section][option]
        else:
            if self.config is None:
                raise ValueError("Config not loaded properly")
            if option is None:
                return self.config.get(section)
            return self.config.get(section, {}).get(option)

    def set(self, section, option, value):
        if self.config is None:
            raise ValueError("Config not loaded properly")

        if isinstance(self.config, configparser.ConfigParser):
            if not self.config.has_section(section):
                self.config.add_section(section)
            self.config.set(section, option, value)
        else:
            if section not in self.config:
                self.config[section] = {}
            self.config[section][option] = value

    def save(self):
        if isinstance(self.config, configparser.ConfigParser):
            with open(self.config_path, 'w', encoding='utf-8') as file:
                self.config.write(file)
        else:
            ext = os.path.splitext(self.config_path)[1]
            with open(self.config_path, 'w', encoding='utf-8') as file:
                if ext == '.json':
                    json.dump(self.config, file, indent=4)
                elif ext in ('.yaml', '.yml'):
                    yaml.dump(self.config, file, default_flow_style=False)

# 使用示例
# #config_manager = ConfigManager('config.ini')
# db_host = config_manager.get('Database', 'host')
# print(f"Database Host: {db_host}")
