# 渗透测试工作流配置
# 定义固定的测试流程，减少AI选择的随机性

workflows:
  # 标准Web应用渗透测试流程
  web_pentest:
    name: "Web应用渗透测试"
    description: "标准的Web应用安全测试流程"
    steps:
      - step: 1
        tool: "httpx_wrapper"
        description: "HTTP服务探测"
        required_params:
          - url
        default_params:
          method: "GET"
        next_step_condition: "success"
        
      - step: 2
        tool: "tlsx_wrapper"
        description: "TLS/SSL配置分析"
        required_params:
          - host
        default_params:
          port: 443
        condition: "if_https"
        
      - step: 3
        tool: "spray_wrapper"
        description: "目录枚举"
        required_params:
          - url
        depends_on: [1]
        
      - step: 4
        tool: "katana_wrapper"
        description: "爬虫分析"
        required_params:
          - url
        depends_on: [1]
        
      - step: 5
        tool: "webleak_wrapper"
        description: "Web应用漏洞扫描"
        required_params:
          - target
        depends_on: [1, 3, 4]
        
      - step: 6
        tool: "nuclei_scan_wrapper"
        description: "综合漏洞扫描"
        required_params:
          - target
        default_params:
          service: "web"
        depends_on: [1, 3, 4, 5]

  # 网络服务渗透测试流程
  network_pentest:
    name: "网络服务渗透测试"
    description: "针对网络服务的渗透测试流程，发现Web服务时自动进行深度测试"
    steps:
      - step: 1
        tool: "rustscan_wrapper"
        description: "端口扫描"
        required_params:
          - target
        default_params:
          ports: "1-65535"
          
      - step: 2
        tool: "fingerprintx_wrapper"
        description: "服务指纹识别"
        required_params:
          - ip
          - port
        depends_on: [1]
        iterate_ports: true
        
      - step: 3
        tool: "nuclei_scan_wrapper"
        description: "服务漏洞扫描"
        required_params:
          - target
        depends_on: [1, 2]
        service_based: true
        
      - step: 4
        tool: "httpx_web_verification"
        description: "Web服务验证"
        required_params:
          - target
        depends_on: [1, 2]
        condition: "potential_web_ports_found"
        default_params: {}
        
      - step: 5
        tool: "cascade_web_test"
        description: "级联Web服务测试"
        required_params:
          - target
        depends_on: [4]
        condition: "verified_web_services_found"
        cascade_workflow: "web_pentest"
        default_params: {}

  # 子域名发现流程
  subdomain_discovery:
    name: "子域名发现与网络渗透测试"
    description: "全面的子域名发现流程，解析IP后进行深度网络渗透测试"
    steps:
      - step: 1
        tool: "comprehensive_subdomain_discovery_wrapper"
        description: "子域名发现"
        required_params:
          - domain
          
      - step: 2
        tool: "dns_resolution"
        description: "子域名DNS解析"
        required_params:
          - domain
        depends_on: [1]
        iterate_subdomains: true
        
      - step: 3
        tool: "httpx_wrapper"
        description: "Web服务存活检测"
        required_params:
          - url
        depends_on: [1]
        iterate_subdomains: true
        
      - step: 4
        tool: "cascade_network_test"
        description: "级联网络渗透测试"
        required_params:
          - target
        depends_on: [2]
        condition: "resolved_ips_found"
        cascade_workflow: "network_pentest"
        default_params: {}
        
      - step: 5
        tool: "cascade_web_test"
        description: "级联Web服务深度测试"
        required_params:
          - target
        depends_on: [3]
        condition: "alive_web_services_found"
        cascade_workflow: "web_pentest"
        default_params: {}

# 参数模板配置
parameter_templates:
  common_headers:
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
    "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8"
    "Accept-Language": "en-US,en;q=0.5"
    "Accept-Encoding": "gzip, deflate"
    "Connection": "keep-alive"
    
  scan_timeouts:
    quick: 60
    normal: 300
    deep: 600
    
  port_ranges:
    common: "80,443,8080,8443,3000,5000,8000"
    extended: "1-1000"
    full: "1-65535"

# 目标类型检测规则
target_detection:
  url_patterns:
    - pattern: "^https?://"
      type: "web_application"
      workflow: "web_pentest"
      
  ip_patterns:
    - pattern: "^\\d{1,3}\\.\\d{1,3}\\.\\d{1,3}\\.\\d{1,3}$"
      type: "ip_address"
      workflow: "network_pentest"
      
  domain_patterns:
    - pattern: "^[a-zA-Z0-9][a-zA-Z0-9-]{1,61}[a-zA-Z0-9]\\.[a-zA-Z]{2,}$"
      type: "domain"
      workflow: "subdomain_discovery"