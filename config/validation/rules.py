# Encoding: utf-8
"""
参数验证规则配置文件 - 重构版本
定义MCP工具调用的参数验证规则，防止使用示例值和无效参数
降低函数复杂度，提高可维护性
"""

import re
from typing import Dict, List, Set, Any, Optional, Tuple, Callable

# 禁用的示例值列表
FORBIDDEN_VALUES: Set[str] = {
    # 域名类示例值
    'example.com', 'test.com', 'target.com', 'demo.com', 'sample.com',
    'placeholder.com', 'your-domain.com', 'your-target.com', 'company.com',
    'website.com', 'site.com', 'domain.com', 'host.com', 'server.com',

    # IP类示例值
    'localhost', '127.0.0.1', '0.0.0.0', 'your-ip', 'target-ip',

    # 通用占位符
    'example', 'test', 'target', 'sample', 'demo', 'placeholder',
    'your-target', 'your-host', 'your-server', 'your-site',

    # URL类示例值
    'http://example.com', 'https://example.com', 'http://test.com',
    'https://test.com', 'http://target.com', 'https://target.com',
    'http://localhost', 'https://localhost',

    # 端口类示例值
    'your-port', 'target-port', 'port',

    # 用户名密码类示例值
    'admin', 'root', 'user', 'test', 'demo', 'guest', 'your-username',
    'your-password', 'password', '123456', 'admin123',
}

# 正则表达式模式
VALIDATION_PATTERNS: Dict[str, str] = {
    # URL格式验证 - 必须包含协议
    'url': r'^https?://[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?)*(:[0-9]+)?(/.*)?$',

    # 域名格式验证
    'domain': r'^[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?)*$',

    # IP地址格式验证
    'ip': r'^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$',

    # 端口格式验证
    'port': r'^(?:[1-9][0-9]{0,3}|[1-5][0-9]{4}|6[0-4][0-9]{3}|65[0-4][0-9]{2}|655[0-2][0-9]|6553[0-5])$',

    # 端口范围验证 (如: 80,443 或 1-1000)
    'port_range': r'^(?:\d+(?:-\d+)?(?:,\d+(?:-\d+)?)*)$',

    # 协议验证
    'protocol': r'^(?:http|https|ssh|ftp|sftp|smb|winrm|wmi|mssql|mysql|redis|mongodb)$',

    # IP:PORT格式验证 (如: ***********:22 或 ***********:22,***********:80)
    'ip_port': r'^(?:(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?):(?:[1-9][0-9]{0,3}|[1-5][0-9]{4}|6[0-4][0-9]{3}|65[0-4][0-9]{2}|655[0-2][0-9]|6553[0-5])(?:,(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?):(?:[1-9][0-9]{0,3}|[1-5][0-9]{4}|6[0-4][0-9]{3}|65[0-4][0-9]{2}|655[0-2][0-9]|6553[0-5]))*)$',
}

# 参数类型定义
class ParameterType:
    URL = 'url'
    DOMAIN = 'domain'
    IP = 'ip'
    HOST = 'host'  # 可以是域名或IP
    PORT = 'port'
    PORT_RANGE = 'port_range'
    PROTOCOL = 'protocol'
    TARGET = 'target'  # 可以是URL、域名或IP
    IP_PORT = 'ip_port'  # IP:PORT格式

# 验证器函数映射
def _validate_url(value: str) -> bool:
    """验证URL格式"""
    return bool(re.match(VALIDATION_PATTERNS['url'], value))

def _validate_domain(value: str) -> bool:
    """验证域名格式"""
    return bool(re.match(VALIDATION_PATTERNS['domain'], value))

def _validate_ip(value: str) -> bool:
    """验证IP地址格式"""
    return bool(re.match(VALIDATION_PATTERNS['ip'], value))

def _validate_host(value: str) -> bool:
    """验证主机格式（域名或IP）"""
    return _validate_domain(value) or _validate_ip(value)

def _validate_port(value: str) -> bool:
    """验证端口格式"""
    return bool(re.match(VALIDATION_PATTERNS['port'], value))

def _validate_port_range(value: str) -> bool:
    """验证端口范围格式"""
    return bool(re.match(VALIDATION_PATTERNS['port_range'], value))

def _validate_protocol(value: str) -> bool:
    """验证协议格式"""
    return bool(re.match(VALIDATION_PATTERNS['protocol'], value.lower()))

def _validate_target(value: str) -> bool:
    """验证目标格式（URL、域名或IP）"""
    return _validate_url(value) or _validate_domain(value) or _validate_ip(value)

def _validate_ip_port(value: str) -> bool:
    """验证IP:PORT格式"""
    if not re.match(VALIDATION_PATTERNS['ip_port'], value):
        return False

    # 检查每个IP部分是否为禁用值
    ip_port_pairs = value.split(',')
    forbidden_ips = {'localhost', '127.0.0.1', 'example.com', 'test.com'}

    for pair in ip_port_pairs:
        if ':' in pair:
            ip_part = pair.split(':')[0]
            if ip_part.lower() in forbidden_ips:
                return False
    return True

# 验证器映射表
VALIDATORS: Dict[str, Callable[[str], bool]] = {
    ParameterType.URL: _validate_url,
    ParameterType.DOMAIN: _validate_domain,
    ParameterType.IP: _validate_ip,
    ParameterType.HOST: _validate_host,
    ParameterType.PORT: _validate_port,
    ParameterType.PORT_RANGE: _validate_port_range,
    ParameterType.PROTOCOL: _validate_protocol,
    ParameterType.TARGET: _validate_target,
    ParameterType.IP_PORT: _validate_ip_port,
}

# 错误消息映射表
ERROR_MESSAGES_MAP: Dict[str, str] = {
    ParameterType.URL: "is not a valid URL format. Must include protocol (http/https).",
    ParameterType.DOMAIN: "is not a valid domain format.",
    ParameterType.IP: "is not a valid IP address format.",
    ParameterType.HOST: "is not a valid hostname or IP address.",
    ParameterType.PORT: "is not a valid port number (1-65535).",
    ParameterType.PORT_RANGE: "is not a valid port range format.",
    ParameterType.PROTOCOL: "is not a supported protocol.",
    ParameterType.TARGET: "is not a valid target format (URL, domain, or IP).",
    ParameterType.IP_PORT: "is not a valid IP:PORT format. Example: '*************:22' or '*************:22,*************:445'",
}

def _check_basic_validity(param_name: str, value: str) -> Tuple[bool, Optional[str]]:
    """检查参数的基本有效性"""
    if not value or not isinstance(value, str):
        return False, f"Parameter '{param_name}' cannot be empty"

    if value.lower() in FORBIDDEN_VALUES:
        return False, f"'{value}' is a placeholder value. Please provide a real {param_name}."

    return True, None

def _validate_format(value: str, param_type: str) -> Tuple[bool, Optional[str]]:
    """验证参数格式"""
    validator = VALIDATORS.get(param_type)
    if not validator:
        return True, None  # 未知类型跳过格式验证

    if not validator(value):
        error_msg = ERROR_MESSAGES_MAP.get(param_type, "is not in valid format.")
        return False, f"'{value}' {error_msg}"

    return True, None

def validate_parameter_value(param_name: str, value: str, param_type: str) -> Tuple[bool, Optional[str]]:
    """
    验证参数值的有效性 - 重构版本，降低复杂度

    Args:
        param_name: 参数名称
        value: 参数值
        param_type: 参数类型

    Returns:
        tuple: (是否有效, 错误信息)
    """
    # 检查基本有效性
    is_valid, error_msg = _check_basic_validity(param_name, value)
    if not is_valid:
        return is_valid, error_msg

    # 检查格式有效性
    return _validate_format(value, param_type)

# 工具参数规则定义
TOOL_PARAMETER_RULES: Dict[str, Dict[str, Dict[str, Any]]] = {
    'nuclei_scan_wrapper': {
        'target': {
            'type': ParameterType.TARGET,
            'required': True,
            'description': 'Target URL, domain, or IP address for security scanning',
            'examples': ['http://scanme.nmap.org', 'scanme.nmap.org', '************'],
            'forbidden_examples': ['http://example.com', 'example.com', 'target.com']
        }
    },

    'rustscan_wrapper': {
        'target': {
            'type': ParameterType.HOST,
            'required': True,
            'description': 'Target IP address or hostname for port scanning',
            'examples': ['scanme.nmap.org', '************', '*************'],
            'forbidden_examples': ['localhost', '127.0.0.1', 'example.com']
        },
        'ports': {
            'type': ParameterType.PORT_RANGE,
            'required': False,
            'description': 'Port specification (e.g., "80,443", "1-1000")',
            'examples': ['80,443', '1-1000', '22,80,443,8080'],
            'forbidden_examples': ['your-port', 'target-port']
        }
    },

    'spray_wrapper': {
        'url': {
            'type': ParameterType.URL,
            'required': True,
            'description': 'Target URL with protocol for directory enumeration',
            'examples': ['http://scanme.nmap.org', 'https://httpbin.org'],
            'forbidden_examples': ['http://example.com', 'example.com', 'target.com']
        }
    },

    'katana_wrapper': {
        'target': {
            'type': ParameterType.URL,
            'required': True,
            'description': 'Target URL for web crawling',
            'examples': ['http://scanme.nmap.org', 'https://httpbin.org'],
            'forbidden_examples': ['http://example.com', 'example.com', 'target.com']
        }
    },

    'subfinder_wrapper': {
        'domain': {
            'type': ParameterType.DOMAIN,
            'required': True,
            'description': 'Target domain for subdomain enumeration',
            'examples': ['hackerone.com', 'bugcrowd.com'],
            'forbidden_examples': ['example.com', 'test.com', 'target.com']
        }
    },
    'tlsx_wrapper': {
        'host': {
            'type': ParameterType.HOST,
            'required': True,
            'description': 'Target hostname or IP address for TLS analysis',
            'examples': ['scanme.nmap.org', '************', 'httpbin.org', '*************'],
            'forbidden_examples': ['example.com', 'localhost', '127.0.0.1']
        },
        'port': {
            'type': ParameterType.PORT,
            'required': False,
            'description': 'Target port number for TLS service (default: 443)',
            'examples': ['443', '8443', '9443'],
            'forbidden_examples': ['your-port', 'target-port']
        }
    },

    'fingerprintx_wrapper': {
        'ip': {
            'type': ParameterType.IP,
            'required': True,
            'description': 'Target IP address for service fingerprinting',
            'examples': ['************', '*******', '*******', '*************'],
            'forbidden_examples': ['127.0.0.1', 'localhost', 'your-ip']
        },
        'port': {
            'type': ParameterType.PORT,
            'required': True,
            'description': 'Target port number as string',
            'examples': ['80', '443', '22', '3389'],
            'forbidden_examples': ['your-port', 'target-port']
        }
    },

    'httpx_wrapper': {
        'url': {
            'type': ParameterType.URL,
            'required': True,
            'description': 'Target URL for HTTP probing',
            'examples': ['http://scanme.nmap.org', 'https://httpbin.org', 'http://testphp.vulnweb.com'],
            'forbidden_examples': ['http://example.com', 'https://test.com', 'localhost']
        },
        'method': {
            'type': 'string',
            'required': False,
            'description': 'HTTP method to use (default: GET)',
            'examples': ['GET', 'POST', 'PUT', 'DELETE', 'HEAD', 'OPTIONS'],
            'forbidden_examples': ['METHOD', 'your-method']
        },
        'headers': {
            'type': 'dict',
            'required': False,
            'description': 'HTTP headers as dictionary',
            'examples': ['{"User-Agent": "Mozilla/5.0"}', '{"Accept": "application/json"}'],
            'forbidden_examples': ['{}', 'headers']
        },
        'data': {
            'type': 'string',
            'required': False,
            'description': 'HTTP request body data',
            'examples': ['param1=value1&param2=value2', '{"key": "value"}'],
            'forbidden_examples': ['data', 'body', 'payload']
        }
    },

    'netexec_wrapper': {
        'protocol': {
            'type': ParameterType.PROTOCOL,
            'required': True,
            'description': 'Protocol for authentication',
            'examples': ['ssh', 'smb', 'winrm', 'wmi', 'mssql'],
            'forbidden_examples': ['protocol', 'your-protocol']
        },
        'target': {
            'type': ParameterType.HOST,
            'required': True,
            'description': 'Target IP address or hostname',
            'examples': ['*************', 'scanme.nmap.org', '**********'],
            'forbidden_examples': ['localhost', '127.0.0.1', 'target.com']
        },
        'port': {
            'type': ParameterType.PORT,
            'required': True,
            'description': 'Target port number as string',
            'examples': ['22', '445', '5985', '1433', '135'],
            'forbidden_examples': ['port', 'your-port']
        },
        'user': {
            'type': 'string',
            'required': True,
            'description': 'Username for authentication',
            'examples': ['administrator', 'root', 'sa', 'admin'],
            'forbidden_examples': ['user', 'username', 'your-user']
        },
        'password': {
            'type': 'string',
            'required': True,
            'description': 'Password for authentication',
            'examples': ['SecurePass123', 'AdminPass2024'],
            'forbidden_examples': ['password', 'pass', 'your-password', '123456']
        },
        'command': {
            'type': 'string',
            'required': True,
            'description': 'Command to execute on target',
            'examples': ['whoami', 'id', 'SELECT @@version', 'dir'],
            'forbidden_examples': ['command', 'cmd', 'your-command']
        }
    },

    'crack_wrapper': {
        'protocol': {
            'type': ParameterType.PROTOCOL,
            'required': True,
            'description': 'Protocol to attack',
            'examples': ['ssh', 'ftp', 'smb', 'mysql', 'mssql', 'rdp'],
            'forbidden_examples': ['protocol', 'your-protocol']
        },
        'targets': {
            'type': ParameterType.IP_PORT,
            'required': True,
            'description': 'Target services in IP:PORT format',
            'examples': ['*************:22', '**********:3389', '*************:22,*************:445'],
            'forbidden_examples': ['localhost:22', '127.0.0.1:80', 'target:port']
        },
        'usernames': {
            'type': 'string',
            'required': False,
            'description': 'Username list for brute force',
            'examples': ['admin,root,user', '/path/to/users.txt', 'administrator'],
            'forbidden_examples': ['user', 'username', 'users']
        },
        'passwords': {
            'type': 'string',
            'required': False,
            'description': 'Password list for brute force',
            'examples': ['password123,admin,123456', '/path/to/passwords.txt', 'SecurePass123'],
            'forbidden_examples': ['password', 'pass', 'passwords']
        }
    }
}

def get_parameter_examples(tool_name: str, param_name: str) -> List[str]:
    """
    获取参数的示例值

    Args:
        tool_name: 工具名称
        param_name: 参数名称

    Returns:
        List[str]: 示例值列表
    """
    if tool_name in TOOL_PARAMETER_RULES and param_name in TOOL_PARAMETER_RULES[tool_name]:
        return TOOL_PARAMETER_RULES[tool_name][param_name].get('examples', [])
    return []

def get_parameter_description(tool_name: str, param_name: str) -> str:
    """
    获取参数的描述信息

    Args:
        tool_name: 工具名称
        param_name: 参数名称

    Returns:
        str: 参数描述
    """
    if tool_name in TOOL_PARAMETER_RULES and param_name in TOOL_PARAMETER_RULES[tool_name]:
        return TOOL_PARAMETER_RULES[tool_name][param_name].get('description', '')
    return ''

def is_forbidden_value(value: str) -> bool:
    """
    检查值是否在禁用列表中

    Args:
        value: 要检查的值

    Returns:
        bool: 是否为禁用值
    """
    return value.lower() in FORBIDDEN_VALUES

def get_suggested_alternatives(param_type: str) -> List[str]:
    """
    根据参数类型获取建议的真实示例

    Args:
        param_type: 参数类型

    Returns:
        List[str]: 建议的真实示例
    """
    suggestions = {
        ParameterType.URL: [
            'http://scanme.nmap.org',
            'https://httpbin.org',
            'http://testphp.vulnweb.com'
        ],
        ParameterType.DOMAIN: [
            'hackerone.com',
            'bugcrowd.com',
            'scanme.nmap.org'
        ],
        ParameterType.HOST: [
            'scanme.nmap.org',
            '************',
            'httpbin.org',
            '*************'
        ],
        ParameterType.IP: [
            '************',
            '*******',
            '*******',
            '*************'
        ]
    }

    return suggestions.get(param_type, [])

# 错误消息模板
ERROR_MESSAGES = {
    'placeholder_detected': "❌ Error: '{value}' appears to be a placeholder. Please provide a real {param_name}.",
    'invalid_format': "❌ Error: '{value}' is not a valid {param_type} format.",
    'missing_required': "❌ Error: Required parameter '{param_name}' is missing. Please provide a real value.",
    'ask_for_real_value': "Please provide a real {param_name}. For example: {examples}",
}
