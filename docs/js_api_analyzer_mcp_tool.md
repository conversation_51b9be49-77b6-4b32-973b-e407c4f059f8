# JS API Analyzer MCP Tool Documentation

## Overview

The JS API Analyzer MCP Tool is a powerful web security assessment tool that discovers and validates JavaScript API endpoints in web applications. It automatically crawls websites, parses JavaScript files, extracts API endpoints, and tests them for accessibility and security issues.

## Features

### Core Functionality
- **JavaScript File Discovery**: Automatically discovers and downloads JavaScript files from target websites
- **API Endpoint Extraction**: Uses advanced regex patterns to extract API endpoints from JavaScript code
- **Live API Validation**: Tests discovered endpoints to verify they're accessible and functional
- **Smart Filtering**: Filters out non-functional APIs (404s, network errors) by default
- **Concurrent Processing**: Uses async/await for efficient parallel processing

### Security Analysis
- **Sensitive Data Detection**: Identifies potential sensitive information exposure
- **Error Information Disclosure**: Detects verbose error messages that could aid attackers
- **Security Headers Analysis**: Checks for important security headers
- **CORS Configuration Analysis**: Analyzes Cross-Origin Resource Sharing settings
- **Authentication Testing**: Supports custom authentication headers

### Output Features
- **Structured JSON Output**: Returns well-formatted JSON with comprehensive analysis results
- **Status Code Reporting**: Provides HTTP status codes for all discovered endpoints
- **Filtering Options**: Configurable filtering to include/exclude specific response types
- **Summary Statistics**: Provides counts of discovered vs valid APIs

## Installation & Setup

The JS API Analyzer is already integrated into the WebSec MCP framework. No additional installation is required.

### Dependencies
- Python 3.7+
- aiohttp
- beautifulsoup4
- urllib3
- asyncio (built-in)

## Usage

### Basic Usage

```python
# Through MCP interface
result = await mcp_tool_call("js_api_analyzer_valid_only", {
    "target": "https://example.com"
})
```

### Advanced Usage

```python
# With authentication and custom settings
result = await mcp_tool_call("js_api_analyzer_valid_only", {
    "target": "https://api.company.com",
    "timeout": 30,
    "max_depth": 3,
    "auth_headers": {"Authorization": "Bearer token123"},
    "max_workers": 20,
    "include_404": True,
    "verbose": True
})
```

## Parameters

### Required Parameters

| Parameter | Type | Description | Example |
|-----------|------|-------------|---------|
| `target` | string | Target URL to analyze for JavaScript APIs. Must include http/https protocol. | `"https://example.com"` |

### Optional Parameters

| Parameter | Type | Default | Description | Example |
|-----------|------|---------|-------------|---------|
| `timeout` | integer | 10 | Request timeout in seconds | `30` |
| `max_depth` | integer | 2 | Maximum crawling depth | `3` |
| `auth_headers` | dictionary | None | Authentication headers | `{"Authorization": "Bearer token"}` |
| `max_workers` | integer | 10 | Maximum concurrent workers | `20` |
| `include_404` | boolean | False | Include 404 status code APIs in results | `true` |
| `verbose` | boolean | False | Enable verbose output | `true` |

## Output Format

The tool returns a JSON object with the following structure:

```json
{
  "target": "https://example.com",
  "scan_time": "2024-01-01 12:00:00",
  "total_discovered": 25,
  "valid_count": 15,
  "filtered_count": 10,
  "valid_apis": [
    "GET /api/users (200)",
    "POST /api/login (200)",
    "GET /api/data (403)",
    "PUT /api/users/123 (200)",
    "DELETE /api/users/123 (401)"
  ]
}
```

### Output Fields

| Field | Type | Description |
|-------|------|-------------|
| `target` | string | The target URL that was analyzed |
| `scan_time` | string | Timestamp when the scan was performed |
| `total_discovered` | integer | Total number of API endpoints discovered |
| `valid_count` | integer | Number of valid/accessible API endpoints |
| `filtered_count` | integer | Number of endpoints filtered out (404s, errors) |
| `valid_apis` | array | List of valid API endpoints with their HTTP methods and status codes |

## Examples

### Example 1: Basic Website Analysis

```python
# Analyze a simple website
result = await mcp_tool_call("js_api_analyzer_valid_only", {
    "target": "https://httpbin.org"
})

# Expected output:
{
  "target": "https://httpbin.org",
  "scan_time": "2024-01-01 12:00:00",
  "total_discovered": 5,
  "valid_count": 3,
  "filtered_count": 2,
  "valid_apis": [
    "GET /get (200)",
    "POST /post (200)",
    "PUT /put (200)"
  ]
}
```

### Example 2: Authenticated API Analysis

```python
# Analyze an API with authentication
result = await mcp_tool_call("js_api_analyzer_valid_only", {
    "target": "https://api.company.com",
    "auth_headers": {
        "Authorization": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
        "X-API-Key": "your-api-key-here"
    },
    "timeout": 45,
    "max_depth": 3
})
```

### Example 3: Comprehensive Security Analysis

```python
# Perform comprehensive analysis including 404s
result = await mcp_tool_call("js_api_analyzer_valid_only", {
    "target": "https://webapp.example.com",
    "include_404": True,
    "verbose": True,
    "max_workers": 25,
    "timeout": 60
})
```

## Best Practices

### Target Selection
- Always use complete URLs with protocols (http/https)
- Test on staging environments before production
- Ensure you have permission to test the target
- Use specific URLs rather than generic domains

### Authentication
- Use proper authentication headers for protected APIs
- Store sensitive tokens securely
- Rotate API keys regularly
- Test with different permission levels

### Performance Optimization
- Adjust `max_workers` based on target server capacity
- Use appropriate `timeout` values for slow servers
- Limit `max_depth` for large websites
- Monitor resource usage during scans

### Security Considerations
- Always get permission before testing third-party websites
- Be mindful of rate limiting and DoS protection
- Use responsible disclosure for found vulnerabilities
- Document all findings properly

## Common Use Cases

### Web Application Security Assessment
```python
# Comprehensive security assessment
result = await mcp_tool_call("js_api_analyzer_valid_only", {
    "target": "https://webapp.company.com",
    "auth_headers": {"Authorization": "Bearer user-token"},
    "max_depth": 4,
    "include_404": True,
    "verbose": True
})
```

### API Discovery for Penetration Testing
```python
# Discover hidden APIs for penetration testing
result = await mcp_tool_call("js_api_analyzer_valid_only", {
    "target": "https://target.com",
    "max_depth": 5,
    "max_workers": 30,
    "timeout": 120
})
```

### Bug Bounty Hunting
```python
# Quick API discovery for bug bounty programs
result = await mcp_tool_call("js_api_analyzer_valid_only", {
    "target": "https://bounty-target.com",
    "max_depth": 3,
    "include_404": False,
    "verbose": False
})
```

## Troubleshooting

### Common Issues

#### 1. Connection Timeouts
```
Error: Request timeout after 10 seconds
```
**Solution**: Increase the timeout parameter
```python
{"target": "https://slow-site.com", "timeout": 60}
```

#### 2. No APIs Found
```
{
  "valid_count": 0,
  "valid_apis": []
}
```
**Solutions**:
- Increase `max_depth` to crawl deeper
- Check if the site uses JavaScript frameworks
- Verify authentication headers if required
- Try including 404s with `include_404: true`

#### 3. Rate Limiting
```
Error: Too many requests (429)
```
**Solutions**:
- Reduce `max_workers`
- Increase delays between requests
- Use authentication if available
- Implement retry logic

#### 4. Authentication Issues
```
Error: Unauthorized (401)
```
**Solutions**:
- Verify authentication headers
- Check token expiration
- Ensure proper API key format
- Test authentication separately

### Debug Mode

Enable verbose mode for detailed debugging:
```python
{
    "target": "https://example.com",
    "verbose": True
}
```

This will provide detailed logging of:
- JavaScript files discovered
- API extraction process
- HTTP requests and responses
- Error details

## Integration with WebSec Framework

The JS API Analyzer is fully integrated with the WebSec MCP framework and inherits all its features:

- **Error Handling**: Comprehensive error handling and logging
- **Timeout Management**: Consistent timeout handling across all tools
- **Parameter Validation**: Automatic input validation and sanitization
- **Response Formatting**: Standardized response format
- **Security**: Built-in security measures and best practices

## Limitations

- **JavaScript Parsing**: Limited to static analysis of JavaScript code
- **Dynamic Content**: Cannot analyze dynamically generated endpoints
- **Authentication**: Requires manual configuration of authentication headers
- **Rate Limiting**: May trigger rate limiting on some servers
- **Framework Specific**: May miss APIs in certain JavaScript frameworks

## Future Enhancements

- **Dynamic Analysis**: Support for headless browser analysis
- **Framework Detection**: Automatic detection of JavaScript frameworks
- **Authentication Auto-discovery**: Automatic authentication method detection
- **Response Analysis**: Enhanced response content analysis
- **Report Generation**: Detailed security assessment reports

## Support

For issues, questions, or contributions:
1. Check the troubleshooting section
2. Review the examples and best practices
3. Consult the WebSec framework documentation
4. File an issue in the project repository

## License

This tool is part of the WebSec framework and follows the same licensing terms.