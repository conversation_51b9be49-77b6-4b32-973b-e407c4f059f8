# WebLeak Tool Usage Guide

WebLeak是一个高级的Web应用程序漏洞扫描工具，集成到WebSec MCP服务器中，用于检测弱密码和未授权访问等安全问题。

## 功能特性

- **弱密码检测**: 检测常见的弱密码配置
- **未授权访问检查**: 发现未授权访问的端点和资源
- **多种验证类型**: 支持weak-password、unauth、all三种扫描模式
- **并发扫描**: 支持多线程并发扫描，提高效率
- **批量扫描**: 支持单个URL和批量URL文件扫描
- **灵活输出**: 支持详细输出、静默模式和文件输出

## 参数说明

### 必需参数
- `target`: 单个目标URL（需包含协议）
- `url_file`: 包含URL列表的文件路径

**注意**: `target` 和 `url_file` 必须指定其中一个

### 可选参数
- `verification_type`: 验证类型
  - `"weak-password"`: 仅检测弱密码
  - `"unauth"`: 仅检测未授权访问
  - `"all"`: 执行所有检测（默认）
- `concurrency`: 并发线程数（默认: 10）
- `timeout_param`: 请求超时时间，秒（默认: 10）
- `output_file`: 输出报告文件路径
- `quiet`: 静默模式，仅输出发现的漏洞（默认: false）
- `verbose`: 详细输出模式（默认: false）

## 使用示例

### 1. 基本单URL扫描
```python
# 扫描单个网站，使用默认设置
webleak_wrapper(target="http://scanme.nmap.org")
```

### 2. 指定验证类型
```python
# 仅检测弱密码
webleak_wrapper(
    target="https://httpbin.org", 
    verification_type="weak-password"
)

# 仅检测未授权访问
webleak_wrapper(
    target="https://httpbin.org", 
    verification_type="unauth"
)
```

### 3. 批量URL扫描
```python
# 从文件读取URL列表进行扫描
webleak_wrapper(
    url_file="/path/to/urls.txt",
    verification_type="all",
    concurrency=20
)
```

### 4. 高级配置
```python
# 使用自定义配置进行扫描
webleak_wrapper(
    target="http://testphp.vulnweb.com",
    verification_type="all",
    concurrency=15,
    timeout_param=30,
    output_file="/tmp/webleak_report.txt",
    verbose=True
)
```

### 5. 静默模式扫描
```python
# 静默模式，仅显示发现的漏洞
webleak_wrapper(
    target="https://example.com",
    quiet=True,
    verification_type="all"
)
```

## URL文件格式

当使用`url_file`参数时，文件应包含每行一个URL：

```
http://example1.com
https://example2.com
http://example3.com:8080
https://example4.com/app
```

## 输出说明

### 正常输出
- 扫描进度信息
- 发现的漏洞详情
- 扫描统计信息

### 静默模式输出
- 仅显示发现的漏洞
- 适合自动化脚本处理

### 详细模式输出
- 包含详细的扫描过程
- 显示每个检测步骤
- 适合调试和分析

## 最佳实践

1. **目标选择**
   - 确保目标URL包含正确的协议（http/https）
   - 避免使用占位符URL如"example.com"
   - 确保有权限扫描目标系统

2. **性能优化**
   - 根据目标系统性能调整并发数
   - 设置合适的超时时间
   - 大批量扫描时考虑分批处理

3. **结果处理**
   - 使用输出文件保存扫描结果
   - 静默模式适合自动化处理
   - 详细模式适合手动分析

4. **安全考虑**
   - 仅扫描授权的目标系统
   - 遵守相关法律法规
   - 避免对生产系统造成影响

## 错误处理

常见错误及解决方案：

- **"Either target URL or url_file must be specified"**
  - 解决：必须指定target或url_file参数之一

- **"Invalid verification type"**
  - 解决：verification_type必须是"weak-password"、"unauth"或"all"之一

- **连接超时**
  - 解决：增加timeout_param值或检查网络连接

- **文件不存在**
  - 解决：确保url_file路径正确且文件存在

## 集成说明

WebLeak工具已完全集成到WebSec MCP服务器的Web工具套件中，支持：

- 自动响应处理（100KB阈值）
- 错误处理和日志记录
- 超时保护机制
- 参数验证和清理

使用时无需额外配置，直接调用`webleak_wrapper`函数即可。
