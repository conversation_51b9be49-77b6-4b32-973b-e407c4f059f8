# MCP 工具调用数据库集成文档

## 概述

本文档描述了为 WebSec MCP 服务器添加的数据库集成功能，用于存储 MCP 工具调用的参数和结果到 PostgreSQL 数据库中。

## 功能特性

### 🎯 核心功能
- **自动记录**: 所有 MCP 工具调用自动记录到数据库
- **参数存储**: 完整保存调用参数（JSON 格式）
- **结果存储**: 保存工具执行结果和状态
- **性能监控**: 记录执行时间和结果大小
- **错误跟踪**: 详细记录错误信息和状态

### 📊 统计分析
- **调用历史**: 查询历史调用记录
- **统计信息**: 工具使用频率和性能统计
- **成功率分析**: 各工具的成功/失败率
- **性能分析**: 平均执行时间和结果大小

### 🧹 数据管理
- **自动清理**: 定期清理过期记录
- **连接池**: 高效的数据库连接管理
- **异步操作**: 非阻塞数据库操作

## 数据库结构

### 主要表结构

#### `mcp_tool_calls` - MCP 工具调用记录表
```sql
CREATE TABLE mcp_tool_calls (
    id SERIAL PRIMARY KEY,
    call_id VARCHAR(100) UNIQUE NOT NULL,
    tool_name VARCHAR(50) NOT NULL,
    function_name VARCHAR(100) NOT NULL,
    call_parameters JSONB NOT NULL DEFAULT '{}',
    call_result TEXT,
    result_size INTEGER DEFAULT 0,
    execution_time_ms INTEGER,
    status VARCHAR(20) DEFAULT 'pending',
    error_message TEXT,
    start_time TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    end_time TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    metadata JSONB DEFAULT '{}',
    result_id VARCHAR(100) REFERENCES tool_results(result_id)
);
```

#### 状态值说明
- `pending`: 调用开始，正在执行
- `success`: 执行成功
- `error`: 执行失败
- `timeout`: 执行超时

### 统计视图

#### `mcp_call_statistics` - 调用统计视图
提供按工具和函数分组的统计信息：
- 总调用次数
- 成功/失败/超时次数
- 平均执行时间
- 平均结果大小

## 实现细节

### 1. 数据库管理器 (`database/db_manager.py`)

#### 核心类: `DatabaseManager`
```python
class DatabaseManager:
    async def record_mcp_call_start(self, tool_name, function_name, parameters, metadata)
    async def record_mcp_call_end(self, call_id, result, status, error_message, execution_time_ms)
    async def get_mcp_call_history(self, tool_name, function_name, limit)
    async def get_mcp_call_statistics(self)
    async def cleanup_old_records(self, days_to_keep)
```

#### 连接池管理
- 异步连接池 (asyncpg)
- 同步连接池 (psycopg2) 作为备用
- 自动重连和错误处理

### 2. 装饰器集成 (`server.py`)

#### `error_handler` 装饰器增强
```python
def error_handler(func):
    @wraps(func)
    async def wrapper(*args, **kwargs):
        # 1. 提取函数参数
        # 2. 记录调用开始
        # 3. 执行原函数
        # 4. 记录调用结束（成功/失败）
        # 5. 返回结果
```

#### 自动参数提取
- 使用 `inspect.signature()` 提取函数参数
- 自动过滤 `self` 参数
- JSON 序列化参数值

### 3. 服务器集成

#### 数据库初始化
```python
class WebSecMCP:
    async def initialize_database(self):
        # 加载数据库配置
        # 初始化连接池
        # 设置初始化标志
```

#### 异步服务器启动
```python
async def run_server_async(self):
    # 初始化数据库
    await self.initialize_database()
    # 启动 MCP 服务器
    self.mcp.run(transport="sse", host="0.0.0.0", port=8000)
```

## 配置要求

### 数据库配置 (`config/config.yaml`)
```yaml
Database:
  host: localhost
  port: 5432
  user: postgres
  password: your_password
  name: websec_mcp
  min_size: 5
  max_size: 20
```

### 依赖包
```bash
pip install psycopg2-binary asyncpg
```

## 使用示例

### 1. 查询调用历史
```python
from database.db_manager import get_db_manager

db_manager = get_db_manager()
history = await db_manager.get_mcp_call_history(
    tool_name="nuclei_scan_wrapper",
    limit=10
)
```

### 2. 获取统计信息
```python
stats = await db_manager.get_mcp_call_statistics()
print(f"Total calls: {stats['total_statistics']['total_calls']}")
```

### 3. 清理旧记录
```python
deleted_count = await db_manager.cleanup_old_records(days_to_keep=30)
print(f"Cleaned up {deleted_count} old records")
```

## 测试和验证

### 测试脚本
- `test_database.py`: 完整的数据库功能测试
- `demo_mcp_recording.py`: 演示脚本展示记录功能

### 运行测试
```bash
# 初始化数据库表
psql -h localhost -U postgres -d websec_mcp -f database/init_result_storage.sql

# 运行功能测试
python test_database.py

# 运行演示
python demo_mcp_recording.py
```

## 性能考虑

### 1. 异步操作
- 所有数据库操作都是异步的
- 不会阻塞 MCP 工具执行
- 数据库错误不会影响工具正常运行

### 2. 连接池
- 预分配连接池减少连接开销
- 自动连接管理和回收
- 支持并发操作

### 3. 错误处理
- 数据库不可用时优雅降级
- 详细的错误日志记录
- 不影响核心 MCP 功能

## 监控和维护

### 1. 日志监控
- 数据库连接状态
- 记录操作成功/失败
- 性能指标

### 2. 定期维护
- 自动清理过期记录
- 数据库性能优化
- 索引维护

### 3. 查询优化
- 为常用查询字段创建索引
- JSONB 字段的 GIN 索引
- 分区表支持（未来扩展）

## 故障排除

### 常见问题

1. **数据库连接失败**
   - 检查配置文件中的数据库连接信息
   - 确认 PostgreSQL 服务运行状态
   - 验证用户权限

2. **表不存在错误**
   - 运行数据库初始化脚本
   - 检查数据库名称是否正确

3. **权限错误**
   - 确认数据库用户有足够权限
   - 检查表和函数的访问权限

### 调试技巧
- 启用详细日志记录
- 使用测试脚本验证功能
- 检查数据库连接池状态

## 未来扩展

### 计划功能
- 实时监控仪表板
- 更详细的性能分析
- 数据导出功能
- 告警和通知系统

### 架构改进
- 分布式数据库支持
- 数据压缩和归档
- 更高级的查询接口
