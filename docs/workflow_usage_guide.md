# 工作流使用指南

## 概述

为了解决AI在选择MCP工具时的随机性问题，我们引入了预定义的工作流系统。这个系统提供了固定的渗透测试流程，确保测试的一致性和完整性。

## 核心优势

### 1. 消除随机性
- **固定流程**: 每个工作流都有预定义的步骤序列
- **参数模板**: 标准化的参数配置，减少参数构造的随机性
- **依赖管理**: 明确的步骤依赖关系，确保执行顺序

### 2. 提高效率
- **自动化执行**: 一键执行完整的测试流程
- **智能选择**: 根据目标类型自动推荐最适合的工作流
- **级联测试**: 发现Web服务时自动进行深度测试
- **并行处理**: 支持独立步骤的并行执行

### 3. 标准化报告
- **统一格式**: 所有工作流生成标准化的测试报告
- **详细记录**: 记录每个步骤的执行状态和结果
- **级联报告**: 包含主测试和级联测试的综合报告
- **错误追踪**: 详细的错误信息和失败原因

### 4. 智能级联测试
- **自动发现**: 在网络扫描中自动识别Web服务
- **深度测试**: 对发现的Web服务执行完整的安全测试
- **综合分析**: 提供从网络到应用层的全面安全评估

### 5. 专项Web漏洞检测
- **webleak扫描**: 专门检测Web应用的弱密码和未授权访问
- **分层检测**: 先进行专项扫描，再进行综合漏洞扫描
- **全面覆盖**: 结合多种扫描引擎确保漏洞检测的完整性

## 可用工作流

### 1. Web应用渗透测试 (web_pentest)
**适用目标**: HTTP/HTTPS URL
**执行步骤**:
1. HTTP服务探测 (httpx_wrapper)
2. TLS/SSL配置分析 (tlsx_wrapper) - 仅HTTPS
3. 目录枚举 (spray_wrapper)
4. 爬虫分析 (katana_wrapper)
5. **Web应用漏洞扫描** (webleak_wrapper) - 弱密码和未授权访问检测
6. 综合漏洞扫描 (nuclei_scan_wrapper)

**使用示例**:
```
execute_workflow_wrapper("web_pentest", "http://example.com")
```

**新增特性**:
- **webleak_wrapper**: 专门检测Web应用的弱密码和未授权访问漏洞
- **分层扫描**: 先进行专项Web漏洞扫描，再进行综合漏洞扫描
- **更全面的覆盖**: 结合多种扫描工具提供更完整的安全评估

### 2. 网络服务渗透测试 (network_pentest)
**适用目标**: IP地址
**执行步骤**:
1. 端口扫描 (rustscan_wrapper)
2. 服务指纹识别 (fingerprintx_wrapper)
3. 服务漏洞扫描 (nuclei_scan_wrapper)
4. **🆕 Web服务验证** (httpx_web_verification) - 实际HTTP请求验证Web服务
5. **级联Web服务测试** - 对已验证的Web服务执行完整的Web测试流程

**使用示例**:
```
execute_workflow_wrapper("network_pentest", "***********")
```

**增强的Web服务检测**:
- **🆕 全端口扫描**: 对所有发现的端口进行Web服务验证（排除明显非Web端口）
- **🆕 双协议验证**: 每个端口都尝试HTTP和HTTPS协议
- **🆕 智能过滤**: 自动排除SSH(22)、DNS(53)、数据库端口等明显非Web服务
- **精确级联**: 只对真正响应HTTP请求的服务执行Web测试
- **详细报告**: 包含每个端口验证过程和结果的完整报告

### 3. 子域名发现与网络渗透测试 (subdomain_discovery)
**适用目标**: 域名
**执行步骤**:
1. 子域名发现 (comprehensive_subdomain_discovery_wrapper)
2. **DNS解析** - 将子域名解析为IP地址
3. Web服务存活检测 (httpx_wrapper)
4. **级联网络渗透测试** - 对解析的IP执行完整的network_pentest工作流
5. **级联Web服务深度测试** - 对存活的Web服务进行深度安全测试

**使用示例**:
```
execute_workflow_wrapper("subdomain_discovery", "example.com")
```

**增强特性**:
- **DNS解析**: 自动将发现的子域名解析为IP地址
- **双重级联测试**: 
  - 对解析的IP进行网络层渗透测试
  - 对存活的Web服务进行应用层安全测试
- **全面覆盖**: 从域名发现到网络扫描再到Web安全的完整测试链
- **智能关联**: 在报告中关联IP地址与对应的域名信息

## MCP工具使用方法

### 1. 查看可用工作流
```
list_workflows_wrapper()
```
返回所有可用的工作流及其描述。

### 2. 检测目标类型
```
detect_target_type_wrapper("http://example.com")
```
分析目标类型并推荐最适合的工作流。

### 3. 执行指定工作流
```
execute_workflow_wrapper("web_pentest", "http://example.com")
```
执行指定的工作流，可选参数：
- `stop_on_error`: 遇到错误时是否停止（默认: false）
- `timeout`: 最大执行时间（默认: 300秒）

### 4. 自动渗透测试
```
auto_pentest_wrapper("http://example.com")
```
最简单的使用方式，自动检测目标类型并执行相应工作流。

## 配置自定义工作流

### 1. 编辑工作流配置
编辑 `config/workflow_config.yaml` 文件来自定义工作流：

```yaml
workflows:
  custom_web_test:
    name: "自定义Web测试"
    description: "针对特定需求的Web测试流程"
    steps:
      - step: 1
        tool: "httpx_wrapper"
        description: "基础HTTP探测"
        required_params: ["url"]
        default_params:
          method: "GET"
      - step: 2
        tool: "nuclei_scan_wrapper"
        description: "快速漏洞扫描"
        required_params: ["target"]
        depends_on: [1]
```

### 2. 参数模板配置
定义常用的参数模板：

```yaml
parameter_templates:
  stealth_headers:
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
    "Accept": "text/html,application/xhtml+xml"
    "Accept-Language": "en-US,en;q=0.9"
    
  aggressive_scan:
    timeout: 600
    threads: 50
```

### 3. 目标检测规则
自定义目标类型检测规则：

```yaml
target_detection:
  custom_patterns:
    - pattern: "^https://.*\\.gov$"
      type: "government_site"
      workflow: "careful_web_pentest"
```

## Web漏洞扫描工具详解

### webleak_wrapper - Web应用专项漏洞扫描
**功能特点**:
- **弱密码检测**: 检测常见的弱密码配置
- **未授权访问**: 发现未授权访问的敏感页面和接口
- **快速扫描**: 针对Web应用优化的高效扫描引擎
- **精准检测**: 专门针对Web应用漏洞的检测逻辑

**在工作流中的作用**:
- 位于目录枚举和爬虫分析之后
- 利用前面步骤发现的路径和接口信息
- 为后续的nuclei综合扫描提供补充

**检测内容**:
- 默认密码和弱密码
- 未授权的管理界面
- 敏感文件和目录访问
- 配置错误导致的安全问题

## 最佳实践

### 1. 选择合适的工作流
- **Web应用**: 使用 `web_pentest` 或 `auto_pentest_wrapper`
- **网络设备**: 使用 `network_pentest`
- **域名侦察**: 使用 `subdomain_discovery`

### 2. 参数配置建议
- 使用真实的目标地址，避免示例值
- 根据目标环境调整超时时间
- 在生产环境中启用 `stop_on_error`

### 3. 结果分析
- 关注工作流执行摘要
- 检查失败步骤的错误信息
- 根据结果调整后续测试策略

### 4. 错误处理
- 检查网络连接和目标可达性
- 验证API密钥配置
- 查看日志文件获取详细错误信息

## 故障排除

### 常见问题

1. **工作流执行失败**
   - 检查目标地址是否有效
   - 验证网络连接
   - 查看具体的错误信息
   - 确认所有必需的工具都已正确安装

2. **步骤跳过**
   - 检查步骤依赖关系
   - 验证执行条件
   - 确认前置步骤是否成功

3. **参数验证错误**
   - 使用真实的目标地址
   - 检查参数格式是否正确
   - 参考参数示例

4. **工具调用失败**
   - 工作流系统已修复工具调用问题
   - 支持自动参数提取和传递
   - 端口信息会自动从扫描结果中提取

5. **级联测试条件**
   - **第一阶段**: 检测潜在Web端口 (80, 443, 8080, 8443等)
   - **第二阶段**: 🆕 HTTP请求验证服务可用性
   - **第三阶段**: 对已验证的Web服务执行级联测试
   - 如果任一阶段失败，后续步骤会被跳过（这是正常的智能行为）

### 日志查看
```bash
tail -f logs/websec_mcp.log
```

### 配置验证
确保以下配置文件存在且格式正确：
- `config/config.yaml` - 基础配置
- `config/workflow_config.yaml` - 工作流配置

## 扩展开发

### 添加新工具
1. 在相应的工具模块中实现功能
2. 在 `server.py` 中注册MCP工具
3. 在工作流配置中添加工具调用

### 创建新工作流
1. 在 `workflow_config.yaml` 中定义工作流
2. 配置步骤依赖和参数
3. 测试工作流执行

### 自定义报告格式
修改 `utils/workflow_executor.py` 中的 `generate_workflow_report` 方法来自定义报告格式。

## 总结

工作流系统通过预定义的测试流程有效解决了AI工具选择的随机性问题，提供了：

- **一致性**: 每次执行相同的测试步骤
- **完整性**: 确保不遗漏重要的测试环节
- **可重复性**: 相同目标得到相同的测试结果
- **可扩展性**: 支持自定义工作流和参数配置

使用 `auto_pentest_wrapper` 是最简单的开始方式，它会自动选择最适合的工作流并执行完整的渗透测试。