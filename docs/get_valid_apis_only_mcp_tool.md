# get_valid_apis_only MCP Tool 注册文档

## 概述

`get_valid_apis_only` 函数已成功注册为 MCP (Model Context Protocol) 工具，工具名称为 `get_valid_apis_only_wrapper`。

## 工具信息

- **工具名称**: `get_valid_apis_only_wrapper`
- **类别**: Web Tools (Web 工具)
- **功能**: JavaScript API 分析器 - 仅返回有效 API
- **源文件**: `tools/js_api_analyzer.py`
- **注册位置**: `server.py` 中的 `_register_web_tools()` 方法

## 参数说明

### 必需参数

| 参数名 | 类型 | 描述 | 示例 |
|--------|------|------|------|
| `target` | string | 目标URL（必须包含协议） | `"https://example.com"` |

### 可选参数

| 参数名 | 类型 | 默认值 | 描述 | 示例 |
|--------|------|--------|------|------|
| `timeout` | int | 10 | 请求超时时间（秒） | `30` |
| `max_depth` | int | 2 | 最大爬取深度 | `3` |
| `auth_headers` | string | null | 认证头信息（JSON格式） | `'{"Authorization": "Bearer token"}'` |
| `max_workers` | int | 10 | 最大并发工作线程数 | `15` |
| `include_404` | bool | false | 是否包含404状态码的API | `true` |
| `verbose` | bool | false | 是否启用详细输出 | `true` |

## 功能特性

1. **智能JS文件发现**: 自动爬取目标网站的JavaScript文件
2. **API端点提取**: 使用智能模式匹配从JS文件中提取API端点
3. **API有效性测试**: 测试发现的API端点的可访问性
4. **结果过滤**: 默认过滤掉404和网络错误的API
5. **认证支持**: 支持自定义认证头
6. **并发处理**: 支持多线程并发处理提高效率

## 使用示例

### 基础用法

```json
{
  "tool": "get_valid_apis_only_wrapper",
  "arguments": {
    "target": "https://example.com"
  }
}
```

### 高级用法

```json
{
  "tool": "get_valid_apis_only_wrapper",
  "arguments": {
    "target": "https://api.example.com",
    "timeout": 30,
    "max_depth": 3,
    "auth_headers": "{\"Authorization\": \"Bearer your-token\", \"User-Agent\": \"Custom-Agent\"}",
    "max_workers": 15,
    "include_404": true,
    "verbose": true
  }
}
```

## 返回格式

工具返回JSON格式的结果：

```json
{
  "target": "https://example.com",
  "scan_time": "2025-07-11 12:15:41",
  "total_discovered": 25,
  "valid_count": 18,
  "filtered_count": 7,
  "valid_apis": [
    "GET https://example.com/api/users (200)",
    "POST https://example.com/api/login (200)",
    "GET https://example.com/api/products (200)"
  ]
}
```

### 返回字段说明

- `target`: 扫描的目标URL
- `scan_time`: 扫描开始时间
- `total_discovered`: 发现的API总数
- `valid_count`: 有效API数量
- `filtered_count`: 被过滤的API数量
- `valid_apis`: 有效API列表（格式：方法 URL (状态码)）

## 错误处理

### 常见错误

1. **URL格式错误**:
```json
{
  "error": "Target URL must include protocol (http:// or https://)",
  "example": "https://example.com"
}
```

2. **认证头格式错误**:
```json
{
  "error": "Invalid auth_headers format. Must be valid JSON.",
  "example": "{\"Authorization\": \"Bearer token\"}"
}
```

## 实现细节

### 注册代码位置

在 `server.py` 的 `_register_web_tools()` 方法中：

```python
@self.mcp.tool()
@error_handler
@validate_input({
    'target': {
        'required': True,
        'type': str,
        'max_length': 500,
        'param_type': ParameterType.URL,
        'description': 'Target URL with protocol for JS API analysis...'
    },
    # ... 其他参数验证
})
async def get_valid_apis_only_wrapper(
    target: str,
    timeout: Optional[int] = 10,
    # ... 其他参数
) -> str:
    # 实现代码
```

### 导入语句

在 `server.py` 顶部添加了导入：

```python
from tools.js_api_analyzer import get_valid_apis_only
```

## 测试验证

工具已通过以下测试：

1. ✅ 函数导入测试
2. ✅ MCP服务器初始化测试
3. ✅ 工具注册验证
4. ✅ 参数验证测试
5. ✅ 功能运行测试

## 使用建议

1. **目标选择**: 使用具体的目标URL以获得更好的结果
2. **深度调整**: 根据网站复杂性调整 `max_depth`
3. **认证配置**: 对于需要认证的端点使用 `auth_headers`
4. **调试模式**: 启用 `verbose` 模式进行调试
5. **性能优化**: 根据网络条件调整 `timeout` 和 `max_workers`

## 相关文件

- 源函数: `tools/js_api_analyzer.py::get_valid_apis_only()`
- 注册代码: `server.py::_register_web_tools()`
- 使用示例: `examples/get_valid_apis_only_mcp_example.py`
- 原始示例: `examples/js_api_analyzer_example.py`
