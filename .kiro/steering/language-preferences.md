---
inclusion: always
---

# 语言和沟通规范

## 输出语言规范
- **AI 助手交流**: 使用中文进行所有对话和解释
- **代码注释**: 使用中文编写注释，提高代码可读性
- **文档内容**: 技术文档和说明使用中文
- **用户界面**: 前端页面内容和用户界面文本使用英文

## 代码风格指南
- 变量名和函数名使用英文，遵循 Python 命名规范
- 类名使用 PascalCase，函数名和变量名使用 snake_case
- 常量使用全大写字母，用下划线分隔
- 文件名使用小写字母和下划线

## 注释规范
```python
# 这是单行注释的示例
def scan_domain(domain: str) -> dict:
    """
    扫描指定域名的资产信息
    
    Args:
        domain: 要扫描的域名
        
    Returns:
        包含扫描结果的字典
    """
    pass
```

## 错误信息和日志
- 错误信息使用中文，便于理解和调试
- 日志记录使用中文描述操作状态
- API 响应消息保持英文，符合国际标准