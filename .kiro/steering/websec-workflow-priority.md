---
inclusion: always
---

# WebSec MCP 工作流优先使用规则

## 核心原则
当用户请求进行渗透测试时，**优先使用工作流工具**而不是单独的工具调用，以确保测试的一致性和完整性。

## 工具选择优先级

### 1. 首选：自动渗透测试
对于任何渗透测试请求，首先推荐使用：
```
auto_pentest_wrapper(target)
```
这个工具会：
- 自动检测目标类型
- 选择最适合的工作流
- 执行完整的测试流程
- 生成标准化报告

### 2. 次选：指定工作流
如果用户有特定需求，使用：
```
execute_workflow_wrapper(workflow_name, target)
```

可用工作流：
- `web_pentest`: Web应用测试 (包含webleak专项扫描)
- `network_pentest`: 网络服务测试 (包含级联Web测试)
- `subdomain_discovery`: 子域名发现与网络渗透测试 (包含DNS解析和双重级联测试)

### 3. 最后：单独工具
只有在以下情况下才使用单独的工具：
- 用户明确要求使用特定工具
- 工作流执行失败需要单独验证
- 进行特定的补充测试

## 使用流程

### 步骤1：目标分析
```
detect_target_type_wrapper(target)
```

### 步骤2：执行测试
```
auto_pentest_wrapper(target)
```

### 步骤3：补充测试（如需要）
根据工作流结果，使用单独工具进行补充测试。

## 参数要求
- **始终使用真实目标**：不要使用example.com、localhost等示例值
- **验证目标格式**：确保URL包含协议，IP地址格式正确
- **询问用户**：如果目标不明确，主动询问用户提供具体目标

## 错误处理
1. 如果工作流执行失败，分析具体原因
2. 提供清晰的错误解释和解决建议
3. 必要时使用单独工具进行故障排除

## 报告生成
工作流工具会自动生成标准化报告，包含：
- 执行摘要
- 详细步骤结果
- 发现的问题和建议
- 执行时间统计

这种方式确保了渗透测试的：
- **一致性**：每次执行相同的测试步骤
- **完整性**：不遗漏重要的测试环节，包括级联Web服务测试
- **可重复性**：相同目标得到相同结果
- **智能化**：自动发现Web服务并进行深度测试
- **专业性**：标准化的测试流程和综合报告

## 级联测试特性

### 网络扫描级联Web测试
当执行 `network_pentest` 时：
1. 端口扫描发现开放端口
2. 识别潜在Web服务端口 (80, 443, 8080等)
3. **🆕 HTTP验证**: 实际发送请求验证Web服务可用性
4. 对已验证的Web服务自动执行完整的 `web_pentest` 工作流
5. 生成包含网络扫描、Web验证和Web测试的综合报告

### 子域名发现双重级联测试  
当执行 `subdomain_discovery` 时：
1. 发现所有子域名
2. **DNS解析** - 将子域名解析为IP地址
3. **级联网络测试** - 对每个IP执行完整的网络渗透测试
4. **级联Web测试** - 对存活的Web服务执行深度安全测试
5. 提供从域名发现到网络扫描再到Web安全的全链路评估

这确保了无论从哪个角度开始测试，都能获得最全面的安全评估结果。

## 级联测试触发条件

### Web服务检测逻辑
级联Web测试只有在检测到Web服务时才会执行：

**三阶段智能检测流程**:

1. **端口过滤阶段**:
   - 扫描所有发现的开放端口
   - 智能排除明显非Web端口: SSH(22), DNS(53), 数据库端口(3306,5432)等
   - 如果所有端口都是明显非Web服务 → 跳过后续步骤

2. **🆕 全面HTTP验证阶段**:
   - 对每个剩余端口发送HTTP和HTTPS请求
   - 检查响应状态码、头部信息、服务器类型等
   - 支持非标准端口的Web服务发现
   - 只有真正响应HTTP请求的服务才被认定为Web服务

3. **级联测试阶段**:
   - 对已验证的Web服务执行完整的web_pentest工作流
   - 生成详细的Web安全测试报告

**智能跳过情况**:
- 端口过滤: `101.35.247.212 -> [22,53,111]` - 全是明显非Web端口 → 跳过 ✓
- HTTP验证: `192.168.1.1 -> [8080,9000]` - 端口开放但无HTTP响应 → 跳过 ✓
- 级联测试: 验证阶段未发现Web服务 → 跳过 ✓

**成功执行示例**:
- `web.example.com -> [22,80,443,3306]` → 验证80,443端口 → 发现Web服务 → 级联测试执行 ✓
- `app.company.com -> [22,8080,9000]` → 验证8080,9000端口 → 发现非标准Web服务 → 级联测试执行 ✓