id: mfa-console-password-disabled

info:
  name: MFA For RAM Users With Console Password - Disabled
  author: DhiyaneshDK
  severity: medium
  description: |
    MFA (Multi-Factor Authentication) for RAM users with console password is currently disabled, meaning users can access the console without requiring a second form of authentication. This configuration reduces security by not enforcing an additional layer of protection beyond the password.
  reference:
    - https://www.alibabacloud.com/help/en/ram/user-guide/bind-an-mfa-device-to-a-ram-user
    - https://www.trendmicro.com/cloudoneconformity/knowledge-base/alibaba-cloud/AlibabaCloud-RAM/ram-user-multi-factor-authentication-enabled.html
  metadata:
    max-request: 1
    verified: true
  tags: cloud,devops,aliyun,alibaba,alibaba-cloud-config,alibaba-ram

variables:
  region: "cn-hangzhou"

flow: |
  code(1)
  for(let UserName of iterate(template.username)){
    set("user", UserName)
    code(2)
  }

self-contained: true

code:
  - engine:
      - sh
      - bash

    source: |
      aliyun ram ListUsers --region $region

    extractors:
      - type: json
        name: username
        internal: true
        json:
          - '.Users.User[].UserName'

  - engine:
      - sh
      - bash

    source: |
      aliyun ram GetLoginProfile --UserName $user --region $region

    matchers:
      - type: word
        words:
          - '"MFABindRequired": false'

    extractors:
      - type: dsl
        dsl:
          - 'username + "MFA For RAM Users With Console Password is Disabled "'
# digest: 4b0a00483046022100c7afcb5e2dc6dc985fe7cdacc0c4b9df46771b260e4d9c285aebad82f799253f02210085b33d9186b18e92bbb5cb344b1fe586afee9a15e6ff8f3403dbf74815ff6a48:922c64590222798bb761d5b6d8e72950