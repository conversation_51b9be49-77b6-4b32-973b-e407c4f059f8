id: max-password-retry-disabled

info:
  name: Maximum Password Retry Constraint Policy - Disabled
  author: DhiyaneshDK
  severity: medium
  description: |
    The password retry constraint policy for RAM (Resource Access Management) users in Alibaba Cloud is either disabled or not properly configured
  reference:
    - https://www.alibabacloud.com/help/en/ram/user-guide/configure-a-password-policy-for-ram-users
  metadata:
    max-request: 1
    verified: true
  tags: cloud,devops,aliyun,alibaba,alibaba-cloud-config,alibaba-ram

variables:
  region: "cn-hangzhou"

self-contained: true

code:
  - engine:
      - sh
      - bash
    source: |
      aliyun ram GetPasswordPolicy --region $region

    matchers:
      - type: word
        name: policy
        words:
          - '"MaxLoginAttemps": 0'

    extractors:
      - type: dsl
        dsl:
          - '"Maximum Password Retry Constraint Policy is Disabled "'
# digest: 4a0a00473045022100f3433183b0212970a977499997f3e2c24d64c9450e10fd8b08ee4c813be0bb5c02202782c8d0ddf2c2bd5f715a11f3561d0635cb7d5994439495302e4b5936e38054:922c64590222798bb761d5b6d8e72950