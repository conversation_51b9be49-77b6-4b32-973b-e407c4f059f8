id: password-policy-length-unconfigured

info:
  name: RAM Password Policy requires Minimum Length 14 or Greater
  author: DhiyaneshDK
  severity: medium
  description: |
    The Alibaba RAM password policy is not configured to require a minimum length of 14 characters. This misconfiguration allows users to set shorter, potentially weaker passwords, increasing the risk of brute-force attacks.
  reference:
    - https://www.alibabacloud.com/help/en/ram/user-guide/configure-a-password-policy-for-ram-users
    - https://www.trendmicro.com/cloudoneconformity/knowledge-base/alibaba-cloud/AlibabaCloud-RAM/require-14-characters-password-policy.html
  metadata:
    max-request: 1
    verified: true
  tags: cloud,devops,aliyun,alibaba,alibaba-cloud-config,alibaba-ram

variables:
  region: "cn-hangzhou"

self-contained: true

code:
  - engine:
      - sh
      - bash

    source: |
      aliyun ram GetPasswordPolicy --region $region


    matchers-condition: and
    matchers:
      - type: regex
        regex:
          - \"MinimumPasswordLength\":\s*(1[4-9]|[2-9][0-9]{1,})
        negative: true

      - type: regex
        regex:
          - \"MinimumPasswordLength":\s*(\d|1[0-3])

    extractors:
      - type: dsl
        dsl:
          - '"RAM Password Policy Does not contain Minimum Length "'
# digest: 490a0046304402205d4196e02a5a3b0a4f7a8387344582cb697cd529c94f8b327a7b9c08de4945b002201f64123e410f44a14463f33f9b2bdd28b5c386eaa43fc7db3bf60ba2cf678ecf:922c64590222798bb761d5b6d8e72950