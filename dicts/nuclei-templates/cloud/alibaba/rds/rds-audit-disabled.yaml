id: rds-audit-disabled

info:
  name: RDS Database Instances - SQL Auditing Disabled
  author: DhiyaneshDK
  severity: high
  description: |
    SQL auditing is disabled on the RDS Database instances, meaning activities such as user queries and connection events are not logged. This may hinder the ability to track database activity, detect suspicious behavior, and comply with security auditing requirements.
  reference:
    - https://www.alibabacloud.com/help/en/rds/apsaradb-rds-for-mysql/use-the-sql-explorer-and-audit-feature-on-an-apsaradb-rds-for-mysql-instance
    - https://www.trendmicro.com/cloudoneconformity/knowledge-base/alibaba-cloud/AlibabaCloud-RDS/enable-audit-logs.html
  metadata:
    max-request: 2
    verified: true
  tags: cloud,devops,aliyun,alibaba,alibaba-cloud-config,alibaba-rds

variables:
  region: "cn-hangzhou"

flow: |
  code(1)
  for(let DBInstanceId of iterate(template.dbinstanceid)){
    set("dbinstanceid", DBInstanceId)
    code(2)
  }

self-contained: true

code:
  - engine:
      - sh
      - bash
    source: |
      aliyun rds DescribeDBInstances --region $region

    extractors:
      - type: json
        name: dbinstanceid
        internal: true
        json:
          - '.Items.DBInstance[].DBInstanceId'

  - engine:
      - sh
      - bash

    source: |
      aliyun rds DescribeSQLCollectorPolicy --DBInstanceId $dbinstanceid --region $region

    matchers:
      - type: word
        words:
          - '"SQLCollectorStatus": "Disabled"'

    extractors:
      - type: dsl
        dsl:
          - 'dbinstanceid + " RDS Database Instances SQL Auditing Disabled "'
# digest: 490a00463044022028cdca0356b150bd29ac95327a5eb276331cde830eff7be2feec1c94f668f18902204652c90b1da9d39f6bc71d842c3e4ceaabbf28b4bf0c39a813baa1f523fa18cb:922c64590222798bb761d5b6d8e72950