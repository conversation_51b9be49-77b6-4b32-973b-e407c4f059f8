id: ack-cluster-api-public

info:
  name: Public Access to ACK Cluster's API Server - Enabled
  author: ritikchaddha
  severity: high
  description: |
    Ensure that your ACK cluster's API server is not publicly accessible in order to avoid exposing private data and minimizing security risks. The level of access to your Kubernetes API server depends on your application use cases, however, for most use cases, the Kubernetes API Server should be accessible only from within your Virtual Private Cloud (VPC).
  reference:
    - https://www.trendmicro.com/cloudoneconformity/knowledge-base/alibaba-cloud/AlibabaCloud-ACK/private-cluster.html
  metadata:
    max-request: 1
    verified: true
  tags: cloud,devops,aliyun,alibaba,aliyun-cloud-config,ack

variables:
  region: "cn-hangzhou"

self-contained: true

code:
  - engine:
      - sh
      - bash
    source: |
      aliyun cs GET /clusters --header "Content-Type=application/json;" --body "{}"

    matchers-condition: and
    matchers:
      - type: word
        words:
          - 'master_url":'
          - 'api_server_endpoint\":\"'

      - type: word
        words:
          - 'api_server_endpoint\":\"\",'
        negative: true
# digest: 4b0a00483046022100b7fc429fd01243451efc9c2af29a4f7dd9fa7d3d472f6b2390b237f52c8d29bd022100e7e94a2b747bfaf80f269494c33d26ad15655c94116df0673fc9002d46206901:922c64590222798bb761d5b6d8e72950