id: generic-weak-login

info:
  name: <PERSON><PERSON> Weak Login Credentials
  author: websec-team
  severity: high
  description: Generic weak login credentials were discovered. This template tests common username and password combinations across various web applications.
  reference:
    - https://github.com/danie<PERSON><PERSON>sler/SecLists/blob/master/Passwords/Default-Credentials/default-passwords.csv
    - https://cirt.net/passwords
  classification:
    cvss-metrics: CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:C/C:L/I:L/A:L
    cvss-score: 8.3
    cwe-id: CWE-522
  metadata:
    verified: true
    max-request: 200
  tags: generic,weak-login,default-login

http:
  - raw:
      - |
        POST /login HTTP/1.1
        Host: {{Hostname}}
        Content-Type: application/x-www-form-urlencoded
        Accept: text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8

        username={{username}}&password={{password}}
      - |
        POST /admin/login HTTP/1.1
        Host: {{Hostname}}
        Content-Type: application/x-www-form-urlencoded
        Accept: text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8

        username={{username}}&password={{password}}
      - |
        POST /auth/login HTTP/1.1
        Host: {{Hostname}}
        Content-Type: application/json
        Accept: application/json

        {"username":"{{username}}","password":"{{password}}"}

    attack: clusterbomb
    payloads:
      username:
        - admin
        - administrator
        - root
        - user
        - guest
        - test
        - demo
        - operator
        - manager
        - service
        - support
        - system
        - webmaster
        - sa
        - dba
        - postgres
        - mysql
        - oracle
        - tomcat
        - jenkins
        - gitlab
        - git
        - ftp
        - www
        - web
        - api
        - app
        - dev
        - prod
        - staging
        - backup
        - monitor
        - nagios
        - zabbix
        - elastic
        - kibana
        - grafana
        - prometheus
        - consul
        - vault
        - etcd
        - redis
        - mongo
        - cassandra
        - influx
        - splunk
        - logstash
        - fluentd
        - kafka
        - rabbitmq
        - activemq
        - nginx
        - apache
        - httpd
        - traefik
        - haproxy
        - docker
        - kubernetes
        - k8s
        - openshift
        - rancher
        - portainer
        - harbor
        - nexus
        - artifactory
        - sonar
        - jira
        - confluence
        - bamboo
        - bitbucket
        - github
        - gitlab-ci
        - jenkins-ci
        - teamcity
        - octopus
        - ansible
        - puppet
        - chef
        - terraform
        - vagrant
        - packer
        - consul-template
        - nomad
        - boundary
        - waypoint
      password:
        - admin
        - password
        - 123456
        - 12345678
        - 123456789
        - qwerty
        - abc123
        - Password1
        - password123
        - admin123
        - root123
        - user123
        - guest123
        - test123
        - demo123
        - changeme
        - change_me
        - default
        - secret
        - pass
        - passwd
        - pwd
        - login
        - welcome
        - hello
        - temp
        - temporary
        - letmein
        - monkey
        - dragon
        - master
        - shadow
        - superman
        - michael
        - jennifer
        - jordan
        - michelle
        - daniel
        - christopher
        - anthony
        - william
        - matthew
        - andrew
        - joshua
        - david
        - james
        - robert
        - john
        - joseph
        - thomas
        - charles
        - mary
        - patricia
        - linda
        - barbara
        - elizabeth
        - jennifer
        - maria
        - susan
        - margaret
        - dorothy
        - lisa
        - nancy
        - karen
        - betty
        - helen
        - sandra
        - donna
        - carol
        - ruth
        - sharon
        - michelle
        - laura
        - sarah
        - kimberly
        - deborah
        - jessica
        - shirley
        - cynthia
        - angela
        - melissa
        - brenda
        - emma
        - olivia
        - ava
        - isabella
        - sophia
        - charlotte
        - mia
        - amelia
        - harper
        - evelyn
        - abigail
        - emily
        - ella
        - elizabeth
        - camila
        - luna
        - sofia
        - avery
        - mila
        - aria
        - scarlett
        - penelope
        - layla
        - chloe
        - victoria
        - madison
        - eleanor
        - grace
        - nora
        - riley
        - zoey
        - hannah
        - hazel
        - lily
        - ellie
        - violet
        - lillian
        - zoe
        - stella
        - aurora
        - natalie
        - emilia
        - everly
        - leah
        - aubrey
        - willow
        - addison
        - lucy
        - audrey
        - bella
        - nova
        - brooklyn
        - alice
        - ""

    matchers-condition: or
    matchers:
      - type: dsl
        dsl:
          - 'status_code_1 == 302'
          - 'status_code_2 == 302'
          - 'status_code_3 == 200 && contains(body_3, "token")'
        condition: or

      - type: word
        part: body
        words:
          - "dashboard"
          - "welcome"
          - "logout"
          - "profile"
          - "settings"
          - "administration"
          - "admin panel"
          - "control panel"
        condition: or
        case-insensitive: true

      - type: word
        part: header
        words:
          - "Set-Cookie:"
          - "Location: /"
          - "Location: /dashboard"
          - "Location: /admin"
          - "Location: /home"
        condition: or
