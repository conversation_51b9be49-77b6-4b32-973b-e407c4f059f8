name: dateease-multi-passwords
description: "DateEase多种常见账号密码组合"
method: POST
headers:
  Content-Type: "application/json"
path:
  - /api/auth/login
bodies:
  # admin/dataease (加密格式)
  - '{"username":"g24DUpuiuhas+dTXGTjqFQAZiIEVLgQEJR31oS8smIWqsKx7QbfAEfTUCfSqt0NXZx9NPMYSHWfefPZ+k3mLYg==","password":"PBFg6uoRZ7hO2oKw4MRGYsY/nTT91qktzawbfyTNg8Fpy/CiugLf59Uf0sanY85udQTmHKbxuiZoCVIgV5d9Uw==","loginType":0}'
  # 尝试明文格式
  - '{"username":"admin","password":"dataease","loginType":0}'
  - '{"username":"admin","password":"admin","loginType":0}'
  - '{"username":"admin","password":"123456","loginType":0}'
  - '{"username":"admin","password":"password","loginType":0}'
  - '{"username":"root","password":"root","loginType":0}'
expression:
  status: 200
  body_any:
    - "true"