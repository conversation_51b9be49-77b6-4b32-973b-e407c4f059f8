name: tomcat-multi-passwords
description: "Tomcat Manager多种常见账号密码组合"
method: GET
path:
  - /manager/html
  - /host-manager/html
headers_list:
  - Authorization: "Basic dG9tY2F0OnRvbWNhdA=="      # tomcat:tomcat
  - Authorization: "Basic YWRtaW46YWRtaW4="          # admin:admin
  - Authorization: "Basic YWRtaW46MTIzNDU2"          # admin:123456
  - Authorization: "Basic YWRtaW46cGFzc3dvcmQ="      # admin:password
  - Authorization: "Basic YWRtaW46dG9tY2F0"          # admin:tomcat
  - Authorization: "Basic cm9vdDpyb290"              # root:root
  - Authorization: "Basic cm9vdDoxMjM0NTY="          # root:123456
  - Authorization: "Basic bWFuYWdlcjptYW5hZ2Vy"      # manager:manager
  - Authorization: "Basic dXNlcjp1c2Vy"              # user:user
  - Authorization: "Basic dGVzdDp0ZXN0"              # test:test
expression:
  status: 200
  body_all:
    - "Tomcat Web Application Manager"
