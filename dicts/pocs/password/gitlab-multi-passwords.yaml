name: gitlab-multi-passwords
description: "GitLab多种常见账号密码组合"
method: POST
headers:
  Content-Type: "application/x-www-form-urlencoded"
path:
  - /users/sign_in
  - /api/v4/session
bodies:
  - "user[login]=root&user[password]=123456&user[remember_me]=0"
  - "user[login]=root&user[password]=admin&user[remember_me]=0"
  - "user[login]=root&user[password]=password&user[remember_me]=0"
  - "user[login]=root&user[password]=gitlab&user[remember_me]=0"
  - "user[login]=admin&user[password]=admin&user[remember_me]=0"
  - "user[login]=admin&user[password]=123456&user[remember_me]=0"
  - "user[login]=admin&user[password]=password&user[remember_me]=0"
  - "user[login]=gitlab&user[password]=gitlab&user[remember_me]=0"
  - "user[login]=user&user[password]=user&user[remember_me]=0"
  - "user[login]=test&user[password]=test&user[remember_me]=0"
expression:
  status: 302
  body_any:
    - "dashboard"
    - "projects"
