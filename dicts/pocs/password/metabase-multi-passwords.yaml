name: metabase-multi-passwords
description: "Metabase多种常见账号密码组合"
method: POST
headers:
  Content-Type: "application/json"
path:
  - /api/session
bodies:
  - '{"username":"<EMAIL>","password":"admin"}'
  - '{"username":"<EMAIL>","password":"123456"}'
  - '{"username":"<EMAIL>","password":"password"}'
  - '{"username":"<EMAIL>","password":"metabase"}'
  - '{"username":"admin","password":"admin"}'
  - '{"username":"admin","password":"123456"}'
  - '{"username":"admin","password":"password"}'
  - '{"username":"admin","password":"metabase"}'
  - '{"username":"user","password":"user"}'
  - '{"username":"test","password":"test"}'
expression:
  status: 200
  body_any:
    - "id"
    - "email"
    - "first_name"
