name: elasticsearch-multi-passwords
description: "Elasticsearch多种常见账号密码组合"
method: POST
headers:
  Content-Type: "application/json"
path:
  - /_security/user/_authenticate
  - /_xpack/security/user/_authenticate
bodies:
  - '{"username":"elastic","password":"elastic"}'
  - '{"username":"elastic","password":"123456"}'
  - '{"username":"elastic","password":"admin"}'
  - '{"username":"elastic","password":"password"}'
  - '{"username":"elastic","password":"changeme"}'
  - '{"username":"admin","password":"admin"}'
  - '{"username":"admin","password":"123456"}'
  - '{"username":"kibana","password":"kibana"}'
  - '{"username":"logstash","password":"logstash"}'
  - '{"username":"beats","password":"beats"}'
expression:
  status: 200
  body_any:
    - "username"
    - "roles"
