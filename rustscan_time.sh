#!/bin/bash

# RustScan 执行时长计算脚本
# 用法: ./rustscan_timer.sh

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 检查rustscan是否存在
if [ ! -f "./bin/darwin/arm64/rustscan" ]; then
    echo -e "${RED}错误: ./bin/rustscan 文件不存在${NC}"
    exit 1
fi

# 扫描参数
TARGET="101.35.247.212"
TIMEOUT="4000"
BATCH_SIZE="1000"
PORT_RANGE="1-65535"
SCAN_ORDER="random"
TRIES="3"

# 构建完整命令
RUSTSCAN_CMD="./bin/darwin/arm64/rustscan -a $TARGET -t $TIMEOUT -b $BATCH_SIZE -r $PORT_RANGE --scan-order $SCAN_ORDER --tries $TRIES -g"

echo -e "${BLUE}=== RustScan 时长计算器 ===${NC}"
echo -e "${YELLOW}目标: $TARGET${NC}"
echo -e "${YELLOW}参数: -t $TIMEOUT -b $BATCH_SIZE -r $PORT_RANGE --scan-order $SCAN_ORDER --tries $TRIES -g${NC}"
echo

# 记录开始时间
echo -e "${GREEN}开始时间: $(date '+%Y-%m-%d %H:%M:%S')${NC}"
START_TIME=$(date +%s)
START_TIME_NS=$(date +%s%N)

echo -e "${BLUE}正在执行扫描...${NC}"
echo "命令: $RUSTSCAN_CMD"
echo

# 执行扫描命令
eval $RUSTSCAN_CMD

# 记录结束时间
END_TIME=$(date +%s)
END_TIME_NS=$(date +%s%N)

echo
echo -e "${GREEN}结束时间: $(date '+%Y-%m-%d %H:%M:%S')${NC}"

# 计算时长
DURATION_SECONDS=$((END_TIME - START_TIME))
DURATION_NS=$((END_TIME_NS - START_TIME_NS))
DURATION_MS=$((DURATION_NS / 1000000))

# 时间格式化
HOURS=$((DURATION_SECONDS / 3600))
MINUTES=$(((DURATION_SECONDS % 3600) / 60))
SECONDS=$((DURATION_SECONDS % 60))

echo
echo -e "${BLUE}=== 扫描完成 ===${NC}"
echo -e "${GREEN}总耗时: ${DURATION_SECONDS}秒 (${DURATION_MS}毫秒)${NC}"

if [ $HOURS -gt 0 ]; then
    echo -e "${GREEN}格式化时间: ${HOURS}小时 ${MINUTES}分钟 ${SECONDS}秒${NC}"
elif [ $MINUTES -gt 0 ]; then
    echo -e "${GREEN}格式化时间: ${MINUTES}分钟 ${SECONDS}秒${NC}"
else
    echo -e "${GREEN}格式化时间: ${SECONDS}秒${NC}"
fi

# 计算平均每端口扫描时间
TOTAL_PORTS=65535
AVG_TIME_PER_PORT=$(echo "scale=6; $DURATION_SECONDS / $TOTAL_PORTS" | bc -l 2>/dev/null || echo "计算失败")

if [ "$AVG_TIME_PER_PORT" != "计算失败" ]; then
    echo -e "${YELLOW}平均每端口扫描时间: ${AVG_TIME_PER_PORT}秒${NC}"
fi

# 保存结果到日志文件
LOG_FILE="rustscan_timing.log"
echo "$(date '+%Y-%m-%d %H:%M:%S') - Target: $TARGET - Duration: ${DURATION_SECONDS}s (${DURATION_MS}ms)" >> $LOG_FILE
echo -e "${BLUE}结果已保存到: $LOG_FILE${NC}"

echo
echo -e "${GREEN}扫描统计:${NC}"
echo -e "  目标地址: $TARGET"
echo -e "  端口范围: $PORT_RANGE (共$TOTAL_PORTS个端口)"
echo -e "  并发数: $BATCH_SIZE"
echo -e "  超时时间: ${TIMEOUT}ms"
echo -e "  重试次数: $TRIES"
echo -e "  扫描顺序: $SCAN_ORDER"