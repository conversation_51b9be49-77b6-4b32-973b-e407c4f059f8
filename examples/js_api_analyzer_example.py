#!/usr/bin/env python3
"""
JS API Analyzer MCP Tool Usage Example

This script demonstrates how to use the JS API analyzer tool through the MCP interface.
It provides examples of basic and advanced usage patterns.
"""

import asyncio
import json
import sys
from pathlib import Path

# Add the parent directory to the path so we can import from WebSec
sys.path.insert(0, str(Path(__file__).parent.parent))

from tools.js_api_analyzer import get_valid_apis_only

async def basic_example():
    """Basic usage example of the JS API analyzer"""
    print("=== Basic JS API Analyzer Example ===")
    print()

    # Target website for analysis
    target = "https://httpbin.org"

    print(f"Analyzing JavaScript APIs for: {target}")
    print("Using default settings...")

    try:
        # Call the analyzer with basic parameters
        result = await get_valid_apis_only(
            target=target,
            timeout=15,
            max_depth=2
        )

        # Parse and display results
        data = json.loads(result)

        print(f"\n📊 Analysis Results:")
        print(f"   Target: {data.get('target')}")
        print(f"   Scan Time: {data.get('scan_time')}")
        print(f"   Total APIs Discovered: {data.get('total_discovered', 0)}")
        print(f"   Valid APIs: {data.get('valid_count', 0)}")
        print(f"   Filtered Out: {data.get('filtered_count', 0)}")

        valid_apis = data.get('valid_apis', [])
        if valid_apis:
            print(f"\n🎯 Valid APIs Found:")
            for i, api in enumerate(valid_apis, 1):
                print(f"   {i}. {api}")
        else:
            print("\n⚠️  No valid APIs found")

    except Exception as e:
        print(f"❌ Error: {e}")

async def advanced_example():
    """Advanced usage example with authentication and custom settings"""
    print("\n=== Advanced JS API Analyzer Example ===")
    print()

    # Target website for analysis
    target = "https://jsonplaceholder.typicode.com"

    # Authentication headers (example)
    auth_headers = {
        "User-Agent": "WebSec-JS-Analyzer/1.0",
        "Accept": "application/json",
        # "Authorization": "Bearer your-token-here"  # Add real token if needed
    }

    print(f"Analyzing JavaScript APIs for: {target}")
    print("Using advanced settings with custom headers...")

    try:
        # Call the analyzer with advanced parameters
        result = await get_valid_apis_only(
            target=target,
            timeout=30,
            max_depth=3,
            auth_headers=auth_headers,
            max_workers=15,
            include_404=True,  # Include 404 responses
            verbose=True
        )

        # Parse and display results
        data = json.loads(result)

        print(f"\n📊 Advanced Analysis Results:")
        print(f"   Target: {data.get('target')}")
        print(f"   Scan Time: {data.get('scan_time')}")
        print(f"   Total APIs Discovered: {data.get('total_discovered', 0)}")
        print(f"   Valid APIs: {data.get('valid_count', 0)}")
        print(f"   Filtered Out: {data.get('filtered_count', 0)}")

        valid_apis = data.get('valid_apis', [])
        if valid_apis:
            print(f"\n🎯 Valid APIs Found (including 404s):")
            for i, api in enumerate(valid_apis, 1):
                print(f"   {i}. {api}")

            # Group by status codes
            status_groups = {}
            for api in valid_apis:
                # Extract status code from the API string
                parts = api.split('(')
                if len(parts) > 1:
                    status = parts[-1].rstrip(')')
                    if status not in status_groups:
                        status_groups[status] = []
                    status_groups[status].append(api)

            print(f"\n📈 Status Code Distribution:")
            for status, apis in sorted(status_groups.items()):
                print(f"   {status}: {len(apis)} APIs")

        else:
            print("\n⚠️  No valid APIs found")

    except Exception as e:
        print(f"❌ Error: {e}")

async def multiple_targets_example():
    """Example of analyzing multiple targets"""
    print("\n=== Multiple Targets Example ===")
    print()

    # List of targets to analyze
    targets = [
        "https://httpbin.org",
        "https://jsonplaceholder.typicode.com",
        "https://reqres.in"
    ]

    results = []

    for target in targets:
        print(f"Analyzing: {target}")

        try:
            result = await get_valid_apis_only(
                target=target,
                timeout=20,
                max_depth=2,
                verbose=False
            )

            data = json.loads(result)
            results.append(data)

            print(f"  ✓ Found {data.get('valid_count', 0)} valid APIs")

        except Exception as e:
            print(f"  ❌ Error: {e}")
            continue

    # Summary of all results
    print(f"\n📊 Multi-Target Analysis Summary:")
    total_valid = sum(r.get('valid_count', 0) for r in results)
    total_discovered = sum(r.get('total_discovered', 0) for r in results)

    print(f"   Targets Analyzed: {len(results)}")
    print(f"   Total APIs Discovered: {total_discovered}")
    print(f"   Total Valid APIs: {total_valid}")

    for result in results:
        print(f"   {result.get('target')}: {result.get('valid_count', 0)} valid APIs")

async def error_handling_example():
    """Example of error handling and edge cases"""
    print("\n=== Error Handling Example ===")
    print()

    # Test with various problematic inputs
    test_cases = [
        {
            "name": "Invalid URL",
            "target": "not-a-valid-url",
            "should_fail": True
        },
        {
            "name": "Non-existent domain",
            "target": "https://this-domain-does-not-exist-12345.com",
            "should_fail": True
        },
        {
            "name": "Valid but empty site",
            "target": "https://example.com",
            "should_fail": False
        }
    ]

    for test_case in test_cases:
        print(f"Testing: {test_case['name']}")
        print(f"  Target: {test_case['target']}")

        try:
            result = await get_valid_apis_only(
                target=test_case['target'],
                timeout=10,
                max_depth=1
            )

            data = json.loads(result)

            if 'error' in data:
                print(f"  ❌ Expected error: {data['error']}")
            else:
                print(f"  ✓ Success: Found {data.get('valid_count', 0)} valid APIs")

        except Exception as e:
            if test_case['should_fail']:
                print(f"  ✓ Expected error: {e}")
            else:
                print(f"  ❌ Unexpected error: {e}")

async def main():
    """Main function to run all examples"""
    print("🚀 JS API Analyzer MCP Tool Examples")
    print("="*50)

    # Run all examples
    await basic_example()
    await advanced_example()
    await multiple_targets_example()
    await error_handling_example()

    print("\n" + "="*50)
    print("✅ All examples completed!")
    print("\n💡 Tips for using the JS API Analyzer:")
    print("   • Use authentication headers for protected sites")
    print("   • Adjust max_depth based on site complexity")
    print("   • Set include_404=True to see all discovered endpoints")
    print("   • Use verbose=True for detailed debugging information")
    print("   • Increase timeout for slow or large sites")
    print("   • Monitor max_workers to balance speed vs resource usage")

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n⚠️  Examples interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 Unexpected error: {e}")
        sys.exit(1)
