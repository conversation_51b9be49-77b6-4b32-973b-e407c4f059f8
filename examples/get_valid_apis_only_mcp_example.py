#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
get_valid_apis_only MCP工具使用示例

这个示例展示了如何使用已注册的get_valid_apis_only MCP工具
"""

import asyncio
import json
import sys
import os

# 添加父目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from server import WebSecMCP

async def test_get_valid_apis_only_mcp_tool():
    """测试get_valid_apis_only MCP工具"""
    print("🚀 Testing get_valid_apis_only MCP tool...")
    
    # 初始化MCP服务器
    server = WebSecMCP()
    server.register_tools()
    
    # 模拟MCP工具调用
    # 在实际使用中，这些参数会通过MCP协议传递
    test_cases = [
        {
            "name": "Basic Test",
            "params": {
                "target": "https://httpbin.org",
                "timeout": 15,
                "max_depth": 1,
                "verbose": True
            }
        },
        {
            "name": "Advanced Test with Auth Headers",
            "params": {
                "target": "https://httpbin.org",
                "timeout": 20,
                "max_depth": 2,
                "auth_headers": '{"User-Agent": "Mozilla/5.0 Custom"}',
                "max_workers": 8,
                "include_404": True,
                "verbose": True
            }
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n📋 Test Case {i}: {test_case['name']}")
        print(f"📝 Parameters: {json.dumps(test_case['params'], indent=2)}")
        
        try:
            # 这里我们直接调用函数来模拟MCP工具调用
            # 在实际MCP环境中，这会通过MCP协议自动处理
            sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
            from tools.js_api_analyzer import get_valid_apis_only
            
            params = test_case['params']
            auth_headers = None
            if 'auth_headers' in params:
                auth_headers = json.loads(params['auth_headers'])
            
            result = await get_valid_apis_only(
                target=params['target'],
                timeout=params.get('timeout', 10),
                max_depth=params.get('max_depth', 2),
                auth_headers=auth_headers,
                max_workers=params.get('max_workers', 10),
                include_404=params.get('include_404', False),
                verbose=params.get('verbose', False)
            )
            
            # 解析结果
            data = json.loads(result)
            print(f"✅ Test successful!")
            print(f"🎯 Target: {data.get('target')}")
            print(f"⏱️ Scan time: {data.get('scan_time')}")
            print(f"📊 Total discovered: {data.get('total_discovered', 0)}")
            print(f"✅ Valid APIs: {data.get('valid_count', 0)}")
            print(f"❌ Filtered out: {data.get('filtered_count', 0)}")
            
            # 显示发现的API
            valid_apis = data.get('valid_apis', [])
            if valid_apis:
                print(f"🔍 Found APIs:")
                for j, api in enumerate(valid_apis[:5], 1):
                    print(f"   {j}. {api}")
                if len(valid_apis) > 5:
                    print(f"   ... and {len(valid_apis) - 5} more")
            else:
                print("⚠️ No valid APIs found")
                
        except Exception as e:
            print(f"❌ Test failed: {e}")
            import traceback
            traceback.print_exc()

def show_mcp_tool_info():
    """显示MCP工具信息"""
    print("\n📖 MCP Tool Information:")
    print("=" * 50)
    print("Tool Name: get_valid_apis_only_wrapper")
    print("Category: Web Tools")
    print("Description: JavaScript API Analyzer - Valid APIs Only")
    print("\nRequired Parameters:")
    print("  - target: Target URL with protocol (string)")
    print("\nOptional Parameters:")
    print("  - timeout: Request timeout in seconds (int, default: 10)")
    print("  - max_depth: Maximum crawling depth (int, default: 2)")
    print("  - auth_headers: Authentication headers in JSON format (string)")
    print("  - max_workers: Maximum concurrent workers (int, default: 10)")
    print("  - include_404: Include 404 status code APIs (bool, default: False)")
    print("  - verbose: Enable verbose output (bool, default: False)")
    print("\nExample Usage in MCP Client:")
    print("""
{
  "tool": "get_valid_apis_only_wrapper",
  "arguments": {
    "target": "https://example.com",
    "timeout": 30,
    "max_depth": 3,
    "auth_headers": "{\\"Authorization\\": \\"Bearer token\\"}",
    "verbose": true
  }
}
""")

async def main():
    """主函数"""
    print("🎯 get_valid_apis_only MCP Tool Example")
    print("=" * 50)
    
    # 显示工具信息
    show_mcp_tool_info()
    
    # 测试MCP工具
    await test_get_valid_apis_only_mcp_tool()
    
    print("\n🎉 Example completed!")
    print("\n💡 Tips:")
    print("  - Use specific target URLs for better results")
    print("  - Adjust max_depth based on site complexity")
    print("  - Use auth_headers for authenticated endpoints")
    print("  - Enable verbose mode for debugging")

if __name__ == "__main__":
    asyncio.run(main())
