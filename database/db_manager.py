# Encoding: utf-8
"""
数据库管理模块
用于管理 PostgreSQL 数据库连接和 MCP 工具调用记录存储
"""

import asyncio
import asyncpg
import psycopg2
from psycopg2 import pool
import json
import uuid
import time
import logging
from typing import Optional, Dict, Any, List
from datetime import datetime, timezone
from contextlib import asynccontextmanager
import traceback

logger = logging.getLogger(__name__)


class DatabaseManager:
    """数据库管理器 - 处理 PostgreSQL 连接和 MCP 调用记录"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化数据库管理器
        
        Args:
            config: 数据库配置字典，包含 host, port, user, password, name 等
        """
        self.config = config
        self.connection_pool = None
        self.async_pool = None
        self._initialized = False
        
    async def initialize(self):
        """初始化数据库连接池"""
        if self._initialized:
            return
            
        try:
            # 创建异步连接池
            self.async_pool = await asyncpg.create_pool(
                host=self.config['host'],
                port=self.config['port'],
                user=self.config['user'],
                password=self.config['password'],
                database=self.config['name'],
                min_size=self.config.get('min_size', 2),
                max_size=self.config.get('max_size', 10),
                command_timeout=30,
                server_settings={
                    'application_name': 'websec_mcp',
                    'tcp_keepalives_idle': '600',
                    'tcp_keepalives_interval': '30',
                    'tcp_keepalives_count': '3'
                }
            )
            
            # 创建同步连接池（备用）
            self.connection_pool = psycopg2.pool.ThreadedConnectionPool(
                minconn=self.config.get('min_size', 5),
                maxconn=self.config.get('max_size', 20),
                host=self.config['host'],
                port=self.config['port'],
                user=self.config['user'],
                password=self.config['password'],
                database=self.config['name']
            )
            
            self._initialized = True
            logger.info("Database connection pools initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize database: {e}")
            raise
    
    async def close(self):
        """关闭数据库连接池"""
        if self.async_pool:
            await self.async_pool.close()
        if self.connection_pool:
            self.connection_pool.closeall()
        self._initialized = False
        logger.info("Database connections closed")
    
    @asynccontextmanager
    async def get_connection(self):
        """获取异步数据库连接的上下文管理器"""
        if not self._initialized:
            await self.initialize()

        # 检查事件循环状态
        try:
            loop = asyncio.get_running_loop()
            if loop.is_closed():
                raise RuntimeError("Event loop is closed")
        except RuntimeError:
            raise RuntimeError("No running event loop")

        connection = None
        try:
            # 检查连接池状态
            if self.async_pool is None or self.async_pool._closed:
                raise RuntimeError("Connection pool is closed")

            connection = await asyncio.wait_for(
                self.async_pool.acquire(),
                timeout=5.0
            )

            # 检查连接是否有效
            if connection.is_closed():
                await self.async_pool.release(connection)
                raise RuntimeError("Connection is closed")

            yield connection

        except asyncio.TimeoutError:
            logger.error("Database connection acquisition timeout")
            raise
        except Exception as e:
            logger.error(f"Failed to acquire database connection: {e}")
            raise
        finally:
            if connection and not connection.is_closed():
                try:
                    await asyncio.wait_for(
                        self.async_pool.release(connection),
                        timeout=2.0
                    )
                except Exception as e:
                    logger.error(f"Failed to release database connection: {e}")
    
    async def record_mcp_call_complete(
        self,
        tool_name: str,
        function_name: str,
        parameters: Dict[str, Any],
        result: str,
        status: str = 'success',
        error_message: Optional[str] = None,
        execution_time_ms: Optional[int] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> str:
        """
        一次性记录完整的 MCP 工具调用（开始和结束）

        Args:
            tool_name: 工具名称
            function_name: 函数名称
            parameters: 调用参数
            result: 调用结果
            status: 状态 (success, error, timeout)
            error_message: 错误信息
            execution_time_ms: 执行时间（毫秒）
            metadata: 额外元数据

        Returns:
            call_id: 调用唯一标识符
        """
        call_id = str(uuid.uuid4())

        # 重试机制
        for attempt in range(3):
            try:
                # 检查事件循环和数据库状态
                try:
                    loop = asyncio.get_running_loop()
                    if loop.is_closed():
                        logger.debug("Event loop is closed, cannot record to database")
                        return call_id
                except RuntimeError:
                    logger.debug("No running event loop, cannot record to database")
                    return call_id

                if not self._initialized or self.async_pool is None:
                    logger.debug("Database not initialized, cannot record")
                    return call_id

                async with self.get_connection() as conn:
                    await conn.execute("""
                        INSERT INTO mcp_tool_calls (
                            call_id, tool_name, function_name, call_parameters,
                            call_result, result_size, status, error_message,
                            execution_time_ms, start_time, end_time, metadata
                        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12)
                    """,
                    call_id, tool_name, function_name,
                    json.dumps(parameters), result, len(result) if result else 0,
                    status, error_message, execution_time_ms,
                    datetime.now(timezone.utc), datetime.now(timezone.utc),
                    json.dumps(metadata or {})
                    )

                logger.debug(f"Recorded complete MCP call: {call_id} - {tool_name}.{function_name} ({status})")
                return call_id

            except (RuntimeError, asyncio.TimeoutError) as e:
                logger.debug(f"Database unavailable (attempt {attempt + 1}): {e}")
                return call_id  # 直接返回，不重试
            except Exception as e:
                logger.error(f"Failed to record MCP call (attempt {attempt + 1}): {e}")
                if attempt < 2:  # 不是最后一次尝试
                    await asyncio.sleep(0.1 * (attempt + 1))  # 递增延迟
                    continue
                else:
                    logger.warning(f"All attempts failed to record MCP call for {tool_name}")
                    return call_id  # 返回 call_id 以便后续操作
    

    
    async def get_mcp_call_history(
        self,
        tool_name: Optional[str] = None,
        function_name: Optional[str] = None,
        limit: int = 100
    ) -> List[Dict[str, Any]]:
        """
        获取 MCP 调用历史记录
        
        Args:
            tool_name: 工具名称过滤
            function_name: 函数名称过滤
            limit: 返回记录数限制
            
        Returns:
            调用历史记录列表
        """
        try:
            async with self.get_connection() as conn:
                query = """
                    SELECT call_id, tool_name, function_name, call_parameters,
                           status, execution_time_ms, result_size, 
                           start_time, end_time, error_message
                    FROM mcp_tool_calls
                    WHERE 1=1
                """
                params = []
                param_count = 0
                
                if tool_name:
                    param_count += 1
                    query += f" AND tool_name = ${param_count}"
                    params.append(tool_name)
                    
                if function_name:
                    param_count += 1
                    query += f" AND function_name = ${param_count}"
                    params.append(function_name)
                
                param_count += 1
                query += f" ORDER BY start_time DESC LIMIT ${param_count}"
                params.append(limit)
                
                rows = await conn.fetch(query, *params)
                
                return [dict(row) for row in rows]
                
        except Exception as e:
            logger.error(f"Failed to get MCP call history: {e}")
            return []
    
    async def get_mcp_call_statistics(self) -> Dict[str, Any]:
        """获取 MCP 调用统计信息"""
        try:
            async with self.get_connection() as conn:
                # 总体统计
                total_stats = await conn.fetchrow("""
                    SELECT 
                        COUNT(*) as total_calls,
                        COUNT(CASE WHEN status = 'success' THEN 1 END) as successful_calls,
                        COUNT(CASE WHEN status = 'error' THEN 1 END) as failed_calls,
                        COUNT(CASE WHEN status = 'timeout' THEN 1 END) as timeout_calls,
                        AVG(execution_time_ms) as avg_execution_time_ms,
                        AVG(result_size) as avg_result_size
                    FROM mcp_tool_calls
                """)
                
                # 按工具统计
                tool_stats = await conn.fetch("""
                    SELECT * FROM mcp_call_statistics
                    ORDER BY total_calls DESC
                    LIMIT 20
                """)
                
                return {
                    'total_statistics': dict(total_stats) if total_stats else {},
                    'tool_statistics': [dict(row) for row in tool_stats]
                }
                
        except Exception as e:
            logger.error(f"Failed to get MCP call statistics: {e}")
            return {'total_statistics': {}, 'tool_statistics': []}
    
    async def cleanup_old_records(self, days_to_keep: int = 30) -> int:
        """清理旧的 MCP 调用记录"""
        try:
            async with self.get_connection() as conn:
                result = await conn.fetchval(
                    "SELECT cleanup_old_mcp_calls($1)", days_to_keep
                )
                logger.info(f"Cleaned up {result} old MCP call records")
                return result
                
        except Exception as e:
            logger.error(f"Failed to cleanup old records: {e}")
            return 0


# 全局数据库管理器实例
_db_manager: Optional[DatabaseManager] = None


def get_db_manager(config: Optional[Dict[str, Any]] = None) -> DatabaseManager:
    """获取全局数据库管理器实例"""
    global _db_manager
    
    if _db_manager is None:
        if config is None:
            raise ValueError("Database config required for first initialization")
        _db_manager = DatabaseManager(config)
    
    return _db_manager


async def initialize_database(config: Dict[str, Any]):
    """初始化数据库连接"""
    db_manager = get_db_manager(config)
    await db_manager.initialize()


async def close_database():
    """关闭数据库连接"""
    global _db_manager
    if _db_manager:
        await _db_manager.close()
        _db_manager = None
