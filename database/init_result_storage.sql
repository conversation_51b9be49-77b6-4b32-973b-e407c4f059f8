-- WebSec MCP 结果存储数据库初始化脚本
-- 用于存储完整的扫描结果，解决上下文长度限制问题

-- 创建数据库（如果需要）
-- CREATE DATABASE websec_mcp;

-- 使用数据库
-- \c websec_mcp;

-- 创建枚举类型
CREATE TYPE result_type_enum AS ENUM (
    'vulnerability_scan',
    'port_scan',
    'subdomain_enum',
    'web_scan',
    'service_enum',
    'network_scan',
    'crack_attempt',
    'general'
);

CREATE TYPE tool_status_enum AS ENUM (
    'running',
    'completed',
    'failed',
    'timeout'
);

-- 1. 工具执行结果表 - 存储完整的原始结果
CREATE TABLE IF NOT EXISTS tool_results (
    id SERIAL PRIMARY KEY,
    result_id VARCHAR(100) UNIQUE NOT NULL,
    tool_name VARCHAR(50) NOT NULL,
    target VARCHAR(500) NOT NULL,
    result_type result_type_enum NOT NULL,
    status tool_status_enum DEFAULT 'completed',
    raw_result TEXT NOT NULL,
    original_size INTEGER NOT NULL,
    execution_time INTEGER, -- 执行时间（秒）
    start_time TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    end_time TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    metadata JSONB DEFAULT '{}',
    -- 索引字段
    target_hash VARCHAR(64) GENERATED ALWAYS AS (encode(sha256(target::bytea), 'hex')) STORED
);

-- 2. 处理后结果表 - 存储压缩和摘要后的结果
CREATE TABLE IF NOT EXISTS processed_results (
    id SERIAL PRIMARY KEY,
    result_id VARCHAR(100) NOT NULL REFERENCES tool_results(result_id) ON DELETE CASCADE,
    summary TEXT NOT NULL,
    key_findings JSONB DEFAULT '[]',
    statistics JSONB DEFAULT '{}',
    sample_data JSONB DEFAULT '[]',
    processed_content TEXT,
    processed_size INTEGER NOT NULL,
    compression_ratio DECIMAL(5,3),
    truncated BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 3. 扫描会话表 - 管理完整的扫描会话
CREATE TABLE IF NOT EXISTS scan_sessions (
    id SERIAL PRIMARY KEY,
    session_id VARCHAR(100) UNIQUE NOT NULL,
    target VARCHAR(500) NOT NULL,
    session_type VARCHAR(50) DEFAULT 'manual',
    status VARCHAR(20) DEFAULT 'active',
    start_time TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    end_time TIMESTAMP WITH TIME ZONE,
    total_tools INTEGER DEFAULT 0,
    completed_tools INTEGER DEFAULT 0,
    failed_tools INTEGER DEFAULT 0,
    metadata JSONB DEFAULT '{}'
);

-- 4. 会话结果关联表
CREATE TABLE IF NOT EXISTS session_results (
    id SERIAL PRIMARY KEY,
    session_id VARCHAR(100) NOT NULL REFERENCES scan_sessions(session_id) ON DELETE CASCADE,
    result_id VARCHAR(100) NOT NULL REFERENCES tool_results(result_id) ON DELETE CASCADE,
    sequence_number INTEGER NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(session_id, result_id)
);

-- 5. 结果统计视图
CREATE OR REPLACE VIEW result_statistics AS
SELECT
    tr.result_type,
    tr.tool_name,
    COUNT(*) as total_executions,
    AVG(tr.execution_time) as avg_execution_time,
    AVG(tr.original_size) as avg_result_size,
    AVG(pr.compression_ratio) as avg_compression_ratio,
    COUNT(CASE WHEN tr.status = 'completed' THEN 1 END) as successful_executions,
    COUNT(CASE WHEN tr.status = 'failed' THEN 1 END) as failed_executions,
    MAX(tr.created_at) as last_execution
FROM tool_results tr
LEFT JOIN processed_results pr ON tr.result_id = pr.result_id
GROUP BY tr.result_type, tr.tool_name;

-- 6. 目标扫描历史视图
CREATE OR REPLACE VIEW target_scan_history AS
SELECT
    tr.target,
    tr.target_hash,
    COUNT(DISTINCT tr.tool_name) as tools_used,
    COUNT(*) as total_scans,
    MIN(tr.created_at) as first_scan,
    MAX(tr.created_at) as last_scan,
    STRING_AGG(DISTINCT tr.tool_name, ', ' ORDER BY tr.tool_name) as tool_list
FROM tool_results tr
GROUP BY tr.target, tr.target_hash;

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_tool_results_target_hash ON tool_results(target_hash);
CREATE INDEX IF NOT EXISTS idx_tool_results_tool_name ON tool_results(tool_name);
CREATE INDEX IF NOT EXISTS idx_tool_results_result_type ON tool_results(result_type);
CREATE INDEX IF NOT EXISTS idx_tool_results_created_at ON tool_results(created_at);
CREATE INDEX IF NOT EXISTS idx_tool_results_status ON tool_results(status);

CREATE INDEX IF NOT EXISTS idx_processed_results_result_id ON processed_results(result_id);
CREATE INDEX IF NOT EXISTS idx_processed_results_truncated ON processed_results(truncated);

CREATE INDEX IF NOT EXISTS idx_scan_sessions_session_id ON scan_sessions(session_id);
CREATE INDEX IF NOT EXISTS idx_scan_sessions_target ON scan_sessions(target);
CREATE INDEX IF NOT EXISTS idx_scan_sessions_status ON scan_sessions(status);

CREATE INDEX IF NOT EXISTS idx_session_results_session_id ON session_results(session_id);
CREATE INDEX IF NOT EXISTS idx_session_results_result_id ON session_results(result_id);

-- 创建GIN索引用于JSONB字段搜索
CREATE INDEX IF NOT EXISTS idx_tool_results_metadata_gin ON tool_results USING GIN(metadata);
CREATE INDEX IF NOT EXISTS idx_processed_results_key_findings_gin ON processed_results USING GIN(key_findings);
CREATE INDEX IF NOT EXISTS idx_processed_results_statistics_gin ON processed_results USING GIN(statistics);

-- 数据清理函数 - 删除超过指定天数的旧结果
CREATE OR REPLACE FUNCTION cleanup_old_results(days_to_keep INTEGER DEFAULT 30)
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    -- 删除旧的结果数据
    WITH deleted_results AS (
        DELETE FROM tool_results
        WHERE created_at < (CURRENT_TIMESTAMP - INTERVAL '1 day' * days_to_keep)
        RETURNING id
    )
    SELECT COUNT(*) INTO deleted_count FROM deleted_results;

    -- 记录清理操作
    INSERT INTO scan_sessions (session_id, target, session_type, status, metadata)
    VALUES (
        'cleanup_' || EXTRACT(epoch FROM CURRENT_TIMESTAMP)::TEXT,
        'system_maintenance',
        'cleanup',
        'completed',
        jsonb_build_object('deleted_count', deleted_count, 'days_to_keep', days_to_keep)
    );

    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- 结果压缩统计函数
CREATE OR REPLACE FUNCTION get_compression_stats()
RETURNS TABLE (
    result_type result_type_enum,
    tool_name VARCHAR(50),
    avg_original_size DECIMAL,
    avg_processed_size DECIMAL,
    avg_compression_ratio DECIMAL,
    total_space_saved BIGINT
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        tr.result_type,
        tr.tool_name,
        AVG(tr.original_size)::DECIMAL as avg_original_size,
        AVG(pr.processed_size)::DECIMAL as avg_processed_size,
        AVG(pr.compression_ratio)::DECIMAL as avg_compression_ratio,
        SUM(tr.original_size - pr.processed_size) as total_space_saved
    FROM tool_results tr
    JOIN processed_results pr ON tr.result_id = pr.result_id
    WHERE pr.truncated = true
    GROUP BY tr.result_type, tr.tool_name
    ORDER BY total_space_saved DESC;
END;
$$ LANGUAGE plpgsql;

-- 插入示例配置数据
INSERT INTO scan_sessions (session_id, target, session_type, status, metadata)
VALUES (
    'system_init',
    'system_initialization',
    'system',
    'completed',
    jsonb_build_object(
        'description', 'Database initialization',
        'version', '1.0.0',
        'features', ARRAY['result_storage', 'compression', 'cleanup']
    )
) ON CONFLICT (session_id) DO NOTHING;

-- 创建用于监控的视图
CREATE OR REPLACE VIEW system_health AS
SELECT
    COUNT(*) as total_results,
    COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_results,
    COUNT(CASE WHEN status = 'failed' THEN 1 END) as failed_results,
    AVG(original_size) as avg_result_size,
    SUM(original_size) as total_storage_used,
    COUNT(DISTINCT target_hash) as unique_targets,
    COUNT(DISTINCT tool_name) as tools_used,
    MIN(created_at) as oldest_result,
    MAX(created_at) as newest_result
FROM tool_results;

-- 权限设置（根据实际需要调整）
-- GRANT SELECT, INSERT, UPDATE, DELETE ON ALL TABLES IN SCHEMA public TO websec_user;
-- GRANT USAGE, SELECT ON ALL SEQUENCES IN SCHEMA public TO websec_user;
-- GRANT EXECUTE ON ALL FUNCTIONS IN SCHEMA public TO websec_user;

COMMENT ON TABLE tool_results IS '存储工具执行的完整原始结果';
COMMENT ON TABLE processed_results IS '存储处理后的压缩结果和摘要信息';
COMMENT ON TABLE scan_sessions IS '管理扫描会话和批量操作';
COMMENT ON TABLE session_results IS '关联扫描会话和具体结果';

COMMENT ON FUNCTION cleanup_old_results IS '清理超过指定天数的旧扫描结果';
COMMENT ON FUNCTION get_compression_stats IS '获取结果压缩统计信息';

-- 6. MCP 工具调用记录表 - 记录每次工具调用的详细信息
CREATE TABLE IF NOT EXISTS mcp_tool_calls (
    id SERIAL PRIMARY KEY,
    call_id VARCHAR(100) UNIQUE NOT NULL,
    tool_name VARCHAR(50) NOT NULL,
    function_name VARCHAR(100) NOT NULL,
    call_parameters JSONB NOT NULL DEFAULT '{}',
    call_result TEXT,
    result_size INTEGER DEFAULT 0,
    execution_time_ms INTEGER, -- 执行时间（毫秒）
    status VARCHAR(20) DEFAULT 'pending', -- pending, success, error, timeout
    error_message TEXT,
    start_time TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    end_time TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    metadata JSONB DEFAULT '{}',
    -- 关联到原有的 tool_results 表
    result_id VARCHAR(100) REFERENCES tool_results(result_id) ON DELETE SET NULL
);

-- 7. MCP 调用统计视图
CREATE OR REPLACE VIEW mcp_call_statistics AS
SELECT
    tool_name,
    function_name,
    COUNT(*) as total_calls,
    COUNT(CASE WHEN status = 'success' THEN 1 END) as successful_calls,
    COUNT(CASE WHEN status = 'error' THEN 1 END) as failed_calls,
    COUNT(CASE WHEN status = 'timeout' THEN 1 END) as timeout_calls,
    AVG(execution_time_ms) as avg_execution_time_ms,
    AVG(result_size) as avg_result_size,
    MIN(created_at) as first_call,
    MAX(created_at) as last_call
FROM mcp_tool_calls
GROUP BY tool_name, function_name
ORDER BY total_calls DESC;

-- 8. 参数使用频率统计视图
CREATE OR REPLACE VIEW parameter_usage_stats AS
SELECT
    tool_name,
    function_name,
    jsonb_object_keys(call_parameters) as parameter_name,
    COUNT(*) as usage_count,
    COUNT(DISTINCT call_parameters->jsonb_object_keys(call_parameters)) as unique_values
FROM mcp_tool_calls
WHERE call_parameters != '{}'::jsonb
GROUP BY tool_name, function_name, jsonb_object_keys(call_parameters)
ORDER BY usage_count DESC;

-- 创建 MCP 调用相关索引
CREATE INDEX IF NOT EXISTS idx_mcp_tool_calls_call_id ON mcp_tool_calls(call_id);
CREATE INDEX IF NOT EXISTS idx_mcp_tool_calls_tool_name ON mcp_tool_calls(tool_name);
CREATE INDEX IF NOT EXISTS idx_mcp_tool_calls_function_name ON mcp_tool_calls(function_name);
CREATE INDEX IF NOT EXISTS idx_mcp_tool_calls_status ON mcp_tool_calls(status);
CREATE INDEX IF NOT EXISTS idx_mcp_tool_calls_created_at ON mcp_tool_calls(created_at);
CREATE INDEX IF NOT EXISTS idx_mcp_tool_calls_result_id ON mcp_tool_calls(result_id);

-- 创建GIN索引用于参数搜索
CREATE INDEX IF NOT EXISTS idx_mcp_tool_calls_parameters_gin ON mcp_tool_calls USING GIN(call_parameters);
CREATE INDEX IF NOT EXISTS idx_mcp_tool_calls_metadata_gin ON mcp_tool_calls USING GIN(metadata);

-- MCP 调用清理函数
CREATE OR REPLACE FUNCTION cleanup_old_mcp_calls(days_to_keep INTEGER DEFAULT 30)
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    -- 删除旧的 MCP 调用记录
    WITH deleted_calls AS (
        DELETE FROM mcp_tool_calls
        WHERE created_at < (CURRENT_TIMESTAMP - INTERVAL '1 day' * days_to_keep)
        RETURNING id
    )
    SELECT COUNT(*) INTO deleted_count FROM deleted_calls;

    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- 完成初始化
SELECT 'WebSec MCP Result Storage Database with MCP Call Tracking initialized successfully!' as status;
