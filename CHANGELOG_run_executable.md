# run_executable 函数重构说明

## 修改概述

根据需求重构了 `utils/exec.py` 中的 `run_executable` 函数，使其能够根据系统环境自动选择合适的可执行文件版本。

## 主要变更

### 1. 函数签名变更
**之前：**
```python
def run_executable(executable_path, *args):
```

**现在：**
```python
def run_executable(cmd):
```

### 2. 参数格式变更
**之前：** 使用可变参数
```python
run_executable('nuclei', '-target', 'http://example.com')
```

**现在：** 使用命令参数列表
```python
run_executable(['nuclei', '-target', 'http://example.com'])
```

### 3. 核心功能实现

#### 自动系统检测
- 自动检测操作系统类型（Linux、macOS、Windows）
- 自动检测CPU架构（amd64、arm64）
- 在 `bin/{os}/{arch}/` 目录下查找对应版本的可执行文件

#### 错误处理优化
- **文件不存在**：返回 "工具不存在错误: 未找到适用于 {os}/{arch} 平台的 {tool} 工具"
- **执行失败**：返回 "执行失败: {具体错误信息}"
- **参数错误**：返回 "Error: cmd 参数必须是非空的命令参数列表"

### 4. 更新的文件列表

以下文件的 `run_executable` 调用已更新：

- `tools/netexec.py` - 网络服务利用工具
- `tools/webleak.py` - Web应用漏洞扫描器
- `tools/tlsx.py` - TLS/SSL配置分析器
- `tools/fingerprintx.py` - 服务指纹识别工具
- `tools/httpx.py` - HTTP探测分析工具
- `tools/nuclei.py` - Nuclei安全扫描器
- `tools/spray.py` - Web目录爆破工具
- `tools/katana.py` - 高级Web爬虫
- `tools/rustscan.py` - 超快端口扫描器
- `tools/crack.py` - 网络服务暴力破解
- `tools/subdomain/subfinder.py` - 子域名枚举工具

### 5. 测试验证

✅ **平台检测正常**：正确检测到 macOS (darwin) 和 arm64 架构
✅ **工具发现正常**：在 `bin/darwin/arm64/` 目录下找到了13个可用工具
✅ **错误处理正确**：空参数、非列表参数、不存在工具都返回明确错误信息
✅ **成功执行正常**：能够正确执行工具并返回输出

## 使用示例

### 基本用法
```python
from utils.exec import run_executable

# 执行 nuclei 扫描
result = run_executable(['nuclei', '-target', 'http://example.com'])

# 执行端口扫描
result = run_executable(['rustscan', '-a', '192.168.1.1', '-p', '22,80,443'])

# 执行目录爆破
result = run_executable(['spray', '-u', 'http://example.com'])
```

### 错误处理
```python
# 工具不存在
result = run_executable(['nonexistent_tool'])
# 返回: "工具不存在错误: 未找到适用于 darwin/arm64 平台的 nonexistent_tool 工具"

# 参数错误
result = run_executable([])
# 返回: "Error: cmd 参数必须是非空的命令参数列表"
```

## 兼容性说明

- ✅ **向前兼容**：所有现有工具调用已更新，无需额外修改
- ✅ **跨平台支持**：支持 Linux、macOS、Windows 系统
- ✅ **多架构支持**：支持 amd64、arm64 架构
- ✅ **错误处理**：明确区分"文件不存在"和"执行失败"两种情况

## 技术细节

### 平台管理器集成
函数使用 `PlatformManager` 类进行：
- 系统平台检测
- 可执行文件路径解析
- 工具可用性验证

### 执行流程
1. 验证 `cmd` 参数格式
2. 提取工具名称和参数
3. 使用平台管理器查找工具路径
4. 构建完整命令并执行
5. 处理执行结果和错误

### 超时设置
- 默认超时时间：300秒（5分钟）
- 超时后返回明确的超时错误信息
